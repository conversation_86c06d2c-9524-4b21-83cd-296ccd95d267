(()=>{var e={};e.id=8733,e.ids=[1391,3772,8733],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},2656:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=a(65239),s=a(48088),n=a(88170),i=a.n(n),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let d={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,52031)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\users\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\users\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6953:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(60687),s=a(43210),n=a(85814),i=a.n(n),o=a(87979),l=a(91391),d=a(3582),c=a(83475),u=a(77567);function p(){let{user:e,loading:t,isAdmin:n}=(0,o.wC)(),[p,x]=(0,s.useState)([]),[g,m]=(0,s.useState)(!0),[y,h]=(0,s.useState)(""),[f,b]=(0,s.useState)(!1),[v,D]=(0,s.useState)(0),[w,j]=(0,s.useState)(null),[N,T]=(0,s.useState)(!1),[k,C]=(0,s.useState)({name:"",email:"",mobile:"",referralCode:"",referredBy:"",plan:"",activeDays:0,totalTranslations:0,todayTranslations:0,wallet:0,status:"active",quickTranslationAdvantage:!1,quickTranslationAdvantageDays:1,quickTranslationAdvantageSeconds:30}),[A,S]=(0,s.useState)(!1),[q,E]=(0,s.useState)(1),[P,L]=(0,s.useState)(!0),[U,O]=(0,s.useState)(null),B=async(e=!0)=>{try{m(!0);let t=await (0,l.lo)(50,e?null:U);if(e){x(t.users),E(1);try{let e=await (0,l.nQ)();D(e)}catch(e){console.error("Error getting total user count:",e)}}else x(e=>[...e,...t.users]);O(t.lastDoc),L(t.hasMore)}catch(e){console.error("Error loading users:",e),u.A.fire({icon:"error",title:"Error",text:"Failed to load users. Please try again."})}finally{m(!1)}},I=async()=>{if(!y.trim())return void B();try{b(!0);let e=await (0,l.searchUsers)(y.trim());x(e),L(!1)}catch(e){console.error("Error searching users:",e),u.A.fire({icon:"error",title:"Search Failed",text:"Failed to search users. Please try again."})}finally{b(!1)}},$=async e=>{j(e);let t=!1;try{let{checkCopyPastePermission:r}=await Promise.resolve().then(a.bind(a,27878));t=(await r(e.id)).hasPermission,console.log(`📋 Current copy-paste status for ${e.name}: ${t}`)}catch(a){console.error("Error checking copy-paste status:",a),t=e.quickTranslationAdvantage||!1}C({name:e.name,email:e.email,mobile:e.mobile,referralCode:e.referralCode,referredBy:e.referredBy,plan:e.plan,activeDays:e.activeDays,totalTranslations:e.totalTranslations,todayTranslations:e.todayTranslations,wallet:e.wallet||0,status:e.status,quickTranslationAdvantage:t,quickTranslationAdvantageDays:e.quickTranslationAdvantageDays||1,quickTranslationAdvantageSeconds:e.quickTranslationAdvantageSeconds||30}),T(!0)},M=async()=>{if(w)try{S(!0);let t=w.plan,r=k.plan,s=t!==r,n={name:k.name,email:k.email,mobile:k.mobile,referralCode:k.referralCode,referredBy:k.referredBy,plan:k.plan,activeDays:k.activeDays,totalTranslations:k.totalTranslations,todayTranslations:k.todayTranslations,wallet:k.wallet,status:k.status};await (0,l.TK)(w.id,n),console.log("\uD83D\uDD27 Processing copy-paste permission changes..."),console.log(`   - Checkbox checked: ${k.quickTranslationAdvantage}`),console.log(`   - User ID: ${w.id}`);let{checkCopyPastePermission:i}=await Promise.resolve().then(a.bind(a,27878)),o=await i(w.id),c=o.hasPermission;if(console.log(`   - Current has permission: ${c}`),console.log(`   - Days remaining: ${o.daysRemaining}`),k.quickTranslationAdvantage&&!c?(console.log(`✅ Granting copy-paste permission for ${k.quickTranslationAdvantageDays} days`),await (0,d.w1)(w.id,k.quickTranslationAdvantageDays,e?.email||"admin",k.quickTranslationAdvantageSeconds)):!k.quickTranslationAdvantage&&c?(console.log(`❌ Removing copy-paste permission`),await (0,d.wT)(w.id,e?.email||"admin")):k.quickTranslationAdvantage&&c?(console.log(`🔄 Updating copy-paste permission for ${k.quickTranslationAdvantageDays} days`),await (0,d.wT)(w.id,e?.email||"admin"),await (0,d.w1)(w.id,k.quickTranslationAdvantageDays,e?.email||"admin",k.quickTranslationAdvantageSeconds)):console.log(`⏭️ No copy-paste permission changes needed`),s)try{await (0,d.II)(w.id,r),console.log(`Updated plan expiry for user ${w.id}: ${t} -> ${r}`)}catch(e){console.error("Error updating plan expiry:",e)}if(s&&"Trial"===t&&"Trial"!==r)try{console.log(`Processing referral bonus for user ${w.id}: ${t} -> ${r}`),await (0,d.IK)(w.id,t,r),u.A.fire({icon:"success",title:"User Updated & Referral Bonus Processed",html:`
              <div class="text-left">
                <p><strong>User plan updated:</strong> ${t} → ${r}</p>
                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>
              </div>
            `,timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error processing referral bonus:",e),u.A.fire({icon:"warning",title:"User Updated (Referral Bonus Issue)",html:`
              <div class="text-left">
                <p><strong>User plan updated successfully:</strong> ${t} → ${r}</p>
                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>
                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>
              </div>
            `,timer:5e3,showConfirmButton:!1})}else{let e="User information has been updated successfully";k.quickTranslationAdvantage&&!c?e+=`. Copy-paste permission granted for ${k.quickTranslationAdvantageDays} days.`:!k.quickTranslationAdvantage&&c?e+=". Copy-paste permission removed.":k.quickTranslationAdvantage&&c&&(e+=`. Copy-paste permission updated for ${k.quickTranslationAdvantageDays} days.`),u.A.fire({icon:"success",title:"User Updated",text:e,timer:3e3,showConfirmButton:!1})}let p=await i(w.id);console.log(`🔄 Refreshed copy-paste status: ${p.hasPermission}`),x(e=>e.map(e=>e.id===w.id?{...e,...n,quickTranslationAdvantage:p.hasPermission,quickTranslationAdvantageDays:k.quickTranslationAdvantageDays,quickTranslationAdvantageSeconds:k.quickTranslationAdvantageSeconds}:e)),C(e=>({...e,quickTranslationAdvantage:p.hasPermission}));try{let t=`copyPasteUpdate_${w.id}`,a={userId:w.id,hasPermission:p.hasPermission,timestamp:Date.now(),updatedBy:e?.email||"admin"};localStorage.setItem(t,JSON.stringify(a)),window.dispatchEvent(new CustomEvent("copyPastePermissionChanged",{detail:a})),console.log(`📡 Broadcasted copy-paste permission change for user ${w.id}`)}catch(e){console.error("Error broadcasting copy-paste permission change:",e)}setTimeout(()=>{T(!1)},500),j(null),await B()}catch(e){console.error("Error updating user:",e),u.A.fire({icon:"error",title:"Update Failed",text:"Failed to update user. Please try again."})}finally{S(!1)}},R=async e=>{if((await u.A.fire({icon:"warning",title:"Delete User",text:`Are you sure you want to delete ${e.name}? This action cannot be undone.`,showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,l.hG)(e.id),x(t=>t.filter(t=>t.id!==e.id)),u.A.fire({icon:"success",title:"User Deleted",text:"User has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting user:",e),u.A.fire({icon:"error",title:"Delete Failed",text:"Failed to delete user. Please try again."})}},Y=e=>null==e||isNaN(e)?"₹0.00":`₹${e.toFixed(2)}`,_=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},V=e=>{switch(e){case"active":return"bg-green-500";case"inactive":return"bg-red-500";case"suspended":return"bg-yellow-500";default:return"bg-gray-500"}},F=async()=>{try{u.A.fire({title:"Exporting Users...",text:"Please wait while we prepare your export file.",allowOutsideClick:!1,didOpen:()=>{u.A.showLoading()}});let e=await (0,l.CF)();if(0===e.length)return void u.A.fire({icon:"warning",title:"No Data",text:"No users to export."});console.log(`📊 Exporting ${e.length} users with copy-paste data...`),e.slice(0,3).forEach(e=>{console.log(`📊 Sample user ${e.email} copy-paste data:`,{quickTranslationAdvantageExpiry:e.quickTranslationAdvantageExpiry,quickVideoAdvantageExpiry:e.quickVideoAdvantageExpiry,hasExpiry:!!(e.quickTranslationAdvantageExpiry||e.quickVideoAdvantageExpiry)})});let t=(0,c.Fz)(e);(0,c.Bf)(t,"users"),u.A.fire({icon:"success",title:"Export Complete",text:`Exported ${e.length} users to CSV file. Check console for copy-paste data details.`,timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error exporting users:",e),u.A.fire({icon:"error",title:"Export Failed",text:"Failed to export users. Please try again."})}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)(i(),{href:"/admin",className:"text-gray-600 hover:text-gray-800",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),v>0&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:y?`Showing ${p.length} of ${v} users`:`Total: ${v} users`})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i(),{href:"/admin/upload-users",className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]}),(0,r.jsxs)("button",{onClick:F,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:()=>B(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("input",{type:"text",value:y,onChange:e=>h(e.target.value),placeholder:"Search by name, email, mobile, or referral code...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyDown:e=>"Enter"===e.key&&I()}),(0,r.jsx)("button",{onClick:I,disabled:f,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Searching..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-search mr-2"}),"Search"]})}),y&&(0,r.jsx)("button",{onClick:()=>{h(""),B()},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times"})})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Translations"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quick Copy-Paste"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g&&0===p.length?(0,r.jsx)("tr",{children:(0,r.jsxs)("td",{colSpan:8,className:"px-6 py-4 text-center",children:[(0,r.jsx)("div",{className:"spinner mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading users..."})]})}):0===p.length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:8,className:"px-6 py-4 text-center text-gray-500",children:"No users found"})}):p.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined: ",e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():new Date(e.joinedDate).toLocaleDateString()]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.mobile}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Code: ",e.referralCode]}),e.referredBy&&(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Ref: ",e.referredBy]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-semibold rounded-full text-white ${_(e.plan)}`,children:e.plan}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Days: ",e.activeDays]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.totalTranslations]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Today: ",e.todayTranslations]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.quickTranslationAdvantage&&e.quickTranslationAdvantageExpiry&&new Date<e.quickTranslationAdvantageExpiry?(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500",children:"Enabled"}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Until: ",e.quickTranslationAdvantageExpiry instanceof Date?e.quickTranslationAdvantageExpiry.toLocaleDateString():new Date(e.quickTranslationAdvantageExpiry).toLocaleDateString()]})]}):(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500",children:"Disabled"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsx)("i",{className:"fas fa-wallet mr-1 text-green-500"}),Y(e.wallet||0)]}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Total Balance"})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-semibold rounded-full text-white ${V(e.status)}`,children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>$(e),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:(0,r.jsx)("i",{className:"fas fa-edit"})}),(0,r.jsx)("button",{onClick:()=>R(e),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,r.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),P&&!g&&p.length>0&&(0,r.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 text-center",children:(0,r.jsxs)("button",{onClick:()=>{P&&!g&&B(!1)},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:[(0,r.jsx)("i",{className:"fas fa-chevron-down mr-2"}),"Load More Users"]})})]})}),N&&w&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 flex-shrink-0",children:(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Edit User"})}),(0,r.jsxs)("div",{className:"p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,r.jsx)("input",{type:"text",value:k.name,onChange:e=>C(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,r.jsx)("input",{type:"email",value:k.email,onChange:e=>C(t=>({...t,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile"}),(0,r.jsx)("input",{type:"text",value:k.mobile,onChange:e=>C(t=>({...t,mobile:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Code"}),(0,r.jsx)("input",{type:"text",value:k.referralCode,onChange:e=>C(t=>({...t,referralCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referred By"}),(0,r.jsx)("input",{type:"text",value:k.referredBy,onChange:e=>C(t=>({...t,referredBy:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,r.jsxs)("select",{value:k.plan,onChange:e=>C(t=>({...t,plan:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"Trial",children:"Trial"}),(0,r.jsx)("option",{value:"Junior",children:"Junior"}),(0,r.jsx)("option",{value:"Senior",children:"Senior"}),(0,r.jsx)("option",{value:"Expert",children:"Expert"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Active Days"}),(0,r.jsx)("input",{type:"number",value:k.activeDays,onChange:e=>C(t=>({...t,activeDays:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Translations"}),(0,r.jsx)("input",{type:"number",value:k.totalTranslations,onChange:e=>C(t=>({...t,totalTranslations:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Today Translations"}),(0,r.jsx)("input",{type:"number",value:k.todayTranslations,onChange:e=>C(t=>({...t,todayTranslations:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wallet Balance (₹)"}),(0,r.jsx)("input",{type:"number",step:"0.01",value:k.wallet,onChange:e=>C(t=>({...t,wallet:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("select",{value:k.status,onChange:e=>C(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"active",children:"Active"}),(0,r.jsx)("option",{value:"inactive",children:"Inactive"}),(0,r.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,r.jsxs)("h4",{className:"text-md font-semibold text-gray-900 mb-3",children:[(0,r.jsx)("i",{className:"fas fa-copy mr-2 text-yellow-500"}),"Quick Translation Copy-Paste Advantage"]}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mb-3",children:"When enabled, user can copy-paste English text instead of typing manually"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"quickTranslationAdvantage",checked:k.quickTranslationAdvantage,onChange:e=>C(t=>({...t,quickTranslationAdvantage:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"quickTranslationAdvantage",className:"ml-2 block text-sm text-gray-700",children:"Enable Copy-Paste for English Text"})]}),k.quickTranslationAdvantage&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 ml-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Days"}),(0,r.jsx)("input",{type:"number",min:"1",max:"365",value:k.quickTranslationAdvantageDays,onChange:e=>C(t=>({...t,quickTranslationAdvantageDays:parseInt(e.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Translation Duration"}),(0,r.jsxs)("select",{value:k.quickTranslationAdvantageSeconds,onChange:e=>C(t=>({...t,quickTranslationAdvantageSeconds:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:1,children:"1 second"}),(0,r.jsx)("option",{value:10,children:"10 seconds"}),(0,r.jsx)("option",{value:30,children:"30 seconds"})]})]})]}),w&&(0,r.jsx)("div",{className:"ml-6 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Current Status:"})," ",w.quickTranslationAdvantage&&w.quickTranslationAdvantageExpiry&&new Date<w.quickTranslationAdvantageExpiry?(0,r.jsxs)("span",{className:"text-green-600",children:["Copy-paste enabled until ",w.quickTranslationAdvantageExpiry instanceof Date?w.quickTranslationAdvantageExpiry.toLocaleDateString():new Date(w.quickTranslationAdvantageExpiry).toLocaleDateString()]}):(0,r.jsx)("span",{className:"text-gray-500",children:"Copy-paste disabled - manual typing required"})]})})]})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 flex-shrink-0",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("button",{onClick:M,disabled:A,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:A?"Saving...":"Save Changes"}),(0,r.jsx)("button",{onClick:()=>T(!1),className:"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700",children:"Cancel"})]})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},48555:(e,t,a)=>{Promise.resolve().then(a.bind(a,6953))},52031:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\users\\page.tsx","default")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54131:(e,t,a)=>{Promise.resolve().then(a.bind(a,52031))},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),s=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],n=new Blob(["\uFEFF"+[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";let r=s.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return`"${e}"`}return a instanceof Date?`"${a.toLocaleDateString()}"`:"object"==typeof a&&null!==a&&a.toDate?`"${a.toDate().toLocaleDateString()}"`:r&&("number"==typeof a||!isNaN(Number(a)))?`"${a}"`:"number"==typeof a?a.toString():`"${String(a)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(n);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function s(e){return e.map(t=>{let a=0,r=null,s="No",n=t.quickTranslationAdvantageExpiry||t.quickVideoAdvantageExpiry;if(n)try{n instanceof Date?r=n:n.toDate&&"function"==typeof n.toDate?r=n.toDate():r=new Date(n);let i=new Date,o=r.getTime()-i.getTime();s=(a=Math.max(0,Math.ceil(o/864e5)))>0?"Yes":"No",5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}:`,{expiryField:n,expiryFieldType:typeof n,copyPasteExpiryDate:r,copyPasteRemainingDays:a,copyPastePermission:s,hasQuickTranslationAdvantageExpiry:!!t.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!t.quickVideoAdvantageExpiry})}catch(e){console.error(`❌ Error calculating copy-paste days for user ${t.email}:`,e)}else 5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}: No copy-paste expiry field found`);return{"User ID":t.id||"",Name:t.name||"",Email:t.email||"",Mobile:String(t.mobile||""),"Referral Code":t.referralCode||"","Referred By":t.referredBy||"Direct",Plan:t.plan||"","Plan Expiry":t.planExpiry instanceof Date?t.planExpiry.toLocaleDateString():t.planExpiry?new Date(t.planExpiry).toLocaleDateString():"","Active Days":t.activeDays||0,"Total Translations":t.totalTranslations||t.totalVideos||0,"Today Translations":t.todayTranslations||t.todayVideos||0,"Last Translation Date":t.lastTranslationDate instanceof Date?t.lastTranslationDate.toLocaleDateString():t.lastTranslationDate?new Date(t.lastTranslationDate).toLocaleDateString():t.lastVideoDate instanceof Date?t.lastVideoDate.toLocaleDateString():t.lastVideoDate?new Date(t.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":s,"Copy-Paste Remaining Days":a,"Copy-Paste Expiry":r?r.toLocaleDateString():"","Copy-Paste Granted By":t.quickTranslationAdvantageGrantedBy||t.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":t.quickTranslationAdvantageGrantedAt?t.quickTranslationAdvantageGrantedAt instanceof Date?t.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(t.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":t.wallet||0,"Referral Bonus Credited":t.referralBonusCredited?"Yes":"No",Status:t.status||"","Joined Date":t.joinedDate instanceof Date?t.joinedDate.toLocaleDateString():t.joinedDate?new Date(t.joinedDate).toLocaleDateString():"","Joined Time":t.joinedDate instanceof Date?t.joinedDate.toLocaleTimeString():t.joinedDate?new Date(t.joinedDate).toLocaleTimeString():""}})}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>r,Fz:()=>s,Pe:()=>o,dB:()=>i,sL:()=>n})},91391:(e,t,a)=>{"use strict";a.d(t,{CF:()=>c,Pn:()=>o,TK:()=>x,getWithdrawals:()=>p,hG:()=>g,lo:()=>l,nQ:()=>u,searchUsers:()=>d,updateWithdrawalStatus:()=>m});var r=a(75535),s=a(33784),n=a(3582);let i=new Map;async function o(){let e="dashboard-stats",t=function(e){let t=i.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=r.Dc.fromDate(t),o=await (0,r.getDocs)((0,r.collection)(s.db,n.COLLECTIONS.users)),l=o.size,d=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.users),(0,r._M)(n.Yr.joinedDate,">=",a)),c=(await (0,r.getDocs)(d)).size,u=0,p=0,x=0,g=0;o.forEach(e=>{let a=e.data(),r=Number(a[n.Yr.totalTranslations])||0,s=Number(a[n.Yr.wallet])||0;u+=r,p+=s;let i=a[n.Yr.lastTranslationDate]?.toDate();if(i&&i.toDateString()===t.toDateString()){let e=Number(a[n.Yr.todayTranslations])||0;x+=e}});try{let e=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.transactions),(0,r._M)(n.Yr.type,"==","translation_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(e)).forEach(e=>{let a=e.data(),r=a[n.Yr.date]?.toDate();if(r&&r>=t){let e=Number(a[n.Yr.amount])||0;g+=e}})}catch(e){console.warn("Could not fetch today's transactions:",e)}let m=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),y=(await (0,r.getDocs)(m)).size,h=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.withdrawals),(0,r._M)("date",">=",a)),f=(await (0,r.getDocs)(h)).size,b={totalUsers:Number(l)||0,totalTranslations:Number(u)||0,totalEarnings:Number(p)||0,pendingWithdrawals:Number(y)||0,todayUsers:Number(c)||0,todayTranslations:Number(x)||0,todayEarnings:Number(g)||0,todayWithdrawals:Number(f)||0};return i.set(e,{data:b,timestamp:Date.now()}),b}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let a=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.users),(0,r.My)(n.Yr.joinedDate,"desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.users),(0,r.My)(n.Yr.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let i=await (0,r.getDocs)(a);return{users:i.docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[n.Yr.joinedDate]?.toDate(),planExpiry:t[n.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.users),(0,r.My)(n.Yr.joinedDate,"desc"));return(await (0,r.getDocs)(a)).docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[n.Yr.joinedDate]?.toDate(),planExpiry:t[n.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}}).filter(e=>{let a=String(e[n.Yr.name]||"").toLowerCase(),r=String(e[n.Yr.email]||"").toLowerCase(),s=String(e[n.Yr.mobile]||"").toLowerCase(),i=String(e[n.Yr.referralCode]||"").toLowerCase();return a.includes(t)||r.includes(t)||s.includes(t)||i.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.users),(0,r.My)(n.Yr.joinedDate,"desc"));return(await (0,r.getDocs)(e)).docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[n.Yr.joinedDate]?.toDate(),planExpiry:t[n.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.users));return(await (0,r.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function p(e=50,t=null){try{let a=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(s.db,n.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(t),(0,r.AB)(e)));let i=await (0,r.getDocs)(a);return{withdrawals:i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()})),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function x(e,t){try{await (0,r.mZ)((0,r.H9)(s.db,n.COLLECTIONS.users,e),t),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function g(e){try{await (0,r.kd)((0,r.H9)(s.db,n.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function m(e,t,o){try{let l=await (0,r.x7)((0,r.H9)(s.db,n.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=l.data(),p={status:t,updatedAt:r.Dc.now()};if(o&&(p.adminNotes=o),await (0,r.mZ)((0,r.H9)(s.db,n.COLLECTIONS.withdrawals,e),p),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3582));await e(d,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3582));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[2579,6803,3582],()=>a(2656));module.exports=r})();