"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/work/page",{

/***/ "(app-pages-browser)/./src/app/work/page.tsx":
/*!*******************************!*\
  !*** ./src/app/work/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useBlockingNotifications */ \"(app-pages-browser)/./src/hooks/useBlockingNotifications.ts\");\n/* harmony import */ var _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useLeaveMonitor */ \"(app-pages-browser)/./src/hooks/useLeaveMonitor.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n/* harmony import */ var _lib_leaveService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\");\n/* harmony import */ var _lib_translationManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/translationManager */ \"(app-pages-browser)/./src/lib/translationManager.ts\");\n/* harmony import */ var _lib_copyPasteService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/copyPasteService */ \"(app-pages-browser)/./src/lib/copyPasteService.ts\");\n/* harmony import */ var _lib_sessionManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/sessionManager */ \"(app-pages-browser)/./src/lib/sessionManager.ts\");\n/* harmony import */ var _components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/BlockingNotificationModal */ \"(app-pages-browser)/./src/components/BlockingNotificationModal.tsx\");\n/* harmony import */ var _components_SessionRecovery__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/SessionRecovery */ \"(app-pages-browser)/./src/components/SessionRecovery.tsx\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_13__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WorkPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth)();\n    const { hasBlockingNotifications, isChecking, markAllAsRead } = (0,_hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications)((user === null || user === void 0 ? void 0 : user.uid) || null);\n    const { isBlocked: isLeaveBlocked, leaveStatus } = (0,_hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor)({\n        userId: (user === null || user === void 0 ? void 0 : user.uid) || null,\n        checkInterval: 30000,\n        enabled: !!user\n    });\n    // Debug logging\n    console.log('WorkPage render:', {\n        user: user === null || user === void 0 ? void 0 : user.uid,\n        loading,\n        hasBlockingNotifications,\n        isChecking,\n        isLeaveBlocked\n    });\n    // New Translation Workflow State\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [todayTranslations, setTodayTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalTranslationsCompleted, setTotalTranslationsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [localTranslationCount, setLocalTranslationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [canSubmitBatch, setCanSubmitBatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationItems, setTranslationItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingTranslations, setIsLoadingTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Typing validation state\n    const [userTypedText, setUserTypedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previousValidText, setPreviousValidText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('') // Track previous valid state for paste reversion\n    ;\n    const [typingErrors, setTypingErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isTypingComplete, setIsTypingComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasQuickAdvantage, setHasQuickAdvantage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copyPasteDaysRemaining, setCopyPasteDaysRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Language selection state\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLanguageCorrect, setIsLanguageCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTranslation, setShowTranslation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Paste detection state\n    const [lastInputTime, setLastInputTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pasteDetected, setPasteDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastInputLength, setLastInputLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [typingBlocked, setTypingBlocked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [blockReason, setBlockReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Auto-save state\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAutoSaving, setIsAutoSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Session recovery state\n    const [showSessionRecovery, setShowSessionRecovery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionRecoveryComplete, setSessionRecoveryComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Daily completion state\n    const [isDailyCompleted, setIsDailyCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFinalSubmit, setShowFinalSubmit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationSettings, setTranslationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        earningPerBatch: 25,\n        plan: 'Trial'\n    });\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [daysLeft, setDaysLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeDays, setActiveDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            if (user && !sessionRecoveryComplete) {\n                // Only show session recovery if we haven't completed it yet\n                setShowSessionRecovery(true);\n                // Add fallback timeout to prevent indefinite blocking\n                const fallbackTimeout = setTimeout({\n                    \"WorkPage.useEffect.fallbackTimeout\": ()=>{\n                        console.log('Session recovery timeout - proceeding without recovery');\n                        setSessionRecoveryComplete(true);\n                        setShowSessionRecovery(false);\n                    }\n                }[\"WorkPage.useEffect.fallbackTimeout\"], 15000) // 15 second timeout\n                ;\n                return ({\n                    \"WorkPage.useEffect\": ()=>clearTimeout(fallbackTimeout)\n                })[\"WorkPage.useEffect\"];\n            } else if (user && sessionRecoveryComplete) {\n                // If user is logged in and recovery is complete, proceed with work access\n                checkWorkAccess();\n            }\n        }\n    }[\"WorkPage.useEffect\"], [\n        user,\n        sessionRecoveryComplete\n    ]);\n    const handleSessionRecoveryComplete = ()=>{\n        setSessionRecoveryComplete(true);\n        setShowSessionRecovery(false);\n    // Don't call checkWorkAccess here - it will be called by the useEffect above\n    };\n    const handleProgressRestored = (progress)=>{\n        // Restore the work state from recovered progress\n        setCurrentStep(progress.currentStep);\n        setUserTypedText(progress.userTypedText);\n        setPreviousValidText(progress.userTypedText) // Set previous valid text to current restored text\n        ;\n        setSelectedLanguage(progress.selectedLanguage);\n        setIsTypingComplete(progress.isTypingComplete);\n        setLocalTranslationCount(progress.completedTranslations);\n        setLastSaved(new Date(progress.lastSaved));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            setCanSubmitBatch(localTranslationCount >= 50);\n            setShowFinalSubmit(localTranslationCount >= 50 && !isDailyCompleted);\n        }\n    }[\"WorkPage.useEffect\"], [\n        localTranslationCount,\n        isDailyCompleted\n    ]);\n    // Auto-save work progress every 10 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            if (!user || !currentStep) return;\n            const autoSaveInterval = setInterval({\n                \"WorkPage.useEffect.autoSaveInterval\": ()=>{\n                    saveWorkProgress();\n                }\n            }[\"WorkPage.useEffect.autoSaveInterval\"], 10000) // Save every 10 seconds\n            ;\n            return ({\n                \"WorkPage.useEffect\": ()=>clearInterval(autoSaveInterval)\n            })[\"WorkPage.useEffect\"];\n        }\n    }[\"WorkPage.useEffect\"], [\n        user,\n        currentStep,\n        userTypedText,\n        selectedLanguage,\n        isTypingComplete,\n        localTranslationCount\n    ]);\n    // Save progress when user types\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            if (user && currentStep && userTypedText) {\n                const debounceTimer = setTimeout({\n                    \"WorkPage.useEffect.debounceTimer\": ()=>{\n                        saveWorkProgress();\n                    }\n                }[\"WorkPage.useEffect.debounceTimer\"], 2000) // Save 2 seconds after user stops typing\n                ;\n                return ({\n                    \"WorkPage.useEffect\": ()=>clearTimeout(debounceTimer)\n                })[\"WorkPage.useEffect\"];\n            }\n        }\n    }[\"WorkPage.useEffect\"], [\n        userTypedText\n    ]);\n    // Save progress before page unload\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"WorkPage.useEffect.handleBeforeUnload\": (e)=>{\n                    if (user && currentStep && (userTypedText || localTranslationCount > 0)) {\n                        saveWorkProgress();\n                        e.preventDefault();\n                        e.returnValue = 'You have unsaved work progress. Are you sure you want to leave?';\n                        return e.returnValue;\n                    }\n                }\n            }[\"WorkPage.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"WorkPage.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"WorkPage.useEffect\"];\n        }\n    }[\"WorkPage.useEffect\"], [\n        user,\n        currentStep,\n        userTypedText,\n        localTranslationCount\n    ]);\n    // Listen for real-time copy-paste permission changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            if (!(user === null || user === void 0 ? void 0 : user.uid)) return;\n            const handleCopyPastePermissionChange = {\n                \"WorkPage.useEffect.handleCopyPastePermissionChange\": (event)=>{\n                    const { userId, hasPermission, timestamp, updatedBy } = event.detail;\n                    if (userId === user.uid) {\n                        console.log(\"\\uD83D\\uDCE1 Received copy-paste permission update: \".concat(hasPermission, \" (by \").concat(updatedBy, \")\"));\n                        setHasQuickAdvantage(hasPermission);\n                        // Show notification to user\n                        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                            icon: hasPermission ? 'success' : 'warning',\n                            title: hasPermission ? 'Copy-Paste Enabled!' : 'Copy-Paste Disabled!',\n                            text: hasPermission ? 'Copy-paste permission has been enabled by admin. You can now copy and paste text.' : 'Copy-paste permission has been disabled by admin. Please type text manually.',\n                            timer: 4000,\n                            showConfirmButton: true,\n                            confirmButtonText: 'OK'\n                        });\n                    }\n                }\n            }[\"WorkPage.useEffect.handleCopyPastePermissionChange\"];\n            // Check localStorage for updates (in case event was missed)\n            const checkForUpdates = {\n                \"WorkPage.useEffect.checkForUpdates\": ()=>{\n                    try {\n                        const updateKey = \"copyPasteUpdate_\".concat(user.uid);\n                        const updateData = localStorage.getItem(updateKey);\n                        if (updateData) {\n                            const parsed = JSON.parse(updateData);\n                            const timeDiff = Date.now() - parsed.timestamp;\n                            // If update is less than 30 seconds old, apply it\n                            if (timeDiff < 30000) {\n                                console.log(\"\\uD83D\\uDCE1 Found recent copy-paste update in localStorage: \".concat(parsed.hasPermission));\n                                setHasQuickAdvantage(parsed.hasPermission);\n                                // Show notification\n                                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                    icon: parsed.hasPermission ? 'success' : 'warning',\n                                    title: parsed.hasPermission ? 'Copy-Paste Enabled!' : 'Copy-Paste Disabled!',\n                                    text: parsed.hasPermission ? 'Copy-paste permission has been enabled by admin.' : 'Copy-paste permission has been disabled by admin.',\n                                    timer: 3000,\n                                    showConfirmButton: false\n                                });\n                                // Clear the update after applying\n                                localStorage.removeItem(updateKey);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error checking for copy-paste updates:', error);\n                    }\n                }\n            }[\"WorkPage.useEffect.checkForUpdates\"];\n            // Listen for custom events\n            window.addEventListener('copyPastePermissionChanged', handleCopyPastePermissionChange);\n            // Check for updates immediately and every 5 seconds\n            checkForUpdates();\n            const interval = setInterval(checkForUpdates, 5000);\n            return ({\n                \"WorkPage.useEffect\": ()=>{\n                    window.removeEventListener('copyPastePermissionChanged', handleCopyPastePermissionChange);\n                    clearInterval(interval);\n                }\n            })[\"WorkPage.useEffect\"];\n        }\n    }[\"WorkPage.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.uid\n    ]);\n    const checkWorkAccess = async ()=>{\n        try {\n            console.log('🔍 Checking work access for user:', user.uid);\n            // Check plan expiry first\n            const planStatus = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.isUserPlanExpired)(user.uid);\n            console.log('📅 Plan status result:', planStatus);\n            if (planStatus.expired) {\n                console.log('🚫 Work access blocked - Plan expired:', planStatus.reason);\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'error',\n                    title: 'Plan Expired',\n                    html: '\\n            <div class=\"text-center\">\\n              <p class=\"mb-3\">'.concat(planStatus.reason, '</p>\\n              <p class=\"text-sm text-gray-600\">\\n                Active Days: ').concat(planStatus.activeDays || 0, \" | Days Left: \").concat(planStatus.daysLeft || 0, \"\\n              </p>\\n            </div>\\n          \"),\n                    confirmButtonText: 'Upgrade Plan',\n                    showCancelButton: true,\n                    cancelButtonText: 'Go to Dashboard'\n                }).then((result)=>{\n                    if (result.isConfirmed) {\n                        window.location.href = '/plans';\n                    } else {\n                        window.location.href = '/dashboard';\n                    }\n                });\n                return;\n            }\n            // Check if user has already completed their daily session (50 translations)\n            const translationData = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getVideoCountData)(user.uid);\n            console.log('📊 Translation data check:', translationData);\n            if (translationData.todayTranslations >= 50) {\n                console.log('🚫 Work access blocked - Daily session completed');\n                setIsDailyCompleted(true);\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'info',\n                    title: 'Daily Session Completed',\n                    html: '\\n            <div class=\"text-center\">\\n              <p class=\"mb-3\">You have already completed your daily session of 50 translations!</p>\\n              <p class=\"text-sm text-gray-600\">\\n                Translations completed today: '.concat(translationData.todayTranslations, '/50\\n              </p>\\n              <p class=\"text-sm text-green-600 mt-2\">\\n                Come back tomorrow for your next session.\\n              </p>\\n            </div>\\n          '),\n                    confirmButtonText: 'Go to Dashboard',\n                    allowOutsideClick: false,\n                    allowEscapeKey: false\n                }).then(()=>{\n                    window.location.href = '/dashboard';\n                });\n                return;\n            }\n            const workStatus = await (0,_lib_leaveService__WEBPACK_IMPORTED_MODULE_7__.isWorkBlocked)(user.uid);\n            console.log('📊 Work status result:', workStatus);\n            if (workStatus.blocked) {\n                console.log('🚫 Work access blocked:', workStatus.reason);\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'warning',\n                    title: 'Work Not Available',\n                    text: workStatus.reason || 'Work is currently blocked.',\n                    confirmButtonText: 'Go to Dashboard'\n                }).then(()=>{\n                    window.location.href = '/dashboard';\n                });\n                return;\n            }\n            console.log('✅ Work access allowed, proceeding with fast loading');\n            await initializeWorkPageFast();\n        } catch (error) {\n            console.error('❌ Error checking work access (allowing work to proceed):', error);\n            await initializeWorkPageFast();\n        }\n    };\n    const loadTranslationData = async ()=>{\n        try {\n            console.log('📊 Loading translation data for user:', user.uid);\n            const data = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getVideoCountData)(user.uid);\n            console.log('📊 Translation data loaded:', data);\n            setTodayTranslations(data.todayTranslations);\n            setTotalTranslationsCompleted(data.totalTranslations);\n        } catch (error) {\n            console.error('Error loading translation data:', error);\n        }\n    };\n    const loadTranslationSettings = async ()=>{\n        try {\n            const settings = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserVideoSettings)(user.uid);\n            setTranslationSettings({\n                earningPerBatch: settings.earningPerBatch,\n                plan: settings.plan\n            });\n            // Check copy-paste permission using new service\n            const copyPastePermission = await (0,_lib_copyPasteService__WEBPACK_IMPORTED_MODULE_9__.checkCopyPastePermission)(user.uid);\n            setHasQuickAdvantage(copyPastePermission.hasPermission);\n            setCopyPasteDaysRemaining(copyPastePermission.daysRemaining);\n            console.log('Copy-paste permission status:', {\n                hasPermission: copyPastePermission.hasPermission,\n                daysRemaining: copyPastePermission.daysRemaining,\n                expiryDate: copyPastePermission.expiryDate\n            });\n        } catch (error) {\n            console.error('Error loading translation settings:', error);\n        }\n    };\n    // Fast initialization - loads all data in one go\n    const initializeWorkPageFast = async ()=>{\n        try {\n            setIsLoadingTranslations(true);\n            console.log('🚀 Fast loading work page data...');\n            const workData = await (0,_lib_translationManager__WEBPACK_IMPORTED_MODULE_8__.initializeWorkPageData)(user.uid);\n            if (!workData.canWork) {\n                console.log('🚫 User cannot work today');\n                setIsDailyCompleted(true);\n                return;\n            }\n            // Set all data from cache\n            setTranslationSettings({\n                earningPerBatch: workData.userSettings.earningPerBatch,\n                plan: workData.userSettings.plan\n            });\n            setHasQuickAdvantage(workData.userSettings.hasQuickAdvantage);\n            setCopyPasteDaysRemaining(workData.userSettings.copyPasteDaysRemaining);\n            setTodayTranslations(workData.todayProgress.todayTranslations);\n            setTotalTranslationsCompleted(workData.todayProgress.totalTranslations);\n            setActiveDays(workData.todayProgress.activeDays || 0);\n            // Convert translations to work format\n            const translationItems = workData.translations.map((item)=>({\n                    english: item.english,\n                    hindi: item.hindi,\n                    spanish: item.spanish,\n                    french: item.french,\n                    german: item.german,\n                    italian: item.italian,\n                    portuguese: item.portuguese,\n                    russian: item.russian,\n                    arabic: item.arabic,\n                    chinese: item.chinese,\n                    japanese: item.japanese,\n                    korean: item.korean,\n                    turkish: item.turkish,\n                    dutch: item.dutch,\n                    swedish: item.swedish,\n                    polish: item.polish,\n                    ukrainian: item.ukrainian,\n                    greek: item.greek,\n                    hebrew: item.hebrew,\n                    vietnamese: item.vietnamese,\n                    thai: item.thai\n                }));\n            setTranslationItems(translationItems);\n            // Initialize session\n            initializeSession();\n            // Generate first translation step if no restored progress\n            if (!currentStep) {\n                generateNewTranslationStep(translationItems);\n            }\n            console.log('✅ Fast initialization complete!');\n        } catch (error) {\n            console.error('❌ Error in fast initialization:', error);\n            // Fallback to old method\n            loadTranslationData();\n            loadTranslationSettings();\n            loadUserData();\n            initializeTranslations();\n        } finally{\n            setIsLoadingTranslations(false);\n        }\n    };\n    const loadUserData = async ()=>{\n        try {\n            const data = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserData)(user.uid);\n            setUserData(data);\n            if (data) {\n                try {\n                    await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.updateUserActiveDays)(user.uid);\n                } catch (error) {\n                    console.error('Error updating active days:', error);\n                }\n                const planStatus = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.isUserPlanExpired)(user.uid);\n                setDaysLeft(planStatus.daysLeft || 0);\n                setActiveDays(planStatus.activeDays || 0);\n                console.log('📊 Plan status loaded:', {\n                    plan: data.plan,\n                    expired: planStatus.expired,\n                    daysLeft: planStatus.daysLeft,\n                    activeDays: planStatus.activeDays,\n                    reason: planStatus.reason\n                });\n            }\n        } catch (error) {\n            console.error('Error loading user data:', error);\n        }\n    };\n    const saveWorkProgress = async ()=>{\n        if (!user || !currentStep) return;\n        try {\n            setIsAutoSaving(true);\n            const progress = {\n                currentStep,\n                userTypedText,\n                selectedLanguage,\n                isTypingComplete,\n                completedTranslations: localTranslationCount,\n                batchProgress: localTranslationCount / 50 * 100,\n                lastSaved: new Date(),\n                userId: user.uid\n            };\n            // Save to session manager\n            _lib_sessionManager__WEBPACK_IMPORTED_MODULE_10__.sessionManager.saveWorkProgress(progress);\n            // Also save to localStorage as backup\n            const progressKey = \"work_progress_backup_\".concat(user.uid);\n            localStorage.setItem(progressKey, JSON.stringify(progress));\n            setLastSaved(new Date());\n            console.log('✅ Work progress saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving work progress:', error);\n        } finally{\n            setIsAutoSaving(false);\n        }\n    };\n    const restoreWorkProgress = ()=>{\n        if (!user) return;\n        try {\n            // Try to restore from session manager first\n            let savedProgress = _lib_sessionManager__WEBPACK_IMPORTED_MODULE_10__.sessionManager.getWorkProgress();\n            // If not found, try localStorage backup\n            if (!savedProgress) {\n                const progressKey = \"work_progress_backup_\".concat(user.uid);\n                const backupData = localStorage.getItem(progressKey);\n                if (backupData) {\n                    savedProgress = JSON.parse(backupData);\n                }\n            }\n            if (savedProgress && savedProgress.userId === user.uid) {\n                // Check if the saved progress is from today\n                const today = new Date().toDateString();\n                const savedDate = new Date(savedProgress.lastSaved).toDateString();\n                if (savedDate === today) {\n                    console.log('🔄 Restoring work progress from:', savedProgress.lastSaved);\n                    // Restore state\n                    setCurrentStep(savedProgress.currentStep);\n                    setUserTypedText(savedProgress.userTypedText);\n                    setSelectedLanguage(savedProgress.selectedLanguage);\n                    setIsTypingComplete(savedProgress.isTypingComplete);\n                    setLocalTranslationCount(savedProgress.completedTranslations);\n                    setLastSaved(new Date(savedProgress.lastSaved));\n                    // Show restoration notification\n                    sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                        icon: 'info',\n                        title: 'Work Progress Restored',\n                        text: \"Your previous work session has been restored. Progress: \".concat(savedProgress.completedTranslations, \"/50 translations.\"),\n                        timer: 3000,\n                        showConfirmButton: false\n                    });\n                    return true // Progress was restored\n                    ;\n                } else {\n                    console.log('🗑️ Clearing old work progress from:', savedDate);\n                    _lib_sessionManager__WEBPACK_IMPORTED_MODULE_10__.sessionManager.clearWorkProgress();\n                    const progressKey = \"work_progress_backup_\".concat(user.uid);\n                    localStorage.removeItem(progressKey);\n                }\n            }\n        } catch (error) {\n            console.error('❌ Error restoring work progress:', error);\n        }\n        return false // No progress was restored\n        ;\n    };\n    const initializeSession = ()=>{\n        const today = new Date().toDateString();\n        const sessionKey = \"translation_session_\".concat(user.uid, \"_\").concat(today);\n        const savedCount = localStorage.getItem(sessionKey);\n        if (savedCount) {\n            const count = parseInt(savedCount);\n            setLocalTranslationCount(count);\n        }\n    };\n    const initializeTranslations = async ()=>{\n        try {\n            setIsLoadingTranslations(true);\n            // Check if we have restored progress\n            const hasRestoredProgress = currentStep !== null;\n            // Initialize translation system with batching\n            const { initializeTranslationSystem } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/translationManager */ \"(app-pages-browser)/./src/lib/translationManager.ts\"));\n            const currentBatchTranslations = await initializeTranslationSystem();\n            // Convert TranslationData to TranslationItem format\n            const translationItems = currentBatchTranslations.map((item)=>({\n                    english: item.english,\n                    hindi: item.hindi,\n                    spanish: item.spanish,\n                    french: item.french,\n                    german: item.german,\n                    italian: item.italian,\n                    portuguese: item.portuguese,\n                    russian: item.russian,\n                    arabic: item.arabic,\n                    chinese: item.chinese,\n                    japanese: item.japanese,\n                    korean: item.korean,\n                    turkish: item.turkish,\n                    dutch: item.dutch,\n                    swedish: item.swedish,\n                    polish: item.polish,\n                    ukrainian: item.ukrainian,\n                    greek: item.greek,\n                    hebrew: item.hebrew,\n                    vietnamese: item.vietnamese,\n                    thai: item.thai\n                }));\n            setTranslationItems(translationItems);\n            // Only generate new step if we don't have restored progress\n            if (!hasRestoredProgress) {\n                generateNewTranslationStep(translationItems);\n            } else {\n                console.log('🔄 Using restored translation step');\n            }\n        } catch (error) {\n            console.error('Error loading translations:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'error',\n                title: 'Loading Error',\n                text: 'Failed to load translation data. Please refresh the page.'\n            });\n        } finally{\n            setIsLoadingTranslations(false);\n        }\n    };\n    // Reset current step (for blocked users)\n    const resetCurrentStep = ()=>{\n        setUserTypedText('');\n        setPreviousValidText('') // Clear previous valid text\n        ;\n        setTypingErrors([]);\n        setIsTypingComplete(false);\n        setSelectedLanguage('');\n        setIsLanguageCorrect(false);\n        setShowTranslation(false);\n        setPasteDetected(false);\n        setTypingBlocked(false);\n        setBlockReason('');\n        setLastInputTime(0);\n        setLastInputLength(0);\n        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n            icon: 'info',\n            title: 'Reset Complete!',\n            text: 'You can now start typing again. Please type carefully.',\n            timer: 2000,\n            showConfirmButton: false\n        });\n    };\n    // Clear only typing errors (for correction)\n    const clearTypingErrors = ()=>{\n        setTypingErrors([]);\n        setTypingBlocked(false);\n        setBlockReason('');\n    };\n    // Generate a new translation step\n    const generateNewTranslationStep = function() {\n        let items = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : translationItems;\n        if (items.length === 0) return;\n        const randomIndex = Math.floor(Math.random() * items.length);\n        const selectedItem = items[randomIndex];\n        const randomTargetLang = (0,_lib_translationManager__WEBPACK_IMPORTED_MODULE_8__.getRandomTargetLanguage)();\n        const targetLangData = _lib_translationManager__WEBPACK_IMPORTED_MODULE_8__.AVAILABLE_LANGUAGES.find((lang)=>lang.code === randomTargetLang);\n        const newStep = {\n            id: \"step_\".concat(Date.now(), \"_\").concat(Math.random()),\n            englishText: selectedItem.english,\n            targetLanguage: randomTargetLang,\n            targetLanguageName: (targetLangData === null || targetLangData === void 0 ? void 0 : targetLangData.name) || 'Unknown',\n            targetTranslation: selectedItem[randomTargetLang] || 'Translation not available',\n            userTypedText: '',\n            selectedLanguage: '',\n            isTypingComplete: false,\n            isLanguageSelected: false,\n            isConverted: false,\n            isSubmitted: false\n        };\n        setCurrentStep(newStep);\n        setUserTypedText('');\n        setPreviousValidText('') // Reset previous valid text for new step\n        ;\n        setTypingErrors([]);\n        setIsTypingComplete(false);\n        setSelectedLanguage('');\n        setIsLanguageCorrect(false);\n        setShowTranslation(false);\n        setPasteDetected(false);\n        setTypingBlocked(false);\n        setBlockReason('');\n    };\n    // Typing validation with paste detection and smart error handling\n    const handleTextInput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkPage.useCallback[handleTextInput]\": (e)=>{\n            if (!currentStep || isTypingComplete) return;\n            const newValue = e.target.value;\n            const currentTime = Date.now();\n            // Initialize timing for first input\n            if (lastInputTime === 0) {\n                setLastInputTime(currentTime);\n                setLastInputLength(0);\n            }\n            // First, validate the input to check for errors\n            const errors = validateTyping(newValue, currentStep.englishText);\n            // For non-advantage users: strict error handling - prevent typing beyond errors\n            if (errors.length > 0 && !hasQuickAdvantage) {\n                const firstErrorIndex = errors[0];\n                // If user is trying to type beyond the first error, block it\n                if (newValue.length > firstErrorIndex + 1) {\n                    // Trim to the error position + 1 character (allow the error character to be visible)\n                    const trimmedValue = newValue.substring(0, firstErrorIndex + 1);\n                    e.target.value = trimmedValue;\n                    setUserTypedText(trimmedValue);\n                    setTypingErrors(validateTyping(trimmedValue, currentStep.englishText));\n                    // Set error state silently (no popup dialogue)\n                    if (!typingBlocked) {\n                        setTypingBlocked(true);\n                        setBlockReason(\"Typing error at position \".concat(firstErrorIndex + 1));\n                    }\n                    return;\n                }\n            }\n            // Only run paste detection if there are no typing errors AND user is not currently in error correction mode\n            // Also skip if user was previously in error state (to avoid false positives during corrections)\n            if (errors.length === 0 && !hasQuickAdvantage && !typingBlocked && detectPasteAttempt(newValue, currentTime)) {\n                // Revert to the last known valid text state (not current state)\n                const revertText = previousValidText;\n                // Revert to previous valid text state\n                setUserTypedText(revertText);\n                e.target.value = revertText;\n                // Show brief warning without blocking\n                setPasteDetected(true);\n                // Provide more specific feedback based on the detection reason\n                const title = blockReason.includes('speed') ? 'Fast Typing Detected!' : 'Paste Not Allowed!';\n                const text = blockReason.includes('speed') ? \"\".concat(blockReason, \". Please type at a moderate pace and continue.\") : \"\".concat(blockReason, \". Please continue typing manually.\");\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'warning',\n                    title,\n                    text,\n                    timer: 2000,\n                    showConfirmButton: false,\n                    toast: true,\n                    position: 'top-end'\n                });\n                // Clear the paste detection flag after a short delay to allow continued typing\n                setTimeout({\n                    \"WorkPage.useCallback[handleTextInput]\": ()=>{\n                        setPasteDetected(false);\n                    }\n                }[\"WorkPage.useCallback[handleTextInput]\"], 1000);\n                return;\n            }\n            // Update previous valid text only when there are no errors\n            if (errors.length === 0) {\n                setPreviousValidText(newValue);\n            }\n            // Update the text and error state\n            setUserTypedText(newValue);\n            setTypingErrors(errors);\n            // Handle error state management\n            if (errors.length > 0) {\n                // User has typing errors\n                if (!typingBlocked) {\n                    setTypingBlocked(true);\n                    setBlockReason(\"Typing error detected\");\n                }\n            } else {\n                // No errors - clear blocked state\n                if (typingBlocked) {\n                    setTypingBlocked(false);\n                    setBlockReason('');\n                }\n            }\n            // Check if typing is complete and correct\n            if (newValue === currentStep.englishText && errors.length === 0) {\n                setIsTypingComplete(true);\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'success',\n                    title: 'Perfect!',\n                    text: 'Text typed correctly. Now select the target language.',\n                    timer: 2000,\n                    showConfirmButton: false\n                });\n            }\n            // Update timing tracking\n            setLastInputTime(currentTime);\n            setLastInputLength(newValue.length);\n        }\n    }[\"WorkPage.useCallback[handleTextInput]\"], [\n        currentStep,\n        isTypingComplete,\n        pasteDetected,\n        typingBlocked,\n        hasQuickAdvantage,\n        userTypedText,\n        previousValidText,\n        blockReason,\n        lastInputTime\n    ]);\n    // Enhanced paste detection with multiple methods (optimized for fast typists)\n    const detectPasteAttempt = (newValue, currentTime)=>{\n        const timeDiff = currentTime - lastInputTime;\n        const lengthDiff = newValue.length - userTypedText.length;\n        // Skip detection if this is the first input or very short text\n        if (lastInputTime === 0 || newValue.length < 4) {\n            return false;\n        }\n        // Skip detection if user was recently in error state (likely correcting)\n        if (typingBlocked || typingErrors.length > 0) {\n            return false;\n        }\n        // Skip detection for small changes (likely normal typing or corrections)\n        if (Math.abs(lengthDiff) <= 1) {\n            return false;\n        }\n        // Method 1: More than 5 characters at once (STRICT RULE - increased from 3 to 5)\n        if (lengthDiff > 5) {\n            console.log('🚫 Paste detected: More than 5 characters at once');\n            setBlockReason('More than 5 characters added at once');\n            return true;\n        }\n        // Method 2: Very fast typing (unrealistic speed - more than 3 chars per 50ms)\n        // Further increased threshold to allow fast typists and avoid false positives during corrections\n        if (lengthDiff > 3 && timeDiff < 50) {\n            console.log('🚫 Paste detected: Unrealistic typing speed (>3 chars in <50ms)');\n            setBlockReason('Typing speed too fast (possible paste)');\n            return true;\n        }\n        // Method 3: Large text block inserted instantly (increased threshold)\n        if (lengthDiff > 15) {\n            console.log('🚫 Paste detected: Large text block');\n            setBlockReason('Large text block added instantly');\n            return true;\n        }\n        // Method 4: Perfect text match with suspicious speed (improved calculation)\n        if (newValue.length > 30 && newValue === (currentStep === null || currentStep === void 0 ? void 0 : currentStep.englishText.substring(0, newValue.length))) {\n            // Calculate characters per second over the entire typing session\n            const totalTime = currentTime - (lastInputTime === 0 ? currentTime : lastInputTime);\n            const charsPerSecond = newValue.length / (totalTime / 1000);\n            // Flag if typing more than 10 characters per second with perfect accuracy\n            if (charsPerSecond > 10 && totalTime > 1000) {\n                console.log('🚫 Paste detected: Perfect match with high speed', {\n                    charsPerSecond,\n                    totalTime\n                });\n                setBlockReason('Perfect text match with unrealistic speed');\n                return true;\n            }\n        }\n        // Method 5: Detect clipboard-like patterns (multiple words at once)\n        if (lengthDiff > 3) {\n            const addedText = newValue.substring(userTypedText.length);\n            const wordCount = addedText.trim().split(/\\s+/).length;\n            // If adding 2+ complete words at once, likely paste\n            if (wordCount >= 2 && addedText.includes(' ')) {\n                console.log('🚫 Paste detected: Multiple words added at once');\n                setBlockReason('Multiple words added simultaneously');\n                return true;\n            }\n        }\n        return false;\n    };\n    // Additional event handlers for paste detection and error correction\n    const handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkPage.useCallback[handleKeyDown]\": (e)=>{\n            if (!hasQuickAdvantage) {\n                // Detect Ctrl+V, Cmd+V\n                if ((e.ctrlKey || e.metaKey) && e.key === 'v') {\n                    e.preventDefault();\n                    sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                        icon: 'warning',\n                        title: 'Paste Not Allowed!',\n                        text: 'Keyboard paste shortcuts are disabled. Please continue typing manually.',\n                        timer: 2000,\n                        toast: true,\n                        position: 'top-end',\n                        showConfirmButton: false\n                    });\n                }\n                // Detect long press (holding a key)\n                if (e.repeat) {\n                    console.log('🚫 Long press detected');\n                    // Allow backspace and delete for corrections\n                    if (e.key !== 'Backspace' && e.key !== 'Delete') {\n                        e.preventDefault();\n                    }\n                }\n            }\n            // Handle any key input for error correction (not just backspace)\n            if (typingErrors.length > 0) {\n                // Allow any editing - the handleTextInput will manage error state\n                setTimeout({\n                    \"WorkPage.useCallback[handleKeyDown]\": ()=>{\n                        // Check if errors are fixed after any key input\n                        const newValue = e.target.value;\n                        const newErrors = validateTyping(newValue, (currentStep === null || currentStep === void 0 ? void 0 : currentStep.englishText) || '');\n                        if (newErrors.length === 0) {\n                            clearTypingErrors();\n                        }\n                    }\n                }[\"WorkPage.useCallback[handleKeyDown]\"], 10);\n            }\n        }\n    }[\"WorkPage.useCallback[handleKeyDown]\"], [\n        hasQuickAdvantage,\n        typingErrors,\n        currentStep\n    ]);\n    // Detect drag and drop\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkPage.useCallback[handleDrop]\": (e)=>{\n            if (!hasQuickAdvantage) {\n                e.preventDefault();\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'warning',\n                    title: 'Drag & Drop Not Allowed!',\n                    text: 'Please continue typing the text manually.',\n                    timer: 2000,\n                    toast: true,\n                    position: 'top-end',\n                    showConfirmButton: false\n                });\n            }\n        }\n    }[\"WorkPage.useCallback[handleDrop]\"], [\n        hasQuickAdvantage\n    ]);\n    // Detect right-click context menu\n    const handleContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkPage.useCallback[handleContextMenu]\": (e)=>{\n            if (!hasQuickAdvantage) {\n                e.preventDefault();\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'warning',\n                    title: 'Context Menu Disabled',\n                    text: 'Right-click menu is disabled to prevent paste operations.',\n                    timer: 1500\n                });\n            }\n        }\n    }[\"WorkPage.useCallback[handleContextMenu]\"], [\n        hasQuickAdvantage\n    ]);\n    // Validate typing character by character\n    const validateTyping = (userText, targetText)=>{\n        const errors = [];\n        for(let i = 0; i < userText.length; i++){\n            if (i >= targetText.length || userText[i] !== targetText[i]) {\n                errors.push(i);\n            }\n        }\n        return errors;\n    };\n    // Handle language selection\n    const handleLanguageSelect = (languageCode)=>{\n        if (!currentStep || !isTypingComplete) return;\n        setSelectedLanguage(languageCode);\n        if (languageCode === currentStep.targetLanguage) {\n            setIsLanguageCorrect(true);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'success',\n                title: 'Correct Language!',\n                text: 'You selected the correct language. Click Convert to see the translation.',\n                timer: 2000,\n                showConfirmButton: false\n            });\n        } else {\n            setIsLanguageCorrect(false);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'error',\n                title: 'Wrong Language!',\n                text: \"Please select \".concat(currentStep.targetLanguageName, \" language.\"),\n                timer: 2000,\n                showConfirmButton: false\n            });\n        }\n    };\n    // Handle convert button click\n    const handleConvert = ()=>{\n        if (!currentStep || !isLanguageCorrect) return;\n        setShowTranslation(true);\n        setCurrentStep((prev)=>prev ? {\n                ...prev,\n                isConverted: true\n            } : null);\n    };\n    // Handle submit translation\n    const handleSubmitTranslation = ()=>{\n        if (!currentStep || !showTranslation) return;\n        // Prevent submitting more than 50 translations\n        if (localTranslationCount >= 50) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'warning',\n                title: 'Daily Limit Reached!',\n                text: 'You have already completed 50 translations for today. Please submit your batch to earn rewards.',\n                timer: 3000,\n                showConfirmButton: false\n            });\n            return;\n        }\n        // Mark current step as submitted\n        setCurrentStep((prev)=>prev ? {\n                ...prev,\n                isSubmitted: true\n            } : null);\n        // Increment local count\n        const newLocalCount = localTranslationCount + 1;\n        setLocalTranslationCount(newLocalCount);\n        // Save to localStorage\n        const today = new Date().toDateString();\n        const sessionKey = \"translation_session_\".concat(user.uid, \"_\").concat(today);\n        localStorage.setItem(sessionKey, newLocalCount.toString());\n        // Show progress\n        if (newLocalCount < 50) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'success',\n                title: 'Translation Submitted!',\n                text: \"Progress: \".concat(newLocalCount, \"/50 translations completed.\"),\n                timer: 2000,\n                showConfirmButton: false\n            }).then(()=>{\n                // Generate next translation step\n                generateNewTranslationStep();\n            });\n        } else {\n            // Exactly 50 translations completed - show final submit option\n            setCanSubmitBatch(true);\n            setShowFinalSubmit(true);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'success',\n                title: '🎉 All Translations Completed!',\n                text: 'You have completed all 50 translations! Click \"Submit & Earn\" to get your rewards.',\n                timer: 3000,\n                showConfirmButton: false\n            });\n            // Clear current step to prevent further translations\n            setCurrentStep(null);\n            setShowTranslation(false);\n            setIsTypingComplete(false);\n            setUserTypedText('');\n            setSelectedLanguage('');\n            setIsLanguageCorrect(false);\n        }\n    };\n    const submitTranslations = async ()=>{\n        if (!canSubmitBatch || isSubmitting || localTranslationCount < 50) return;\n        // Check if work is blocked due to leave\n        if (isLeaveBlocked) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'warning',\n                title: 'Submission Not Available',\n                text: leaveStatus.reason || 'Translation submission is not available due to leave.',\n                confirmButtonText: 'Go to Dashboard'\n            }).then(()=>{\n                window.location.href = '/dashboard';\n            });\n            return;\n        }\n        try {\n            setIsSubmitting(true);\n            // Calculate earning for the batch of 50 translations\n            const batchEarningAmount = translationSettings.earningPerBatch;\n            // Update translation count in database (add 50 translations)\n            for(let i = 0; i < 50; i++){\n                await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.updateVideoCount)(user.uid);\n            }\n            // Add batch earning to wallet\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.updateWalletBalance)(user.uid, batchEarningAmount);\n            // Add transaction record for the batch\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.addTransaction)(user.uid, {\n                type: 'translation_earning',\n                amount: batchEarningAmount,\n                description: \"Batch completion reward - 50 translations completed\"\n            });\n            // Update local state\n            const newTodayTranslations = Math.min(todayTranslations + 50, 50);\n            setTodayTranslations(newTodayTranslations);\n            setTotalTranslationsCompleted(totalTranslationsCompleted + 50);\n            // Clear local session data\n            const today = new Date().toDateString();\n            const sessionKey = \"translation_session_\".concat(user.uid, \"_\").concat(today);\n            localStorage.removeItem(sessionKey);\n            setLocalTranslationCount(0);\n            setCanSubmitBatch(false);\n            setShowFinalSubmit(false);\n            setIsDailyCompleted(true) // Mark daily session as completed\n            ;\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'success',\n                title: '🎉 Daily Session Completed!',\n                html: '\\n          <div class=\"text-center\">\\n            <p class=\"text-lg font-bold text-green-600 mb-2\">₹'.concat(batchEarningAmount, ' Earned!</p>\\n            <p class=\"mb-2\">50 translations completed and submitted</p>\\n            <p class=\"text-sm text-gray-600 mb-3\">Earnings have been added to your wallet</p>\\n            <p class=\"text-sm text-blue-600 font-semibold\">\\n              \\uD83C\\uDF89 Your daily session is complete! Come back tomorrow for your next session.\\n            </p>\\n          </div>\\n        '),\n                confirmButtonText: 'Go to Dashboard',\n                timer: 6000,\n                showConfirmButton: true\n            }).then(()=>{\n                window.location.href = '/dashboard';\n            });\n        } catch (error) {\n            console.error('Error submitting translations:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'error',\n                title: 'Submission Failed',\n                text: 'There was an error submitting your translations. Please try again.'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (loading || isLoadingTranslations || isChecking) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading translations...'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1188,\n            columnNumber: 7\n        }, this);\n    }\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1204,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: \"Loading work page...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1205,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1203,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1202,\n            columnNumber: 7\n        }, this);\n    }\n    // If user is not authenticated, show loading (useRequireAuth will handle redirect)\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: \"Authenticating...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1214,\n            columnNumber: 7\n        }, this);\n    }\n    // Show blocking notifications if any exist\n    if (hasBlockingNotifications && user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            userId: user.uid,\n            onAllRead: markAllAsRead\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1226,\n            columnNumber: 7\n        }, this);\n    }\n    // Show daily completion lockout\n    if (isDailyCompleted && user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center shadow-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-check-circle text-6xl text-green-400 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"Daily Work Completed! \\uD83C\\uDF89\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-lg\",\n                                children: \"You've successfully completed your 50 translations for today.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1243,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1238,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/5 rounded-xl p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/70\",\n                                        children: \"Today's Earnings:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-400 font-bold text-xl\",\n                                        children: [\n                                            \"₹\",\n                                            translationSettings.earningPerBatch\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/70\",\n                                        children: \"Translations Completed:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-semibold\",\n                                        children: \"50/50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1253,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/70\",\n                                        children: \"Next Session:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400 font-semibold\",\n                                        children: \"Tomorrow\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1257,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/60 text-sm mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-clock mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1265,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Your work session is locked until tomorrow\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1264,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-calendar-alt mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Come back tomorrow for your next 50 translations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1268,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1263,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/dashboard',\n                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-home mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1278,\n                                columnNumber: 13\n                            }, this),\n                            \"Go to Dashboard\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1274,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1236,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700\",\n        children: [\n            showSessionRecovery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionRecovery__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                onProgressRestored: handleProgressRestored,\n                onRecoveryComplete: handleSessionRecoveryComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1290,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"glass-card p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard\",\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-arrow-left mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1300,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Back to Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Translate Text & Earn\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            \"Plan: \",\n                                            translationSettings.plan\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            \"₹\",\n                                            translationSettings.earningPerBatch,\n                                            \"/batch (50 translations)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1306,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1304,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-5 gap-2 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-blue-400\",\n                                        children: todayTranslations\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"Today TL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-green-400\",\n                                        children: totalTranslationsCompleted\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1317,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"Total TL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1318,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1316,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-purple-400\",\n                                        children: Math.max(0, 50 - localTranslationCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"TL Left\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-orange-400\",\n                                        children: [\n                                            activeDays,\n                                            \"/\",\n                                            translationSettings.plan === 'Trial' ? '2' : '30'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"Active Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1328,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold \".concat(hasQuickAdvantage ? 'text-green-400' : 'text-gray-400'),\n                                        children: hasQuickAdvantage ? copyPasteDaysRemaining : '0'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"Copy Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mt-3\",\n                        children: isAutoSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-yellow-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-spinner fa-spin mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1342,\n                                    columnNumber: 15\n                                }, this),\n                                \"Saving progress...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                            lineNumber: 1341,\n                            columnNumber: 13\n                        }, this) : lastSaved ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-check mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1347,\n                                    columnNumber: 15\n                                }, this),\n                                \"Last saved: \",\n                                lastSaved.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                            lineNumber: 1346,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-shield-alt mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1352,\n                                    columnNumber: 15\n                                }, this),\n                                \"Auto-save protection enabled\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                            lineNumber: 1351,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1339,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-language mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1363,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Translate Text & Earn\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1362,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: resetCurrentStep,\n                                className: \"glass-button px-3 py-1 text-white text-sm\",\n                                title: \"Clear typed text and reset\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-eraser mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1371,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1361,\n                        columnNumber: 9\n                    }, this),\n                    !currentStep && !isLoadingTranslations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 rounded-lg p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1379,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-4\",\n                                    children: \"No Translation Available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/80 mb-6\",\n                                    children: \"Unable to load translation data. This could be due to:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1381,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-white/70 text-left max-w-md mx-auto mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mb-2\",\n                                            children: \"• Translation data file not found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1385,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mb-2\",\n                                            children: \"• Network connectivity issues\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1386,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mb-2\",\n                                            children: \"• Server maintenance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1384,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"btn-primary px-6 py-3 rounded-lg font-semibold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-sync-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1393,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Retry Loading\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                            lineNumber: 1378,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1377,\n                        columnNumber: 11\n                    }, this),\n                    currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-keyboard mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 1: Type the English text below\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 17\n                                            }, this),\n                                            hasQuickAdvantage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    navigator.clipboard.writeText(currentStep.englishText);\n                                                    sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                                        icon: 'success',\n                                                        title: 'Copied!',\n                                                        text: 'English text copied to clipboard',\n                                                        timer: 1500,\n                                                        showConfirmButton: false\n                                                    });\n                                                },\n                                                className: \"group relative bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 animate-pulse\",\n                                                title: \"Copy English text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-copy mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1425,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1427,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/60 text-xs bg-white/10 px-3 py-2 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-lock mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1431,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Copy disabled - Type manually\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-base md:text-lg font-mono leading-relaxed\",\n                                                    children: typingErrors.length > 0 && !hasQuickAdvantage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400\",\n                                                                children: currentStep.englishText.substring(0, typingErrors[0])\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                                lineNumber: 1442,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-red-500 text-white px-1 rounded animate-pulse\",\n                                                                children: currentStep.englishText[typingErrors[0]]\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                                lineNumber: 1445,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/60\",\n                                                                children: currentStep.englishText.substring(typingErrors[0] + 1)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                                lineNumber: 1448,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : currentStep.englishText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1438,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1437,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-white/60 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-info-circle mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1458,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    typingErrors.length > 0 && !hasQuickAdvantage ? \"Error at highlighted character (position \".concat(typingErrors[0] + 1, \")\") : \"Scroll to see full text if needed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1436,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: userTypedText,\n                                                onChange: handleTextInput,\n                                                onKeyDown: handleKeyDown,\n                                                onDrop: handleDrop,\n                                                onContextMenu: handleContextMenu,\n                                                disabled: isTypingComplete,\n                                                placeholder: typingErrors.length > 0 ? \"Fix the highlighted error and continue typing...\" : hasQuickAdvantage ? \"Type or paste the English text here...\" : \"Type the English text here (copy-paste not allowed). Fast typists: please type at moderate speed to avoid triggering anti-paste protection.\",\n                                                className: \"w-full h-24 md:h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono text-sm md:text-base leading-relaxed \".concat(typingErrors.length > 0 ? 'border-red-500' : '', \" \").concat(isTypingComplete ? 'border-green-500 bg-green-500/10' : ''),\n                                                onPaste: (e)=>{\n                                                    if (!hasQuickAdvantage) {\n                                                        e.preventDefault();\n                                                        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                                            icon: 'warning',\n                                                            title: 'Paste Not Allowed!',\n                                                            text: 'Please continue typing the text manually.',\n                                                            timer: 2000,\n                                                            toast: true,\n                                                            position: 'top-end',\n                                                            showConfirmButton: false\n                                                        });\n                                                    }\n                                                },\n                                                onDragOver: (e)=>{\n                                                    if (!hasQuickAdvantage) {\n                                                        e.preventDefault();\n                                                    }\n                                                },\n                                                spellCheck: false,\n                                                autoComplete: \"off\",\n                                                autoCorrect: \"off\",\n                                                autoCapitalize: \"off\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1467,\n                                                columnNumber: 17\n                                            }, this),\n                                            hasQuickAdvantage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        const text = await navigator.clipboard.readText();\n                                                        setUserTypedText(text);\n                                                        handleTextInput({\n                                                            target: {\n                                                                value: text\n                                                            }\n                                                        });\n                                                        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                                            icon: 'success',\n                                                            title: 'Pasted!',\n                                                            text: 'Text pasted from clipboard',\n                                                            timer: 1500,\n                                                            showConfirmButton: false\n                                                        });\n                                                    } catch (error) {\n                                                        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                                            icon: 'error',\n                                                            title: 'Paste Failed',\n                                                            text: 'Could not access clipboard',\n                                                            timer: 1500,\n                                                            showConfirmButton: false\n                                                        });\n                                                    }\n                                                },\n                                                className: \"group absolute top-3 right-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-2 px-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 animate-bounce disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:animate-none\",\n                                                title: \"Paste from clipboard\",\n                                                disabled: isTypingComplete,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-paste mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Paste\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1539,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1540,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1511,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1466,\n                                        columnNumber: 15\n                                    }, this),\n                                    typingErrors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-red-400 text-sm bg-red-500/10 border border-red-500/30 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-exclamation-triangle mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1548,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Typing Error Detected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1549,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1547,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-300 text-xs mb-2\",\n                                                children: [\n                                                    \"Error at position \",\n                                                    typingErrors[0] + 1,\n                                                    ': Expected \"',\n                                                    currentStep.englishText[typingErrors[0]],\n                                                    '\" but got \"',\n                                                    userTypedText[typingErrors[0]] || 'nothing',\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1551,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-200 text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-edit mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1555,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Edit the text box to correct the mistake, then continue typing.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1554,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1546,\n                                        columnNumber: 17\n                                    }, this),\n                                    isTypingComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-green-400 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-check-circle mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1563,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Perfect! Text typed correctly.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1562,\n                                        columnNumber: 17\n                                    }, this),\n                                    pasteDetected && !hasQuickAdvantage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-exclamation-triangle mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1571,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Paste Attempt Detected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1572,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1570,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-300 text-xs mt-1\",\n                                                children: blockReason.includes('speed') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-info-circle mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1577,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Fast typing detected. Please type at a moderate pace. You can continue typing normally.\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-clipboard mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1582,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Paste operation blocked. Please continue typing manually from where you left off.\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1574,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-200 text-xs mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-arrow-right mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1588,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"This message will disappear automatically. Continue typing normally.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1587,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1569,\n                                        columnNumber: 17\n                                    }, this),\n                                    typingErrors.length > 0 && !hasQuickAdvantage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/30 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 text-sm font-semibold mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-lightbulb mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1601,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"How to Fix the Error:\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1600,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-300 text-xs space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"1. Click in the text box and edit the incorrect character\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1605,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                '2. Change it to the correct character: \"',\n                                                                currentStep.englishText[typingErrors[0]],\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1606,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"3. Continue typing the rest of the text\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1607,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1604,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1599,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1598,\n                                        columnNumber: 17\n                                    }, this),\n                                    userTypedText && !isTypingComplete && typingErrors.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                // Manually trigger text validation\n                                                handleTextInput({\n                                                    target: {\n                                                        value: userTypedText\n                                                    }\n                                                });\n                                            },\n                                            className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n                                            title: \"Check if typed text is correct\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-check-circle mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1624,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Check Text\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1616,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1615,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1403,\n                                columnNumber: 13\n                            }, this),\n                            isTypingComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-globe mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1635,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Step 2: Select the target language - \",\n                                            currentStep.targetLanguageName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1634,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedLanguage,\n                                        onChange: (e)=>handleLanguageSelect(e.target.value),\n                                        className: \"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                className: \"bg-gray-800 text-white\",\n                                                children: \"Select target language...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1643,\n                                                columnNumber: 19\n                                            }, this),\n                                            _lib_translationManager__WEBPACK_IMPORTED_MODULE_8__.AVAILABLE_LANGUAGES.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: lang.code,\n                                                    className: \"bg-gray-800 text-white\",\n                                                    children: [\n                                                        lang.flag,\n                                                        \" \",\n                                                        lang.name\n                                                    ]\n                                                }, lang.code, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1645,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1638,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedLanguage && !isLanguageCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-red-400 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-times-circle mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1653,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Wrong language! Please select \",\n                                            currentStep.targetLanguageName,\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1652,\n                                        columnNumber: 19\n                                    }, this),\n                                    isLanguageCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-green-400 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-check-circle mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1660,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Correct language selected!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1659,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1633,\n                                columnNumber: 15\n                            }, this),\n                            isLanguageCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleConvert,\n                                    disabled: showTranslation,\n                                    className: \"px-8 py-3 rounded-lg font-semibold transition-all duration-300 \".concat(showTranslation ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-primary hover:scale-105'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-exchange-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1679,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Convert to \",\n                                        currentStep.targetLanguageName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1670,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1669,\n                                columnNumber: 15\n                            }, this),\n                            showTranslation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-language mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1689,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentStep.targetLanguageName,\n                                            \" Translation:\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1688,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 p-3 rounded border-l-4 border-green-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg\",\n                                            children: currentStep.targetTranslation\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1693,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1692,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSubmitTranslation,\n                                            disabled: localTranslationCount >= 50,\n                                            className: \"px-6 py-3 rounded-lg font-semibold transition-all duration-300 \".concat(localTranslationCount >= 50 ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-success hover:scale-105'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-check mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1708,\n                                                    columnNumber: 21\n                                                }, this),\n                                                localTranslationCount >= 50 ? 'Daily Limit Reached' : 'Submit Translation'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1699,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1698,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1687,\n                                columnNumber: 15\n                            }, this),\n                            showFinalSubmit && !isDailyCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 mb-4 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-bold text-xl mb-2\",\n                                                children: \"\\uD83C\\uDF89 Congratulations! You've completed 50 translations!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1719,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/90 mb-4\",\n                                                children: \"Click the button below to submit your daily batch and receive your earnings.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1722,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80 text-sm\",\n                                                children: \"⚠️ After submission, you won't be able to work until tomorrow.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1725,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1718,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: submitTranslations,\n                                        disabled: isSubmitting,\n                                        className: \"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 px-12 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none animate-pulse\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-spinner fa-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1737,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting Final Batch...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-trophy mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1742,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submit Final Batch & Earn ₹\",\n                                                translationSettings.earningPerBatch\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1730,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1717,\n                                columnNumber: 15\n                            }, this),\n                            canSubmitBatch && !showFinalSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: submitTranslations,\n                                    disabled: isSubmitting,\n                                    className: \"btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-money-bill-wave mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1758,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Submit All 50 Translations & Earn ₹\",\n                                        translationSettings.earningPerBatch\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1753,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1752,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80\",\n                                        children: [\n                                            \"Progress: \",\n                                            localTranslationCount,\n                                            \"/50 translations completed\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1766,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-white/20 rounded-full h-2 mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(localTranslationCount / 50 * 100, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1770,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1769,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1765,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1401,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1360,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n        lineNumber: 1287,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkPage, \"E++vB29iQ75Ygo362K3dp6og/LU=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth,\n        _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications,\n        _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor\n    ];\n});\n_c = WorkPage;\nvar _c;\n$RefreshReg$(_c, \"WorkPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/work/page.tsx\n"));

/***/ })

});