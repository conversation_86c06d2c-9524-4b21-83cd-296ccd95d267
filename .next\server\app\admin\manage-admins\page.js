(()=>{var e={};e.id=7515,e.ids=[7515],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72585:(e,t,r)=>{Promise.resolve().then(r.bind(r,73707))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73707:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(87979),l=r(63385),d=r(75535),c=r(33784),m=r(3582);let u={super_admin:["all"],admin:["users.read","users.write","users.delete","transactions.read","transactions.write","withdrawals.read","withdrawals.write","withdrawals.approve","notifications.read","notifications.write","settings.read","settings.write","leaves.read","leaves.write","reports.read"],moderator:["users.read","users.write","transactions.read","withdrawals.read","notifications.read","notifications.write","leaves.read"],support:["users.read","transactions.read","notifications.read","notifications.write"]};async function p(e,t){try{let r=(await (0,l.eJ)(c.j2,e.email,e.password)).user,s=e.permissions||u[e.role],a={[m.Yr.name]:e.name,[m.Yr.email]:e.email,[m.Yr.mobile]:"0000000000",[m.Yr.referralCode]:`TN${Date.now().toString().slice(-4)}`,[m.Yr.referredBy]:"",[m.Yr.plan]:"Admin",[m.Yr.planExpiry]:null,[m.Yr.activeDays]:999999,[m.Yr.totalTranslations]:0,[m.Yr.todayTranslations]:0,[m.Yr.lastTranslationDate]:null,[m.Yr.wallet]:0,[m.Yr.joinedDate]:d.Dc.now(),status:"active",role:e.role,isAdmin:!0,permissions:s};await (0,d.BN)((0,d.H9)(c.db,m.COLLECTIONS.users,r.uid),a);let i={email:e.email,name:e.name,role:e.role,permissions:s,createdAt:d.Dc.now(),isActive:!0,createdBy:t||"system"};return await (0,d.BN)((0,d.H9)(c.db,"admins",r.uid),i),await f(t||"system","admin_created",{targetUserId:r.uid,targetEmail:e.email,role:e.role}),r.uid}catch(e){throw console.error("Error creating admin account:",e),e}}async function x(e,t){try{await (0,d.mZ)((0,d.H9)(c.db,"admins",e),{isActive:!1,deactivatedAt:d.Dc.now(),deactivatedBy:t}),await (0,d.mZ)((0,d.H9)(c.db,m.COLLECTIONS.users,e),{status:"inactive"}),await f(t,"admin_deactivated",{targetUserId:e})}catch(e){throw console.error("Error deactivating admin:",e),e}}async function h(){try{let e=(0,d.P)((0,d.collection)(c.db,"admins")),t=await (0,d.getDocs)(e),r=[];return t.forEach(e=>{let t=e.data();r.push({id:e.id,name:t.name,email:t.email,role:t.role,permissions:t.permissions||[],isActive:t.isActive,createdAt:t.createdAt?.toDate()||new Date,lastLogin:t.lastLogin?.toDate(),createdBy:t.createdBy})}),r}catch(e){return console.error("Error getting all admins:",e),[]}}async function f(e,t,r){try{let s={adminId:e,action:t,details:r,timestamp:d.Dc.now(),ip:"unknown"};await (0,d.gS)((0,d.collection)(c.db,"adminLogs"),s)}catch(e){console.error("Error logging admin action:",e)}}var v=r(77567);function g(){let{user:e,loading:t,isAdmin:r}=(0,o.wC)(),[i,l]=(0,a.useState)([]),[d,c]=(0,a.useState)(!0),[m,u]=(0,a.useState)(!1),[f,g]=(0,a.useState)({name:"",email:"",password:"",role:"support"}),w=async()=>{try{c(!0);let e=await h();l(e)}catch(e){console.error("Error loading admins:",e),v.A.fire({icon:"error",title:"Error",text:"Failed to load admin list"})}finally{c(!1)}},b=async t=>{if(t.preventDefault(),!f.name||!f.email||!f.password)return void v.A.fire({icon:"error",title:"Error",text:"Please fill in all required fields"});try{await p(f,e?.uid),v.A.fire({icon:"success",title:"Admin Created",text:`Admin account created successfully for ${f.email}`}),u(!1),g({name:"",email:"",password:"",role:"support"}),w()}catch(t){console.error("Error creating admin:",t);let e="Failed to create admin account";"auth/email-already-in-use"===t.code&&(e="An account with this email already exists"),v.A.fire({icon:"error",title:"Creation Failed",text:e})}},j=async(t,r)=>{if((await v.A.fire({icon:"warning",title:"Deactivate Admin",text:`Are you sure you want to deactivate ${r}?`,showCancelButton:!0,confirmButtonText:"Yes, Deactivate",cancelButtonText:"Cancel"})).isConfirmed)try{await x(t,e?.uid||""),v.A.fire({icon:"success",title:"Admin Deactivated",text:"Admin account has been deactivated"}),w()}catch(e){console.error("Error deactivating admin:",e),v.A.fire({icon:"error",title:"Error",text:"Failed to deactivate admin"})}},N=e=>{switch(e){case"super_admin":return"bg-red-500";case"admin":return"bg-blue-500";case"moderator":return"bg-yellow-500";case"support":return"bg-green-500";default:return"bg-gray-500"}};return t||d?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Loading admin management..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("div",{className:"glass-card p-6 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-white mb-2",children:[(0,s.jsx)("i",{className:"fas fa-users-cog mr-3"}),"Manage Admins"]}),(0,s.jsx)("p",{className:"text-white/80",children:"Create and manage admin accounts"})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{onClick:()=>u(!0),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Create Admin"]}),(0,s.jsxs)(n(),{href:"/admin",className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]})]})]})}),m&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create New Admin"]}),(0,s.jsxs)("form",{onSubmit:b,className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Name"}),(0,s.jsx)("input",{type:"text",value:f.name,onChange:e=>g({...f,name:e.target.value}),className:"form-input",placeholder:"Enter admin name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",value:f.email,onChange:e=>g({...f,email:e.target.value}),className:"form-input",placeholder:"Enter admin email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Password"}),(0,s.jsx)("input",{type:"password",value:f.password,onChange:e=>g({...f,password:e.target.value}),className:"form-input",placeholder:"Enter password",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Role"}),(0,s.jsxs)("select",{value:f.role,onChange:e=>g({...f,role:e.target.value}),className:"form-input",children:[(0,s.jsx)("option",{value:"support",children:"Support"}),(0,s.jsx)("option",{value:"moderator",children:"Moderator"}),(0,s.jsx)("option",{value:"admin",children:"Admin"}),(0,s.jsx)("option",{value:"super_admin",children:"Super Admin"})]})]}),(0,s.jsxs)("div",{className:"md:col-span-2 flex space-x-3",children:[(0,s.jsxs)("button",{type:"submit",className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-save mr-2"}),"Create Admin"]}),(0,s.jsx)("button",{type:"button",onClick:()=>u(!1),className:"btn-secondary",children:"Cancel"})]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-list mr-2"}),"Admin Accounts (",i.length,")"]}),(0,s.jsxs)("div",{className:"overflow-x-auto",children:[(0,s.jsxs)("table",{className:"w-full text-white",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-white/20",children:[(0,s.jsx)("th",{className:"text-left p-3",children:"Name"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Email"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Role"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Status"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Created"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Last Login"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:i.map(t=>(0,s.jsxs)("tr",{className:"border-b border-white/10",children:[(0,s.jsx)("td",{className:"p-3",children:t.name}),(0,s.jsx)("td",{className:"p-3",children:t.email}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium text-white ${N(t.role)}`,children:t.role.replace("_"," ").toUpperCase()})}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium ${t.isActive?"bg-green-500 text-white":"bg-red-500 text-white"}`,children:t.isActive?"Active":"Inactive"})}),(0,s.jsx)("td",{className:"p-3 text-sm",children:t.createdAt.toLocaleDateString()}),(0,s.jsx)("td",{className:"p-3 text-sm",children:t.lastLogin?t.lastLogin.toLocaleDateString():"Never"}),(0,s.jsx)("td",{className:"p-3",children:t.isActive&&t.email!==e?.email&&(0,s.jsx)("button",{onClick:()=>j(t.id,t.email),className:"text-red-400 hover:text-red-300 transition-colors",title:"Deactivate Admin",children:(0,s.jsx)("i",{className:"fas fa-user-times"})})})]},t.id))})]}),0===i.length&&(0,s.jsx)("div",{className:"text-center py-8 text-white/60",children:"No admin accounts found"})]})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82313:(e,t,r)=>{Promise.resolve().then(r.bind(r,92385))},87108:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["manage-admins",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,92385)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\manage-admins\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\manage-admins\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/manage-admins/page",pathname:"/admin/manage-admins",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},92385:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\manage-admins\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\manage-admins\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2579,6803,3582],()=>r(87108));module.exports=s})();