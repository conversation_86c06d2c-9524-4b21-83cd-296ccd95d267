(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2215],{8269:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(5155),l=r(2115),a=r(6681),n=r(3592);function c(){let{user:e,loading:s}=(0,a.Nu)(),[r,c]=(0,l.useState)(""),[i,d]=(0,l.useState)(null),[o,h]=(0,l.useState)(!1),u=async()=>{if(!r.trim())return void alert("Please enter a user ID");h(!0),d(null);try{let e=await (0,n._I)(r.trim());d(e)}catch(e){d({success:!1,message:"Error: ".concat((null==e?void 0:e.message)||"Unknown error")})}finally{h(!1)}};return s?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,t.jsx)("div",{className:"min-h-screen p-4",children:(0,t.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Referral Bonus"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white mb-2",children:"User ID:"}),(0,t.jsx)("input",{type:"text",value:r,onChange:e=>c(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID to process referral bonus"})]}),(0,t.jsx)("button",{onClick:u,disabled:o,className:"w-full btn-primary py-3 rounded-lg font-semibold disabled:opacity-50",children:o?"Processing...":"Process Referral Bonus"}),i&&(0,t.jsxs)("div",{className:"p-4 rounded-lg ".concat(i.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"),children:[(0,t.jsx)("h3",{className:"font-bold ".concat(i.success?"text-green-400":"text-red-400"),children:i.success?"Success!":"Failed"}),(0,t.jsx)("p",{className:"text-white mt-2",children:i.message})]})]}),(0,t.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,t.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Enter the user ID of someone who was referred"}),(0,t.jsx)("li",{children:"• User must be on a paid plan (Junior, Senior, Expert)"}),(0,t.jsx)("li",{children:"• User must have been referred by someone"}),(0,t.jsx)("li",{children:"• Referral bonus must not have been credited already"})]})]})]})})})}},9606:(e,s,r)=>{Promise.resolve().then(r.bind(r,8269))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6681,3592,8441,1684,7358],()=>s(9606)),_N_E=e.O()}]);