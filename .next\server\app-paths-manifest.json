{"/admin/login/page": "app/admin/login/page.js", "/admin/fix-permissions/page": "app/admin/fix-permissions/page.js", "/_not-found/page": "app/_not-found/page.js", "/admin/fix-active-days/page": "app/admin/fix-active-days/page.js", "/admin/active-days-manager/page": "app/admin/active-days-manager/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/manage-admins/page": "app/admin/manage-admins/page.js", "/admin/page": "app/admin/page.js", "/admin/notifications/page": "app/admin/notifications/page.js", "/admin/setup/page": "app/admin/setup/page.js", "/admin/test-blocking/page": "app/admin/test-blocking/page.js", "/admin/upload-users/page": "app/admin/upload-users/page.js", "/admin/simple-upload/page": "app/admin/simple-upload/page.js", "/admin/transactions/page": "app/admin/transactions/page.js", "/admin/withdrawals/page": "app/admin/withdrawals/page.js", "/clear-cache/page": "app/clear-cache/page.js", "/forgot-password/page": "app/forgot-password/page.js", "/admin/users/page": "app/admin/users/page.js", "/plans/page": "app/plans/page.js", "/refer/page": "app/refer/page.js", "/register/page": "app/register/page.js", "/profile/page": "app/profile/page.js", "/test-copy-paste-admin/page": "app/test-copy-paste-admin/page.js", "/registration-diagnostics/page": "app/registration-diagnostics/page.js", "/support/page": "app/support/page.js", "/reset-password/page": "app/reset-password/page.js", "/test-active-days-service/page": "app/test-active-days-service/page.js", "/test-copy-paste-export/page": "app/test-copy-paste-export/page.js", "/admin/leaves/page": "app/admin/leaves/page.js", "/test-copy-paste-reduction/page": "app/test-copy-paste-reduction/page.js", "/test-copy-paste-service/page": "app/test-copy-paste-service/page.js", "/test-referral/page": "app/test-referral/page.js", "/wallet/page": "app/wallet/page.js", "/test-simple-registration/page": "app/test-simple-registration/page.js", "/test-sample-upload/page": "app/test-sample-upload/page.js", "/test-translations/page": "app/test-translations/page.js", "/transactions/page": "app/transactions/page.js", "/login/page": "app/login/page.js", "/dashboard/page": "app/dashboard/page.js", "/page": "app/page.js", "/work/page": "app/work/page.js"}