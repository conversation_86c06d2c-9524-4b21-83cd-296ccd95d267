(()=>{var e={};e.id=8171,e.ids=[8171],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60114:(e,r,s)=>{Promise.resolve().then(s.bind(s,77111))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68001:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\clear-cache\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\clear-cache\\page.tsx","default")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77111:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var t=s(60687),i=s(43210);function o(){let[e,r]=(0,i.useState)(""),[s,o]=(0,i.useState)(!1),n=e=>{r(r=>r+e+"\n"),console.log(e)},a=async()=>{r(""),o(!0);try{if(n("\uD83E\uDDF9 Starting cache clearing process..."),"caches"in window){let e=await caches.keys();for(let r of(n(`📦 Found ${e.length} caches: ${e.join(", ")}`),e))await caches.delete(r),n(`✅ Deleted cache: ${r}`)}else n("❌ Cache API not supported");if("serviceWorker"in navigator){let e=await navigator.serviceWorker.getRegistrations();for(let r of(n(`🔧 Found ${e.length} service worker registrations`),e))await r.unregister(),n(`✅ Unregistered service worker: ${r.scope}`)}else n("❌ Service Worker API not supported");localStorage.clear(),n("✅ Cleared localStorage"),sessionStorage.clear(),n("✅ Cleared sessionStorage"),"indexedDB"in window&&n("✅ IndexedDB available (manual clearing may be needed)"),n("\n\uD83C\uDF89 Cache clearing completed!"),n("\uD83D\uDCA1 Refresh the page to see changes")}catch(e){n(`❌ Cache clearing failed: ${e.message}`),console.error("Cache clearing error:",e)}finally{o(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"glass-card p-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Clear Cache & Service Worker"}),(0,t.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,t.jsx)("button",{onClick:a,disabled:s,className:"btn-primary w-full",children:s?"Clearing Cache...":"Clear All Cache & Service Workers"}),(0,t.jsx)("button",{onClick:()=>{n("\uD83D\uDD04 Performing hard refresh..."),window.location.reload()},className:"btn-secondary w-full",children:"Hard Refresh Page"})]}),(0,t.jsx)("div",{className:"bg-black/30 rounded-lg p-4 text-white font-mono text-sm whitespace-pre-wrap max-h-96 overflow-y-auto",children:e||"Click the button to clear cache and service workers..."}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-yellow-500/20 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,t.jsxs)("ol",{className:"text-white/80 text-sm space-y-1",children:[(0,t.jsx)("li",{children:'1. Click "Clear All Cache & Service Workers"'}),(0,t.jsx)("li",{children:"2. Wait for completion"}),(0,t.jsx)("li",{children:'3. Click "Hard Refresh Page" or press Ctrl+Shift+R'}),(0,t.jsx)("li",{children:"4. Try registration again"})]})]}),(0,t.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,t.jsx)("a",{href:"/register",className:"btn-primary inline-block",children:"Go to Registration"}),(0,t.jsx)("a",{href:"/debug-registration-simple",className:"btn-secondary inline-block ml-4",children:"Go to Debug Registration"})]})]})})})}},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83508:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var t=s(65239),i=s(48088),o=s(88170),n=s.n(o),a=s(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);s.d(r,c);let l={children:["",{children:["clear-cache",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,68001)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\clear-cache\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\clear-cache\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/clear-cache/page",pathname:"/clear-cache",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97066:(e,r,s)=>{Promise.resolve().then(s.bind(s,68001))}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[2579,6803],()=>s(83508));module.exports=t})();