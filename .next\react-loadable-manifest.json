{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app", "files": ["static/chunks/_pages-dir-browser_node_modules_next_dist_pages__app_js.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error", "files": ["static/chunks/_pages-dir-browser_node_modules_next_dist_pages__error_js.js"]}, "app\\dashboard\\page.tsx -> @/lib/leaveService": {"id": "app\\dashboard\\page.tsx -> @/lib/leaveService", "files": ["static/chunks/_app-pages-browser_src_lib_leaveService_ts.js"]}, "app\\work\\page.tsx -> @/lib/translationManager": {"id": "app\\work\\page.tsx -> @/lib/translationManager", "files": []}, "components\\UserLeaveManagement.tsx -> @/lib/dataService": {"id": "components\\UserLeaveManagement.tsx -> @/lib/dataService", "files": []}, "components\\UserLeaveManagement.tsx -> @/lib/leaveService": {"id": "components\\UserLeaveManagement.tsx -> @/lib/leaveService", "files": ["static/chunks/_app-pages-browser_src_lib_leaveService_ts.js"]}, "lib\\activeDaysService.ts -> ./leaveService": {"id": "lib\\activeDaysService.ts -> ./leaveService", "files": ["static/chunks/_app-pages-browser_src_lib_leaveService_ts.js"]}, "lib\\dataService.ts -> ./activeDaysService": {"id": "lib\\dataService.ts -> ./activeDaysService", "files": ["static/chunks/_app-pages-browser_src_lib_activeDaysService_ts.js"]}, "lib\\dataService.ts -> ./copyPasteService": {"id": "lib\\dataService.ts -> ./copyPasteService", "files": ["static/chunks/_app-pages-browser_src_lib_copyPasteService_ts.js"]}, "lib\\dataService.ts -> ./leaveService": {"id": "lib\\dataService.ts -> ./leaveService", "files": ["static/chunks/_app-pages-browser_src_lib_leaveService_ts.js"]}, "lib\\leaveService.ts -> ./dataService": {"id": "lib\\leaveService.ts -> ./dataService", "files": ["static/chunks/_app-pages-browser_src_lib_dataService_ts.js"]}, "lib\\translationManager.ts -> ./dataService": {"id": "lib\\translationManager.ts -> ./dataService", "files": []}}