"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7718],{7718:(e,t,s)=>{s.d(t,{Mk:()=>y,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>p});var a=s(6104),r=s(5317);let o={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},i={users:"users"};class n{static async checkCopyPastePermission(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,i.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let s=t.data()[o.quickTranslationAdvantageExpiry];if(!s)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=s.toDate(),c=new Date,l=n>c,p=l?Math.ceil((n.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:p,expiryDate:n}}catch(t){return console.error("Error checking copy-paste permission for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let s=new Date;s.setDate(s.getDate()+t);let n=(0,r.H9)(a.db,i.users,e);await (0,r.mZ)(n,{[o.quickTranslationAdvantageExpiry]:r.Dc.fromDate(s),[o.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(t," days (expires: ").concat(s.toDateString(),")"))}catch(t){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),t),t}}static async removeCopyPastePermission(e){try{let t=(0,r.H9)(a.db,i.users,e);await (0,r.mZ)(t,{[o.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(t){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,i.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let s=t.data(),n=s[o.quickTranslationAdvantageExpiry],c=s[o.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=n.toDate(),t=new Date,s=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:s,expired:0===s}}let p=n.toDate(),u=new Date(p);u.setDate(u.getDate()-1);let y=(0,r.H9)(a.db,i.users,e);if(u<=new Date)return await (0,r.mZ)(y,{[o.quickTranslationAdvantageExpiry]:null,[o.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(y,{[o.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[o.lastCopyPasteReduction]:r.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(t," days remaining")),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error("Error reducing copy-paste days for user ".concat(e,":"),t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,i.users)),t=0,s=0,o=0,c=0;for(let a of e.docs)try{t++;let e=await n.reduceCopyPasteDays(a.id);e.reduced&&(s++,e.expired&&o++)}catch(e){c++,console.error("Error processing copy-paste reduction for user ".concat(a.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Reduced: ".concat(s," users")),console.log("   - Expired: ".concat(o," users")),console.log("   - Errors: ".concat(c," users")),{processed:t,reduced:s,expired:o,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await n.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error("Error getting copy-paste status for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=n.checkCopyPastePermission,l=n.grantCopyPastePermission,p=n.removeCopyPastePermission,u=n.reduceCopyPasteDays,y=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus}}]);