(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{1469:(e,s,a)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var a in s)Object.defineProperty(e,a,{enumerable:!0,get:s[a]})}(s,{default:function(){return d},getImageProps:function(){return n}});let l=a(8229),t=a(8883),r=a(3063),i=l._(a(1193));function n(e){let{props:s}=(0,t.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(s))void 0===a&&delete s[e];return{props:s}}let d=r.Image},5487:(e,s,a)=>{Promise.resolve().then(a.bind(a,7220))},6766:(e,s,a)=>{"use strict";a.d(s,{default:()=>t.a});var l=a(1469),t=a.n(l)},7220:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>x});var l=a(5155),t=a(2115),r=a(6874),i=a.n(r),n=a(6766),d=a(6681),c=a(6779),o=a(12);function x(){let{user:e,loading:s,isAdmin:a}=(0,d.wC)(),[r,x]=(0,t.useState)(null),[m,h]=(0,t.useState)(!0),[g,f]=(0,t.useState)(!1);(0,t.useEffect)(()=>{a&&j()},[a]);let j=async()=>{try{h(!0);let e=await (0,c.Pn)();x(e)}catch(e){console.error("Error loading dashboard stats:",e)}finally{h(!1)}};return s||m?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)("div",{className:"spinner"})}):(0,l.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,l.jsxs)("aside",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ".concat(g?"translate-x-0":"-translate-x-full"," transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"),children:[(0,l.jsxs)("div",{className:"flex items-center justify-center h-16 bg-gray-900",children:[(0,l.jsx)(n.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:32,height:32,className:"mr-2"}),(0,l.jsx)("span",{className:"text-white text-xl font-bold",children:"Instra Global Admin"})]}),(0,l.jsx)("nav",{className:"mt-8",children:(0,l.jsxs)("div",{className:"px-4 space-y-2",children:[(0,l.jsxs)(i(),{href:"/admin",className:"flex items-center px-4 py-2 text-white bg-gray-700 rounded-lg",children:[(0,l.jsx)("i",{className:"fas fa-tachometer-alt mr-3"}),"Dashboard"]}),(0,l.jsxs)(i(),{href:"/admin/users",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-users mr-3"}),"Users"]}),(0,l.jsxs)(i(),{href:"/admin/simple-upload",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-file-csv mr-3"}),"Simple Upload"]}),(0,l.jsxs)(i(),{href:"/admin/transactions",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-exchange-alt mr-3"}),"Transactions"]}),(0,l.jsxs)(i(),{href:"/admin/withdrawals",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-money-bill-wave mr-3"}),"Withdrawals"]}),(0,l.jsxs)(i(),{href:"/admin/notifications",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-bell mr-3"}),"Notifications"]}),(0,l.jsxs)(i(),{href:"/admin/leaves",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-calendar-times mr-3"}),"Leave Management"]}),(0,l.jsxs)(i(),{href:"/admin/manage-admins",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-users-cog mr-3"}),"Manage Admins"]}),(0,l.jsxs)(i(),{href:"/admin/settings",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-cog mr-3"}),"Settings"]}),(0,l.jsxs)(i(),{href:"/admin/active-days-manager",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Active Days Manager"]}),(0,l.jsxs)(i(),{href:"/admin/fix-active-days",className:"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-tools mr-3"}),"Fix Active Days"]})]})}),(0,l.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,l.jsxs)("button",{onClick:()=>{(0,o._f)(null==e?void 0:e.uid,"/admin/login")},className:"w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-sign-out-alt mr-3"}),"Logout"]})})]}),(0,l.jsxs)("div",{className:"lg:ml-64",children:[(0,l.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,l.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,l.jsx)("button",{onClick:()=>f(!g),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,l.jsx)("i",{className:"fas fa-bars text-xl"})}),(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("span",{className:"text-gray-700",children:"Welcome, Admin"}),(0,l.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,l.jsx)("i",{className:"fas fa-user-shield text-gray-600"})})]})]})}),(0,l.jsxs)("main",{className:"p-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,l.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-users text-blue-600 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:("number"==typeof(null==r?void 0:r.totalUsers)?r.totalUsers:0).toLocaleString()})]})]})}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-language text-green-600 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Translations"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:("number"==typeof(null==r?void 0:r.totalTranslations)?r.totalTranslations:0).toLocaleString()})]})]})}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-rupee-sign text-yellow-600 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),(0,l.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",("number"==typeof(null==r?void 0:r.totalEarnings)?r.totalEarnings:0).toLocaleString()]})]})]})}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-clock text-red-600 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Withdrawals"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==r?void 0:r.pendingWithdrawals)||"0"})]})]})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow mb-8",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Activity"})}),(0,l.jsx)("div",{className:"p-6",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:(null==r?void 0:r.todayUsers)||"0"}),(0,l.jsx)("p",{className:"text-gray-600",children:"New Users"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-3xl font-bold text-green-600",children:("number"==typeof(null==r?void 0:r.todayTranslations)?r.todayTranslations:0).toLocaleString()}),(0,l.jsx)("p",{className:"text-gray-600",children:"Translations Completed"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("p",{className:"text-3xl font-bold text-yellow-600",children:["₹",("number"==typeof(null==r?void 0:r.todayEarnings)?r.todayEarnings:0).toLocaleString()]}),(0,l.jsx)("p",{className:"text-gray-600",children:"Earnings Paid"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-3xl font-bold text-red-600",children:(null==r?void 0:r.todayWithdrawals)||"0"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Withdrawals"})]})]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,l.jsx)(i(),{href:"/admin/users",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-users text-blue-600 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Users"}),(0,l.jsx)("p",{className:"text-gray-600",children:"View and manage user accounts"})]})]})}),(0,l.jsx)(i(),{href:"/admin/withdrawals",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-money-bill-wave text-green-600 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Process Withdrawals"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Review and approve withdrawals"})]})]})}),(0,l.jsx)(i(),{href:"/admin/notifications",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-bell text-yellow-600 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Send Notifications"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Notify users about updates"})]})]})}),(0,l.jsx)(i(),{href:"/admin/settings",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-cog text-purple-600 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"System Settings"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Configure platform settings"})]})]})}),(0,l.jsx)(i(),{href:"/admin/fix-active-days",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-tools text-red-600 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fix Active Days"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Fix daily counts and active days"})]})]})}),(0,l.jsx)(i(),{href:"/admin/manage-admins",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-indigo-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-users-cog text-indigo-600 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Manage Admins"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Create and manage admin accounts"})]})]})}),(0,l.jsx)(i(),{href:"/admin/simple-upload",className:"bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-file-csv text-green-600 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Simple Upload"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Update translations, wallet & active days via CSV"})]})]})})]})]})]}),g&&(0,l.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>f(!1)})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3063,6681,3592,6779,8441,1684,7358],()=>s(5487)),_N_E=e.O()}]);