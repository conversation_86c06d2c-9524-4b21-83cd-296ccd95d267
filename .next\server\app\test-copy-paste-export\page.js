(()=>{var e={};e.id=7646,e.ids=[1391,3772,7646],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19119:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=a(65239),s=a(48088),o=a(88170),i=a.n(o),n=a(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let d={children:["",{children:["test-copy-paste-export",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,39094)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-export\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-export\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/test-copy-paste-export/page",pathname:"/test-copy-paste-export",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28181:(e,t,a)=>{Promise.resolve().then(a.bind(a,39094))},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},39094:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-copy-paste-export\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-export\\page.tsx","default")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65232:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(60687),s=a(43210),o=a(87979),i=a(91391),n=a(83475),l=a(27878);function d(){let{user:e,loading:t}=(0,o.Nu)(),[a,d]=(0,s.useState)(null),[c,u]=(0,s.useState)(!1),p=async()=>{u(!0),d(null);try{console.log("\uD83E\uDDEA Testing copy-paste export functionality...");let e=await (0,i.CF)();console.log(`📊 Retrieved ${e.length} users for testing`);let t=e.slice(0,5),a=[];for(let e of t)try{let t=await (0,l.checkCopyPastePermission)(e.id),r={email:e.email,name:e.name,hasQuickTranslationAdvantageExpiry:!!e.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!e.quickVideoAdvantageExpiry,quickTranslationAdvantageExpiry:e.quickTranslationAdvantageExpiry,quickVideoAdvantageExpiry:e.quickVideoAdvantageExpiry,servicePermission:t.hasPermission,serviceDaysRemaining:t.daysRemaining,serviceExpiryDate:t.expiryDate};a.push(r),console.log(`📋 User ${e.email}:`,r)}catch(t){console.error(`❌ Error testing user ${e.email}:`,t)}let r=(0,n.Fz)(t);console.log("\uD83D\uDCCA Export data sample:",r.slice(0,2));let s=e.filter(e=>e.quickTranslationAdvantageExpiry||e.quickVideoAdvantageExpiry);d({success:!0,totalUsers:e.length,usersWithCopyPaste:s.length,testUsers:a,exportSample:r.slice(0,2)})}catch(e){console.error("❌ Test failed:",e),d({success:!1,error:e.message})}finally{u(!1)}},x=async()=>{try{let e=await (0,i.CF)(),t=(0,n.Fz)(e);(0,n.Bf)(t,"test_copy_paste_export"),alert(`Downloaded test export with ${e.length} users. Check the Copy-Paste columns!`)}catch(e){console.error("Export failed:",e),alert("Export failed. Check console for details.")}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Export"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:p,disabled:c,className:"bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold disabled:opacity-50",children:c?"Testing...":"Test Copy-Paste Data"}),(0,r.jsx)("button",{onClick:x,className:"bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold",children:"Download Test Export"})]}),a&&(0,r.jsx)("div",{className:"p-4 bg-white/10 rounded-lg",children:a.success?(0,r.jsxs)("div",{className:"text-white space-y-4",children:[(0,r.jsx)("h3",{className:"font-bold text-green-400",children:"Test Results:"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Total Users:"})," ",a.totalUsers]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Users with Copy-Paste:"})," ",a.usersWithCopyPaste]})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-bold mb-2",children:"Sample Users Test:"}),(0,r.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:a.testUsers.map((e,t)=>(0,r.jsxs)("div",{className:"p-2 bg-white/5 rounded text-sm",children:[(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:e.email})}),(0,r.jsxs)("p",{children:["Service Permission: ",e.servicePermission?"Yes":"No"]}),(0,r.jsxs)("p",{children:["Service Days Remaining: ",e.serviceDaysRemaining]}),(0,r.jsxs)("p",{children:["Has Expiry Field: ",e.hasQuickTranslationAdvantageExpiry?"Yes":"No"]})]},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-bold mb-2",children:"Export Sample:"}),(0,r.jsx)("div",{className:"text-xs bg-black/20 p-2 rounded overflow-x-auto",children:(0,r.jsx)("pre",{children:JSON.stringify(a.exportSample,null,2)})})]})]}):(0,r.jsxs)("div",{className:"text-red-400",children:[(0,r.jsx)("h3",{className:"font-bold",children:"Test Failed:"}),(0,r.jsx)("p",{children:a.error})]})})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,r.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:'• Click "Test Copy-Paste Data" to analyze first 5 users'}),(0,r.jsx)("li",{children:'• Click "Download Test Export" to get full CSV with copy-paste data'}),(0,r.jsx)("li",{children:"• Check console for detailed debugging information"}),(0,r.jsx)("li",{children:"• Verify Copy-Paste columns in the downloaded CSV"})]})]})]})})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),s=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],o=new Blob(["\uFEFF"+[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";let r=s.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return`"${e}"`}return a instanceof Date?`"${a.toLocaleDateString()}"`:"object"==typeof a&&null!==a&&a.toDate?`"${a.toDate().toLocaleDateString()}"`:r&&("number"==typeof a||!isNaN(Number(a)))?`"${a}"`:"number"==typeof a?a.toString():`"${String(a)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(o);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function s(e){return e.map(t=>{let a=0,r=null,s="No",o=t.quickTranslationAdvantageExpiry||t.quickVideoAdvantageExpiry;if(o)try{o instanceof Date?r=o:o.toDate&&"function"==typeof o.toDate?r=o.toDate():r=new Date(o);let i=new Date,n=r.getTime()-i.getTime();s=(a=Math.max(0,Math.ceil(n/864e5)))>0?"Yes":"No",5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}:`,{expiryField:o,expiryFieldType:typeof o,copyPasteExpiryDate:r,copyPasteRemainingDays:a,copyPastePermission:s,hasQuickTranslationAdvantageExpiry:!!t.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!t.quickVideoAdvantageExpiry})}catch(e){console.error(`❌ Error calculating copy-paste days for user ${t.email}:`,e)}else 5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}: No copy-paste expiry field found`);return{"User ID":t.id||"",Name:t.name||"",Email:t.email||"",Mobile:String(t.mobile||""),"Referral Code":t.referralCode||"","Referred By":t.referredBy||"Direct",Plan:t.plan||"","Plan Expiry":t.planExpiry instanceof Date?t.planExpiry.toLocaleDateString():t.planExpiry?new Date(t.planExpiry).toLocaleDateString():"","Active Days":t.activeDays||0,"Total Translations":t.totalTranslations||t.totalVideos||0,"Today Translations":t.todayTranslations||t.todayVideos||0,"Last Translation Date":t.lastTranslationDate instanceof Date?t.lastTranslationDate.toLocaleDateString():t.lastTranslationDate?new Date(t.lastTranslationDate).toLocaleDateString():t.lastVideoDate instanceof Date?t.lastVideoDate.toLocaleDateString():t.lastVideoDate?new Date(t.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":s,"Copy-Paste Remaining Days":a,"Copy-Paste Expiry":r?r.toLocaleDateString():"","Copy-Paste Granted By":t.quickTranslationAdvantageGrantedBy||t.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":t.quickTranslationAdvantageGrantedAt?t.quickTranslationAdvantageGrantedAt instanceof Date?t.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(t.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":t.wallet||0,"Referral Bonus Credited":t.referralBonusCredited?"Yes":"No",Status:t.status||"","Joined Date":t.joinedDate instanceof Date?t.joinedDate.toLocaleDateString():t.joinedDate?new Date(t.joinedDate).toLocaleDateString():"","Joined Time":t.joinedDate instanceof Date?t.joinedDate.toLocaleTimeString():t.joinedDate?new Date(t.joinedDate).toLocaleTimeString():""}})}function o(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function n(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>r,Fz:()=>s,Pe:()=>n,dB:()=>i,sL:()=>o})},86389:(e,t,a)=>{Promise.resolve().then(a.bind(a,65232))},91391:(e,t,a)=>{"use strict";a.d(t,{CF:()=>c,Pn:()=>n,TK:()=>x,getWithdrawals:()=>p,hG:()=>g,lo:()=>l,nQ:()=>u,searchUsers:()=>d,updateWithdrawalStatus:()=>D});var r=a(75535),s=a(33784),o=a(3582);let i=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=i.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=r.Dc.fromDate(t),n=await (0,r.getDocs)((0,r.collection)(s.db,o.COLLECTIONS.users)),l=n.size,d=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.users),(0,r._M)(o.Yr.joinedDate,">=",a)),c=(await (0,r.getDocs)(d)).size,u=0,p=0,x=0,g=0;n.forEach(e=>{let a=e.data(),r=Number(a[o.Yr.totalTranslations])||0,s=Number(a[o.Yr.wallet])||0;u+=r,p+=s;let i=a[o.Yr.lastTranslationDate]?.toDate();if(i&&i.toDateString()===t.toDateString()){let e=Number(a[o.Yr.todayTranslations])||0;x+=e}});try{let e=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.transactions),(0,r._M)(o.Yr.type,"==","translation_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(e)).forEach(e=>{let a=e.data(),r=a[o.Yr.date]?.toDate();if(r&&r>=t){let e=Number(a[o.Yr.amount])||0;g+=e}})}catch(e){console.warn("Could not fetch today's transactions:",e)}let D=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),m=(await (0,r.getDocs)(D)).size,h=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.withdrawals),(0,r._M)("date",">=",a)),y=(await (0,r.getDocs)(h)).size,f={totalUsers:Number(l)||0,totalTranslations:Number(u)||0,totalEarnings:Number(p)||0,pendingWithdrawals:Number(m)||0,todayUsers:Number(c)||0,todayTranslations:Number(x)||0,todayEarnings:Number(g)||0,todayWithdrawals:Number(y)||0};return i.set(e,{data:f,timestamp:Date.now()}),f}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(e=50,t=null){try{let a=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.users),(0,r.My)(o.Yr.joinedDate,"desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.users),(0,r.My)(o.Yr.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let i=await (0,r.getDocs)(a);return{users:i.docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[o.Yr.joinedDate]?.toDate(),planExpiry:t[o.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.users),(0,r.My)(o.Yr.joinedDate,"desc"));return(await (0,r.getDocs)(a)).docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[o.Yr.joinedDate]?.toDate(),planExpiry:t[o.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}}).filter(e=>{let a=String(e[o.Yr.name]||"").toLowerCase(),r=String(e[o.Yr.email]||"").toLowerCase(),s=String(e[o.Yr.mobile]||"").toLowerCase(),i=String(e[o.Yr.referralCode]||"").toLowerCase();return a.includes(t)||r.includes(t)||s.includes(t)||i.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.users),(0,r.My)(o.Yr.joinedDate,"desc"));return(await (0,r.getDocs)(e)).docs.map(e=>{let t=e.data();return{id:e.id,...t,joinedDate:t[o.Yr.joinedDate]?.toDate(),planExpiry:t[o.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:t.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:t.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:t.lastTranslationDate?.toDate(),lastVideoDate:t.lastVideoDate?.toDate(),lastCopyPasteReduction:t.lastCopyPasteReduction?.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.users));return(await (0,r.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function p(e=50,t=null){try{let a=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(s.db,o.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(t),(0,r.AB)(e)));let i=await (0,r.getDocs)(a);return{withdrawals:i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()})),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function x(e,t){try{await (0,r.mZ)((0,r.H9)(s.db,o.COLLECTIONS.users,e),t),i.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function g(e){try{await (0,r.kd)((0,r.H9)(s.db,o.COLLECTIONS.users,e)),i.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function D(e,t,n){try{let l=await (0,r.x7)((0,r.H9)(s.db,o.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=l.data(),p={status:t,updatedAt:r.Dc.now()};if(n&&(p.adminNotes=n),await (0,r.mZ)((0,r.H9)(s.db,o.COLLECTIONS.withdrawals,e),p),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3582));await e(d,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3582));await e(d,c),await t(d,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}i.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[2579,6803,3582],()=>a(19119));module.exports=r})();