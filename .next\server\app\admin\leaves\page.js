(()=>{var e={};e.id=9451,e.ids=[9451],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23907:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\leaves\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\leaves\\page.tsx","default")},27055:(e,t,a)=>{Promise.resolve().then(a.bind(a,46661))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},46661:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(60687),s=a(43210),n=a(85814),i=a.n(n),o=a(87979),l=a(83475),c=a(77567);function d(){let{user:e,loading:t,isAdmin:n}=(0,o.wC)(),[d,u]=(0,s.useState)([]),[x,p]=(0,s.useState)(!0),[m,g]=(0,s.useState)(!1),[y,f]=(0,s.useState)(!1),[h,D]=(0,s.useState)({date:"",reason:"",type:"holiday"}),v=async()=>{try{p(!0);let{getAdminLeaves:e}=await a.e(7087).then(a.bind(a,87087)),t=await e();u(t)}catch(e){console.error("Error loading leaves:",e),u([]),c.A.fire({icon:"error",title:"Error",text:"Failed to load leaves. Please try again."})}finally{p(!1)}},b=async()=>{try{if(!h.date||!h.reason.trim())return void c.A.fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let t=new Date(h.date),r=new Date;if(r.setHours(0,0,0,0),t<r)return void c.A.fire({icon:"error",title:"Invalid Date",text:"Cannot create leave for past dates."});if(d.find(e=>e.date.toDateString()===t.toDateString()))return void c.A.fire({icon:"error",title:"Duplicate Leave",text:"Leave already exists for this date."});f(!0);let{createAdminLeave:s}=await a.e(7087).then(a.bind(a,87087));await s({date:t,reason:h.reason.trim(),type:h.type,createdBy:e?.email||"admin"}),await v(),c.A.fire({icon:"success",title:"Leave Created!",text:`Admin leave created for ${t.toLocaleDateString()}.`,timer:3e3,showConfirmButton:!1}),D({date:"",reason:"",type:"holiday"}),g(!1)}catch(e){console.error("Error creating leave:",e),c.A.fire({icon:"error",title:"Creation Failed",text:"Failed to create leave. Please try again."})}finally{f(!1)}},N=async e=>{try{if((await c.A.fire({title:"Delete Leave",text:"Are you sure you want to delete this leave?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Delete",cancelButtonText:"Cancel"})).isConfirmed){let{deleteAdminLeave:t}=await a.e(7087).then(a.bind(a,87087));await t(e),await v(),c.A.fire({icon:"success",title:"Leave Deleted",text:"Leave has been deleted successfully.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error deleting leave:",e),c.A.fire({icon:"error",title:"Delete Failed",text:"Failed to delete leave. Please try again."})}},j=e=>{switch(e){case"holiday":return"bg-green-100 text-green-800";case"maintenance":return"bg-blue-100 text-blue-800";case"emergency":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"holiday":return"fas fa-calendar-day text-green-500";case"maintenance":return"fas fa-tools text-blue-500";case"emergency":return"fas fa-exclamation-triangle text-red-500";default:return"fas fa-calendar text-gray-500"}};return t||x?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading leaves..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Leave Management"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-gray-700",children:["Total: ",d.length]}),(0,r.jsxs)("button",{onClick:()=>{if(0===d.length)return void c.A.fire({icon:"warning",title:"No Data",text:"No leaves to export."});let e=d.map(e=>({Date:e.date instanceof Date?e.date.toLocaleDateString():new Date(e.date).toLocaleDateString(),Reason:e.reason,Type:e.type.charAt(0).toUpperCase()+e.type.slice(1),"Created By":e.createdBy,"Created At":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():new Date(e.createdAt).toLocaleDateString()}));(0,l.Bf)(e,"admin-leaves"),c.A.fire({icon:"success",title:"Export Complete",text:`Exported ${d.length} leaves to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:()=>g(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Leave"]}),(0,r.jsxs)("button",{onClick:v,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===d.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("i",{className:"fas fa-calendar-times text-gray-300 text-6xl mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No leaves scheduled"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your first admin leave to block work and withdrawals"}),(0,r.jsxs)("button",{onClick:()=>g(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Add First Leave"]})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reason"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created By"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.date.toLocaleDateString()}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.date.toLocaleDateString("en-US",{weekday:"long"})})]}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm text-gray-900 max-w-xs",children:e.reason})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${j(e.type)}`,children:[(0,r.jsx)("i",{className:`${w(e.type)} mr-1`}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.createdBy}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.createdAt.toLocaleDateString()})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("button",{onClick:()=>N(e.id),className:"text-red-600 hover:text-red-900",children:[(0,r.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete"]})})]},e.id))})]})})})}),m&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Add Admin Leave"}),(0,r.jsx)("button",{onClick:()=>g(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,r.jsx)("input",{type:"date",value:h.date,onChange:e=>D(t=>({...t,date:e.target.value})),min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,r.jsxs)("select",{value:h.type,onChange:e=>D(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"holiday",children:"Holiday"}),(0,r.jsx)("option",{value:"maintenance",children:"Maintenance"}),(0,r.jsx)("option",{value:"emergency",children:"Emergency"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,r.jsx)("textarea",{value:h.reason,onChange:e=>D(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,r.jsx)("button",{onClick:()=>g(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,r.jsx)("button",{onClick:b,disabled:y,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Create Leave"]})})]})]})})]})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54010:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=a(65239),s=a(48088),n=a(88170),i=a.n(n),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c={children:["",{children:["admin",{children:["leaves",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,23907)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\leaves\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\leaves\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/leaves/page",pathname:"/admin/leaves",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73911:(e,t,a)=>{Promise.resolve().then(a.bind(a,23907))},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),s=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],n=new Blob(["\uFEFF"+[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";let r=s.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return`"${e}"`}return a instanceof Date?`"${a.toLocaleDateString()}"`:"object"==typeof a&&null!==a&&a.toDate?`"${a.toDate().toLocaleDateString()}"`:r&&("number"==typeof a||!isNaN(Number(a)))?`"${a}"`:"number"==typeof a?a.toString():`"${String(a)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(n);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function s(e){return e.map(t=>{let a=0,r=null,s="No",n=t.quickTranslationAdvantageExpiry||t.quickVideoAdvantageExpiry;if(n)try{n instanceof Date?r=n:n.toDate&&"function"==typeof n.toDate?r=n.toDate():r=new Date(n);let i=new Date,o=r.getTime()-i.getTime();s=(a=Math.max(0,Math.ceil(o/864e5)))>0?"Yes":"No",5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}:`,{expiryField:n,expiryFieldType:typeof n,copyPasteExpiryDate:r,copyPasteRemainingDays:a,copyPastePermission:s,hasQuickTranslationAdvantageExpiry:!!t.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!t.quickVideoAdvantageExpiry})}catch(e){console.error(`❌ Error calculating copy-paste days for user ${t.email}:`,e)}else 5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}: No copy-paste expiry field found`);return{"User ID":t.id||"",Name:t.name||"",Email:t.email||"",Mobile:String(t.mobile||""),"Referral Code":t.referralCode||"","Referred By":t.referredBy||"Direct",Plan:t.plan||"","Plan Expiry":t.planExpiry instanceof Date?t.planExpiry.toLocaleDateString():t.planExpiry?new Date(t.planExpiry).toLocaleDateString():"","Active Days":t.activeDays||0,"Total Translations":t.totalTranslations||t.totalVideos||0,"Today Translations":t.todayTranslations||t.todayVideos||0,"Last Translation Date":t.lastTranslationDate instanceof Date?t.lastTranslationDate.toLocaleDateString():t.lastTranslationDate?new Date(t.lastTranslationDate).toLocaleDateString():t.lastVideoDate instanceof Date?t.lastVideoDate.toLocaleDateString():t.lastVideoDate?new Date(t.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":s,"Copy-Paste Remaining Days":a,"Copy-Paste Expiry":r?r.toLocaleDateString():"","Copy-Paste Granted By":t.quickTranslationAdvantageGrantedBy||t.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":t.quickTranslationAdvantageGrantedAt?t.quickTranslationAdvantageGrantedAt instanceof Date?t.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(t.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":t.wallet||0,"Referral Bonus Credited":t.referralBonusCredited?"Yes":"No",Status:t.status||"","Joined Date":t.joinedDate instanceof Date?t.joinedDate.toLocaleDateString():t.joinedDate?new Date(t.joinedDate).toLocaleDateString():"","Joined Time":t.joinedDate instanceof Date?t.joinedDate.toLocaleTimeString():t.joinedDate?new Date(t.joinedDate).toLocaleTimeString():""}})}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>r,Fz:()=>s,Pe:()=>o,dB:()=>i,sL:()=>n})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[2579,6803],()=>a(54010));module.exports=r})();