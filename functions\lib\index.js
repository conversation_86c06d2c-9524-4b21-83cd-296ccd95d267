"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.submitTranslationBatch = exports.getUserWorkData = exports.helloWorld = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin (only if not already initialized)
if (!admin.apps.length) {
    admin.initializeApp();
}
const db = admin.firestore();
// Minimal test function
exports.helloWorld = functions.https.onCall(async (data, context) => {
    return {
        message: 'Hello from Firebase Functions!',
        timestamp: new Date().toISOString()
    };
});
/**
 * Optimized function to get user work data with minimal reads
 * Replaces 5+ separate Firestore reads with 1 function call
 */
exports.getUserWorkData = functions.https.onCall(async (data, context) => {
    var _a, _b, _c;
    try {
        // Verify authentication
        if (!context || !context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log(`📊 Getting work data for user: ${userId}`);
        // Single read to get user data
        const userDoc = await db.collection('users').doc(userId).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User not found');
        }
        const userData = userDoc.data();
        const today = new Date();
        const todayString = today.toDateString();
        // Calculate if user can work today
        const lastTranslationDate = (_a = userData.lastTranslationDate) === null || _a === void 0 ? void 0 : _a.toDate();
        const todayTranslations = userData.todayTranslations || 0;
        // Check if it's a new day
        const isNewDay = !lastTranslationDate || lastTranslationDate.toDateString() !== todayString;
        const canWork = isNewDay || todayTranslations < 50;
        // Check copy-paste permission
        const copyPasteExpiry = (_b = userData.quickTranslationAdvantageExpiry) === null || _b === void 0 ? void 0 : _b.toDate();
        const hasCopyPastePermission = copyPasteExpiry && copyPasteExpiry > today;
        const copyPasteDaysRemaining = hasCopyPastePermission
            ? Math.ceil((copyPasteExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
            : 0;
        // Calculate plan expiry info
        let planExpired = false;
        let daysLeft = 0;
        if (userData.plan === 'Trial') {
            const joinedDate = ((_c = userData.joinedDate) === null || _c === void 0 ? void 0 : _c.toDate()) || today;
            const daysSinceJoining = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));
            daysLeft = Math.max(0, 2 - daysSinceJoining);
            planExpired = daysLeft <= 0;
        }
        else if (userData.planExpiry) {
            const planExpiryDate = userData.planExpiry.toDate();
            planExpired = today > planExpiryDate;
            daysLeft = planExpired ? 0 : Math.ceil((planExpiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        }
        // Get plan-based earning amount
        const planEarnings = {
            'Trial': 25, 'Junior': 150, 'Senior': 250, 'Expert': 400,
            'Starter': 25, 'Basic': 75, 'Premium': 150, 'Gold': 200, 'Platinum': 250, 'Diamond': 400
        };
        const earningPerBatch = planEarnings[userData.plan] || 25;
        const result = {
            // User basic info
            name: userData.name,
            email: userData.email,
            plan: userData.plan,
            wallet: userData.wallet || 0,
            // Translation data
            totalTranslations: userData.totalTranslations || 0,
            todayTranslations: isNewDay ? 0 : todayTranslations,
            canWork,
            earningPerBatch,
            // Plan info
            planExpired,
            daysLeft,
            activeDays: userData.activeDays || 0,
            // Copy-paste info
            hasCopyPastePermission,
            copyPasteDaysRemaining,
            // Status flags
            isNewDay,
            lastTranslationDate: (lastTranslationDate === null || lastTranslationDate === void 0 ? void 0 : lastTranslationDate.toISOString()) || null
        };
        console.log(`✅ Work data retrieved for user ${userId}`);
        return result;
    }
    catch (error) {
        console.error('Error getting user work data:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to get user work data');
    }
});
/**
 * Optimized function to submit translation batch with minimal writes
 * Combines 3+ separate operations into 1 atomic transaction
 */
exports.submitTranslationBatch = functions.https.onCall(async (data, context) => {
    try {
        // Verify authentication
        if (!context || !context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        const { batchSize = 50 } = data;
        console.log(`📊 Submitting translation batch for user: ${userId}, size: ${batchSize}`);
        // Use a transaction to ensure atomicity and reduce reads
        const result = await db.runTransaction(async (transaction) => {
            var _a;
            // Single read within transaction
            const userRef = db.collection('users').doc(userId);
            const userDoc = await transaction.get(userRef);
            if (!userDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'User not found');
            }
            const userData = userDoc.data();
            const today = new Date();
            // Validate user can submit batch
            const lastTranslationDate = (_a = userData.lastTranslationDate) === null || _a === void 0 ? void 0 : _a.toDate();
            const todayTranslations = userData.todayTranslations || 0;
            const isNewDay = !lastTranslationDate || lastTranslationDate.toDateString() !== today.toDateString();
            if (!isNewDay && todayTranslations >= 50) {
                throw new functions.https.HttpsError('failed-precondition', 'Daily translation limit already reached');
            }
            // Calculate earning amount
            const planEarnings = {
                'Trial': 25, 'Junior': 150, 'Senior': 250, 'Expert': 400,
                'Starter': 25, 'Basic': 75, 'Premium': 150, 'Gold': 200, 'Platinum': 250, 'Diamond': 400
            };
            const earningAmount = planEarnings[userData.plan] || 25;
            // Prepare user updates
            const userUpdates = {
                totalTranslations: admin.firestore.FieldValue.increment(batchSize),
                wallet: admin.firestore.FieldValue.increment(earningAmount),
                lastTranslationDate: admin.firestore.Timestamp.fromDate(today)
            };
            // Handle daily translation count
            if (isNewDay && todayTranslations > 0) {
                userUpdates.todayTranslations = batchSize;
            }
            else {
                userUpdates.todayTranslations = admin.firestore.FieldValue.increment(batchSize);
            }
            // Update user data
            transaction.update(userRef, userUpdates);
            // Add transaction record
            const transactionRef = db.collection('transactions').doc();
            transaction.set(transactionRef, {
                userId: userId,
                type: 'translation_earning',
                amount: earningAmount,
                description: `Batch completion reward - ${batchSize} translations completed`,
                status: 'completed',
                date: admin.firestore.Timestamp.fromDate(today)
            });
            return {
                success: true,
                earningAmount,
                newTotalTranslations: (userData.totalTranslations || 0) + batchSize,
                newWalletBalance: (userData.wallet || 0) + earningAmount,
                newTodayTranslations: isNewDay ? batchSize : Math.min((todayTranslations + batchSize), 50)
            };
        });
        console.log(`✅ Translation batch submitted for user ${userId}:`, result);
        return result;
    }
    catch (error) {
        console.error('Error submitting translation batch:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to submit translation batch');
    }
});
//# sourceMappingURL=index.js.map