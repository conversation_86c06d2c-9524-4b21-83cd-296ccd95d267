"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processWithdrawalRequest = exports.getUserTransactions = exports.getAdminDashboardData = exports.submitTranslationBatch = exports.getUserWorkData = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin
admin.initializeApp();
const db = admin.firestore();
// Collection names
const COLLECTIONS = {
    users: 'users',
    transactions: 'transactions',
    withdrawals: 'withdrawals',
    plans: 'plans',
    settings: 'settings',
    notifications: 'notifications',
    adminLeaves: 'adminLeaves',
    userLeaves: 'userLeaves'
};
// Field names
const FIELD_NAMES = {
    // User fields
    name: 'name',
    email: 'email',
    mobile: 'mobile',
    referralCode: 'referralCode',
    referredBy: 'referredBy',
    plan: 'plan',
    planExpiry: 'planExpiry',
    activeDays: 'activeDays',
    totalTranslations: 'totalTranslations',
    todayTranslations: 'todayTranslations',
    wallet: 'wallet',
    joinedDate: 'joinedDate',
    userStatus: 'status',
    lastTranslationDate: 'lastTranslationDate',
    // Copy-paste fields
    quickTranslationAdvantage: 'quickTranslationAdvantage',
    quickTranslationAdvantageExpiry: 'quickTranslationAdvantageExpiry',
    quickTranslationAdvantageDays: 'quickTranslationAdvantageDays',
    quickTranslationAdvantageGrantedBy: 'quickTranslationAdvantageGrantedBy',
    quickTranslationAdvantageGrantedAt: 'quickTranslationAdvantageGrantedAt',
    // Bank details
    bankAccountHolderName: 'bankAccountHolderName',
    bankAccountNumber: 'bankAccountNumber',
    bankIfscCode: 'bankIfscCode',
    bankName: 'bankName',
    bankDetailsUpdated: 'bankDetailsUpdated',
    // Transaction fields
    transactionType: 'type',
    transactionAmount: 'amount',
    transactionDate: 'date',
    transactionStatus: 'status',
    transactionDescription: 'description',
    transactionUserId: 'userId'
};
/**
 * Optimized function to get user work data with minimal reads
 * Combines multiple data fetches into a single function call
 */
exports.getUserWorkData = functions.https.onCall(async (data, context) => {
    var _a, _b, _c;
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log(`📊 Getting work data for user: ${userId}`);
        // Single read to get user data
        const userDoc = await db.collection(COLLECTIONS.users).doc(userId).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User not found');
        }
        const userData = userDoc.data();
        const today = new Date();
        const todayString = today.toDateString();
        // Calculate if user can work today
        const lastTranslationDate = (_a = userData[FIELD_NAMES.lastTranslationDate]) === null || _a === void 0 ? void 0 : _a.toDate();
        const todayTranslations = userData[FIELD_NAMES.todayTranslations] || 0;
        // Check if it's a new day
        const isNewDay = !lastTranslationDate || lastTranslationDate.toDateString() !== todayString;
        const canWork = isNewDay || todayTranslations < 50;
        // Check copy-paste permission
        const copyPasteExpiry = (_b = userData[FIELD_NAMES.quickTranslationAdvantageExpiry]) === null || _b === void 0 ? void 0 : _b.toDate();
        const hasCopyPastePermission = copyPasteExpiry && copyPasteExpiry > today;
        const copyPasteDaysRemaining = hasCopyPastePermission
            ? Math.ceil((copyPasteExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
            : 0;
        // Calculate plan expiry info
        let planExpired = false;
        let daysLeft = 0;
        if (userData[FIELD_NAMES.plan] === 'Trial') {
            const joinedDate = ((_c = userData[FIELD_NAMES.joinedDate]) === null || _c === void 0 ? void 0 : _c.toDate()) || today;
            const daysSinceJoining = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));
            daysLeft = Math.max(0, 2 - daysSinceJoining);
            planExpired = daysLeft <= 0;
        }
        else if (userData[FIELD_NAMES.planExpiry]) {
            const planExpiryDate = userData[FIELD_NAMES.planExpiry].toDate();
            planExpired = today > planExpiryDate;
            daysLeft = planExpired ? 0 : Math.ceil((planExpiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        }
        // Get plan-based earning amount
        const planEarnings = {
            'Trial': 25,
            'Junior': 150,
            'Senior': 250,
            'Expert': 400,
            'Starter': 25,
            'Basic': 75,
            'Premium': 150,
            'Gold': 200,
            'Platinum': 250,
            'Diamond': 400
        };
        const earningPerBatch = planEarnings[userData[FIELD_NAMES.plan]] || 25;
        const result = {
            // User basic info
            name: userData[FIELD_NAMES.name],
            email: userData[FIELD_NAMES.email],
            plan: userData[FIELD_NAMES.plan],
            wallet: userData[FIELD_NAMES.wallet] || 0,
            // Translation data
            totalTranslations: userData[FIELD_NAMES.totalTranslations] || 0,
            todayTranslations: isNewDay ? 0 : todayTranslations,
            canWork,
            earningPerBatch,
            // Plan info
            planExpired,
            daysLeft,
            activeDays: userData[FIELD_NAMES.activeDays] || 0,
            // Copy-paste info
            hasCopyPastePermission,
            copyPasteDaysRemaining,
            // Status flags
            isNewDay,
            lastTranslationDate: (lastTranslationDate === null || lastTranslationDate === void 0 ? void 0 : lastTranslationDate.toISOString()) || null
        };
        console.log(`✅ Work data retrieved for user ${userId}:`, {
            canWork: result.canWork,
            todayTranslations: result.todayTranslations,
            hasCopyPaste: result.hasCopyPastePermission,
            planExpired: result.planExpired
        });
        return result;
    }
    catch (error) {
        console.error('Error getting user work data:', error);
        throw new functions.https.HttpsError('internal', 'Failed to get user work data');
    }
});
/**
 * Optimized function to submit translation batch with minimal writes
 * Combines multiple operations into a single atomic transaction
 */
exports.submitTranslationBatch = functions.https.onCall(async (data, context) => {
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        const { batchSize = 50 } = data;
        console.log(`📊 Submitting translation batch for user: ${userId}, size: ${batchSize}`);
        // Use a transaction to ensure atomicity and reduce reads
        const result = await db.runTransaction(async (transaction) => {
            var _a;
            // Single read within transaction
            const userRef = db.collection(COLLECTIONS.users).doc(userId);
            const userDoc = await transaction.get(userRef);
            if (!userDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'User not found');
            }
            const userData = userDoc.data();
            const today = new Date();
            // Validate user can submit batch
            const lastTranslationDate = (_a = userData[FIELD_NAMES.lastTranslationDate]) === null || _a === void 0 ? void 0 : _a.toDate();
            const todayTranslations = userData[FIELD_NAMES.todayTranslations] || 0;
            const isNewDay = !lastTranslationDate || lastTranslationDate.toDateString() !== today.toDateString();
            if (!isNewDay && todayTranslations >= 50) {
                throw new functions.https.HttpsError('failed-precondition', 'Daily translation limit already reached');
            }
            // Calculate earning amount
            const planEarnings = {
                'Trial': 25, 'Junior': 150, 'Senior': 250, 'Expert': 400,
                'Starter': 25, 'Basic': 75, 'Premium': 150, 'Gold': 200, 'Platinum': 250, 'Diamond': 400
            };
            const earningAmount = planEarnings[userData[FIELD_NAMES.plan]] || 25;
            // Prepare user updates
            const userUpdates = {
                [FIELD_NAMES.totalTranslations]: admin.firestore.FieldValue.increment(batchSize),
                [FIELD_NAMES.wallet]: admin.firestore.FieldValue.increment(earningAmount),
                [FIELD_NAMES.lastTranslationDate]: admin.firestore.Timestamp.fromDate(today)
            };
            // Handle daily translation count
            if (isNewDay && todayTranslations > 0) {
                userUpdates[FIELD_NAMES.todayTranslations] = batchSize;
            }
            else {
                userUpdates[FIELD_NAMES.todayTranslations] = admin.firestore.FieldValue.increment(batchSize);
            }
            // Update user data
            transaction.update(userRef, userUpdates);
            // Add transaction record
            const transactionRef = db.collection(COLLECTIONS.transactions).doc();
            transaction.set(transactionRef, {
                [FIELD_NAMES.transactionUserId]: userId,
                [FIELD_NAMES.transactionType]: 'translation_earning',
                [FIELD_NAMES.transactionAmount]: earningAmount,
                [FIELD_NAMES.transactionDescription]: `Batch completion reward - ${batchSize} translations completed`,
                [FIELD_NAMES.transactionStatus]: 'completed',
                [FIELD_NAMES.transactionDate]: admin.firestore.Timestamp.fromDate(today)
            });
            return {
                success: true,
                earningAmount,
                newTotalTranslations: (userData[FIELD_NAMES.totalTranslations] || 0) + batchSize,
                newWalletBalance: (userData[FIELD_NAMES.wallet] || 0) + earningAmount,
                newTodayTranslations: isNewDay ? batchSize : Math.min((todayTranslations + batchSize), 50)
            };
        });
        console.log(`✅ Translation batch submitted for user ${userId}:`, result);
        return result;
    }
    catch (error) {
        console.error('Error submitting translation batch:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to submit translation batch');
    }
});
/**
 * Optimized function to get admin dashboard data with minimal reads
 * Aggregates multiple collections in a single function call
 */
exports.getAdminDashboardData = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify admin authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Check if user is admin (you can implement your admin check logic here)
        const adminDoc = await db.collection(COLLECTIONS.users).doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        console.log('📊 Getting admin dashboard data...');
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        // Parallel reads for efficiency
        const [usersSnapshot, transactionsSnapshot, withdrawalsSnapshot] = await Promise.all([
            db.collection(COLLECTIONS.users).get(),
            db.collection(COLLECTIONS.transactions)
                .where(FIELD_NAMES.transactionDate, '>=', admin.firestore.Timestamp.fromDate(todayStart))
                .get(),
            db.collection(COLLECTIONS.withdrawals)
                .where('status', '==', 'pending')
                .get()
        ]);
        // Process users data
        let totalUsers = 0;
        let activeUsers = 0;
        let trialUsers = 0;
        let paidUsers = 0;
        let totalWalletBalance = 0;
        usersSnapshot.forEach(doc => {
            var _a;
            const userData = doc.data();
            totalUsers++;
            if (userData[FIELD_NAMES.plan] === 'Trial') {
                trialUsers++;
            }
            else {
                paidUsers++;
            }
            totalWalletBalance += userData[FIELD_NAMES.wallet] || 0;
            // Check if user was active today
            const lastTranslationDate = (_a = userData[FIELD_NAMES.lastTranslationDate]) === null || _a === void 0 ? void 0 : _a.toDate();
            if (lastTranslationDate && lastTranslationDate >= todayStart) {
                activeUsers++;
            }
        });
        // Process transactions data
        let todayEarnings = 0;
        let todayTransactions = 0;
        let todayWithdrawals = 0;
        transactionsSnapshot.forEach(doc => {
            const transactionData = doc.data();
            todayTransactions++;
            if (transactionData[FIELD_NAMES.transactionType] === 'translation_earning') {
                todayEarnings += transactionData[FIELD_NAMES.transactionAmount] || 0;
            }
            else if (transactionData[FIELD_NAMES.transactionType] === 'withdrawal_request') {
                todayWithdrawals += Math.abs(transactionData[FIELD_NAMES.transactionAmount] || 0);
            }
        });
        // Process pending withdrawals
        const pendingWithdrawals = withdrawalsSnapshot.size;
        let pendingWithdrawalAmount = 0;
        withdrawalsSnapshot.forEach(doc => {
            const withdrawalData = doc.data();
            pendingWithdrawalAmount += withdrawalData.amount || 0;
        });
        const result = {
            users: {
                total: totalUsers,
                active: activeUsers,
                trial: trialUsers,
                paid: paidUsers
            },
            financials: {
                totalWalletBalance,
                todayEarnings,
                todayWithdrawals,
                pendingWithdrawals,
                pendingWithdrawalAmount
            },
            activity: {
                todayTransactions,
                activeUsers
            },
            timestamp: today.toISOString()
        };
        console.log('✅ Admin dashboard data retrieved:', result);
        return result;
    }
    catch (error) {
        console.error('Error getting admin dashboard data:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to get admin dashboard data');
    }
});
/**
 * Optimized function to get user transactions with pagination
 * Reduces reads by implementing server-side pagination
 */
exports.getUserTransactions = functions.https.onCall(async (data, context) => {
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        const { limit = 20, lastDocId = null, type = null } = data;
        console.log(`📊 Getting transactions for user: ${userId}, limit: ${limit}`);
        // Build query
        let query = db.collection(COLLECTIONS.transactions)
            .where(FIELD_NAMES.transactionUserId, '==', userId)
            .orderBy(FIELD_NAMES.transactionDate, 'desc')
            .limit(limit);
        // Add type filter if specified
        if (type) {
            query = db.collection(COLLECTIONS.transactions)
                .where(FIELD_NAMES.transactionUserId, '==', userId)
                .where(FIELD_NAMES.transactionType, '==', type)
                .orderBy(FIELD_NAMES.transactionDate, 'desc')
                .limit(limit);
        }
        // Add pagination if lastDocId is provided
        if (lastDocId) {
            const lastDoc = await db.collection(COLLECTIONS.transactions).doc(lastDocId).get();
            if (lastDoc.exists) {
                query = query.startAfter(lastDoc);
            }
        }
        const snapshot = await query.get();
        const transactions = snapshot.docs.map(doc => {
            var _a, _b;
            return ({
                id: doc.id,
                type: doc.data()[FIELD_NAMES.transactionType],
                amount: doc.data()[FIELD_NAMES.transactionAmount],
                description: doc.data()[FIELD_NAMES.transactionDescription],
                status: doc.data()[FIELD_NAMES.transactionStatus],
                date: (_b = (_a = doc.data()[FIELD_NAMES.transactionDate]) === null || _a === void 0 ? void 0 : _a.toDate()) === null || _b === void 0 ? void 0 : _b.toISOString()
            });
        });
        const result = {
            transactions,
            hasMore: snapshot.docs.length === limit,
            lastDocId: snapshot.docs.length > 0 ? snapshot.docs[snapshot.docs.length - 1].id : null
        };
        console.log(`✅ Retrieved ${transactions.length} transactions for user ${userId}`);
        return result;
    }
    catch (error) {
        console.error('Error getting user transactions:', error);
        throw new functions.https.HttpsError('internal', 'Failed to get user transactions');
    }
});
/**
 * Optimized function to process withdrawal request with validation
 * Combines multiple operations in a single transaction
 */
exports.processWithdrawalRequest = functions.https.onCall(async (data, context) => {
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        const { amount, bankDetails } = data;
        if (!amount || amount < 50) {
            throw new functions.https.HttpsError('invalid-argument', 'Minimum withdrawal amount is ₹50');
        }
        console.log(`📊 Processing withdrawal request for user: ${userId}, amount: ${amount}`);
        // Use transaction for atomicity
        const result = await db.runTransaction(async (transaction) => {
            // Get user data
            const userRef = db.collection(COLLECTIONS.users).doc(userId);
            const userDoc = await transaction.get(userRef);
            if (!userDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'User not found');
            }
            const userData = userDoc.data();
            const currentWallet = userData[FIELD_NAMES.wallet] || 0;
            // Validate sufficient balance
            if (currentWallet < amount) {
                throw new functions.https.HttpsError('failed-precondition', 'Insufficient wallet balance');
            }
            // Validate bank details
            if (!bankDetails || !bankDetails.accountNumber || !bankDetails.ifscCode) {
                throw new functions.https.HttpsError('invalid-argument', 'Valid bank details required');
            }
            // Debit amount from wallet
            transaction.update(userRef, {
                [FIELD_NAMES.wallet]: admin.firestore.FieldValue.increment(-amount)
            });
            // Create withdrawal record
            const withdrawalRef = db.collection(COLLECTIONS.withdrawals).doc();
            transaction.set(withdrawalRef, {
                userId,
                amount,
                bankDetails,
                status: 'pending',
                date: admin.firestore.Timestamp.now(),
                createdAt: admin.firestore.Timestamp.now()
            });
            // Add transaction record
            const transactionRef = db.collection(COLLECTIONS.transactions).doc();
            transaction.set(transactionRef, {
                [FIELD_NAMES.transactionUserId]: userId,
                [FIELD_NAMES.transactionType]: 'withdrawal_request',
                [FIELD_NAMES.transactionAmount]: -amount,
                [FIELD_NAMES.transactionDescription]: `Withdrawal request submitted - ₹${amount} debited from wallet`,
                [FIELD_NAMES.transactionStatus]: 'completed',
                [FIELD_NAMES.transactionDate]: admin.firestore.Timestamp.now()
            });
            return {
                success: true,
                withdrawalId: withdrawalRef.id,
                newWalletBalance: currentWallet - amount
            };
        });
        console.log(`✅ Withdrawal request processed for user ${userId}:`, result);
        return result;
    }
    catch (error) {
        console.error('Error processing withdrawal request:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to process withdrawal request');
    }
});
//# sourceMappingURL=index.js.map