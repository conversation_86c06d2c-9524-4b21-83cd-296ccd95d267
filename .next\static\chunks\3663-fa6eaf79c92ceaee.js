"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3663],{3663:(e,a,t)=>{t.d(a,{Dt:()=>c,Qy:()=>s,bQ:()=>g,bj:()=>u,cb:()=>o,initializeTranslationSystem:()=>d,jQ:()=>T});let n={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_",TRANSLATION_INDEX:"instra_translation_index",TOTAL_TRANSLATIONS:"instra_total_translations",LAST_PROCESSED:"instra_translation_last_processed"},o=[{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"chinese",name:"Chinese (Simplified)",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"turkish",name:"Turkish",flag:"\uD83C\uDDF9\uD83C\uDDF7"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"},{code:"swedish",name:"Swedish",flag:"\uD83C\uDDF8\uD83C\uDDEA"},{code:"polish",name:"Polish",flag:"\uD83C\uDDF5\uD83C\uDDF1"},{code:"ukrainian",name:"Ukrainian",flag:"\uD83C\uDDFA\uD83C\uDDE6"},{code:"greek",name:"Greek",flag:"\uD83C\uDDEC\uD83C\uDDF7"},{code:"hebrew",name:"Hebrew",flag:"\uD83C\uDDEE\uD83C\uDDF1"},{code:"vietnamese",name:"Vietnamese",flag:"\uD83C\uDDFB\uD83C\uDDF3"},{code:"thai",name:"Thai",flag:"\uD83C\uDDF9\uD83C\uDDED"}];function r(e){let a=[];return Array.isArray(e)&&e.forEach((e,t)=>{let n=e.English||e.english;n&&a.push({id:"translation_".concat(t,"_").concat(Date.now()),english:n,hindi:e.Hindi||e.hindi,spanish:e.Spanish||e.spanish,french:e.French||e.french,german:e.German||e.german,italian:e.Italian||e.italian,portuguese:e.Portuguese||e.portuguese,russian:e.Russian||e.russian,arabic:e.Arabic||e.arabic,chinese:e["Chinese (Simplified)"]||e.chinese,japanese:e.Japanese||e.japanese,korean:e.Korean||e.korean,turkish:e.Turkish||e.turkish,dutch:e.Dutch||e.dutch,swedish:e.Swedish||e.swedish,polish:e.Polish||e.polish,ukrainian:e.Ukrainian||e.ukrainian,greek:e.Greek||e.greek,hebrew:e.Hebrew||e.hebrew,vietnamese:e.Vietnamese||e.vietnamese,thai:e.Thai||e.thai,category:"General",batchIndex:Math.floor(a.length/60)})}),a}function l(e){let a=function(e){try{let a=localStorage.getItem("".concat(n.BATCH_PREFIX).concat(e));if(!a)return null;let t=JSON.parse(a);if(Date.now()-t.lastUpdated>864e5)return localStorage.removeItem("".concat(n.BATCH_PREFIX).concat(e)),null;return t}catch(a){return console.error("Error loading translation batch ".concat(e,":"),a),null}}(e);return a?a.translations:[]}function c(){let e=parseInt(localStorage.getItem(n.TOTAL_TRANSLATIONS)||"0"),a=parseInt(localStorage.getItem(n.CURRENT_BATCH)||"0"),t=Math.ceil(e/60),o=l(a),r=function(){try{let e=0;for(let a in localStorage)localStorage.hasOwnProperty(a)&&(e+=localStorage[a].length+a.length);let a=e/5242880*100;return{used:e,total:5242880,percentage:a}}catch(e){return{used:0,total:5242880,percentage:0}}}(),c=Object.keys(localStorage).filter(e=>e.startsWith(n.BATCH_PREFIX)).length;return{totalTranslations:e,currentBatch:a,totalBatches:t,translationsInCurrentBatch:o.length,storageUsage:r,cachedBatches:c}}function i(){try{let e=parseInt(localStorage.getItem(n.CURRENT_BATCH)||"0");Object.keys(localStorage).forEach(a=>{a.startsWith(n.BATCH_PREFIX)&&parseInt(a.replace(n.BATCH_PREFIX,""))!==e&&localStorage.removeItem(a)}),console.log("\uD83E\uDDF9 Force cleanup completed - removed all batches except current")}catch(e){console.error("Error in force cleanup:",e)}}function s(){Object.keys(localStorage).forEach(e=>{(e.startsWith(n.BATCH_PREFIX)||Object.values(n).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all translation storage")}async function g(){try{let e=await fetch("/instradata.json");if(!e.ok)throw Error("Failed to load translations: ".concat(e.statusText));let a=await e.json();console.log("Raw translation data loaded:",a.length,"entries");let t=a.slice(0,60);return console.log("Using minimal dataset:",t.length,"entries (optimized for daily usage)"),r(t)}catch(e){throw console.error("Error loading translations from file:",e),e}}async function h(){try{let e=l(parseInt(localStorage.getItem(n.CURRENT_BATCH)||"0"));if(e.length>=50)return console.log("✅ Using cached translations (fast load)"),e;console.log("\uD83D\uDCE6 Loading minimal translation data...");let a=await g();return localStorage.setItem(n.TOTAL_TRANSLATIONS,a.length.toString()),localStorage.setItem(n.CURRENT_BATCH,"0"),localStorage.setItem(n.LAST_PROCESSED,Date.now().toString()),function(e,a){try{var t=0;try{let e=Object.keys(localStorage).filter(e=>e.startsWith(n.BATCH_PREFIX)).sort((e,a)=>{let t=parseInt(e.replace(n.BATCH_PREFIX,"")),o=parseInt(a.replace(n.BATCH_PREFIX,""));return t-o});e.length>=1&&e.slice(0,e.length-1+1).forEach(e=>{let a=parseInt(e.replace(n.BATCH_PREFIX,""));a!==t&&(localStorage.removeItem(e),console.log("\uD83D\uDDD1️ Removed old batch ".concat(a," to free space")))})}catch(e){console.error("Error cleaning up old batches:",e)}let o={batchNumber:e,translations:a,totalTranslations:a.length,lastUpdated:Date.now()},r=JSON.stringify(o),l="".concat(n.BATCH_PREFIX).concat(e);!function(e){try{let a="storage_test_key",t="x".repeat(e);return localStorage.setItem(a,t),localStorage.removeItem(a),!0}catch(e){return!1}}(r.length)&&(console.warn("Not enough localStorage space for batch ".concat(e,", cleaning up...")),i()),localStorage.setItem(l,r),console.log("✅ Saved batch ".concat(e," (").concat(Math.round(r.length/1024),"KB)"))}catch(t){t instanceof Error&&"QuotaExceededError"===t.name?(console.error("❌ LocalStorage quota exceeded for batch ".concat(e)),function(e,a){try{console.log("\uD83D\uDEA8 Handling quota exceeded error..."),i();let t={batchNumber:e,translations:a.slice(0,25),totalTranslations:25,lastUpdated:Date.now()};localStorage.setItem("".concat(n.BATCH_PREFIX).concat(e),JSON.stringify(t)),console.log("⚠️ Saved reduced batch ".concat(e," with 25 translations due to quota limits"))}catch(e){console.error("Failed to handle quota exceeded error:",e),s()}}(e,a)):console.error("Error saving translation batch ".concat(e,":"),t)}}(0,a),console.log("✅ Fast initialization complete: ".concat(a.length," translations loaded")),a}catch(e){return console.error("❌ Error in fast initialization:",e),[]}}async function d(){return h()}async function u(e){let a=new Date().toDateString(),t="work_data_".concat(e,"_").concat(a);try{let a=localStorage.getItem(t);if(a){let e=JSON.parse(a);return console.log("✅ Using cached work data (instant load)"),e}console.log("\uD83D\uDCE6 Loading fresh work data for today...");let[n,o,r]=await Promise.all([f(),m(e),p(e)]),l=r.todayTranslations<50&&!r.planExpired,c={translations:n,userSettings:o,todayProgress:r,canWork:l,cachedAt:Date.now()};return localStorage.setItem(t,JSON.stringify(c)),console.log("✅ Work data cached for today: ".concat(n.length," translations")),c}catch(e){return console.error("❌ Error loading work data:",e),{translations:[],userSettings:{plan:"Trial",earningPerBatch:25,hasQuickAdvantage:!1},todayProgress:{todayTranslations:0,totalTranslations:0,planExpired:!1},canWork:!1}}}async function f(){let e=await fetch("/instradata.json");return r((await e.json()).sort(()=>.5-Math.random()).slice(0,60))}async function m(e){let{getUserVideoSettings:a,getUserData:n}=await Promise.all([t.e(2992),t.e(7416),t.e(5181),t.e(3592),t.e(6104)]).then(t.bind(t,3592)),[o,r]=await Promise.all([a(e),n(e)]),l=null!=r&&!!r.quickTranslationAdvantageExpiry&&new Date(r.quickTranslationAdvantageExpiry)>new Date,c=l&&(null==r?void 0:r.quickTranslationAdvantageExpiry)?Math.ceil((new Date(r.quickTranslationAdvantageExpiry).getTime()-Date.now())/864e5):0;return{plan:o.plan,earningPerBatch:o.earningPerBatch,hasQuickAdvantage:l,copyPasteDaysRemaining:c}}async function p(e){let{getVideoCountData:a,isUserPlanExpired:n}=await Promise.all([t.e(2992),t.e(7416),t.e(5181),t.e(3592),t.e(6104)]).then(t.bind(t,3592)),[o,r]=await Promise.all([a(e),n(e)]);return{todayTranslations:o.todayTranslations,totalTranslations:o.totalTranslations,planExpired:r.expired,daysLeft:r.daysLeft,activeDays:r.activeDays}}function T(){let e=Math.floor(Math.random()*o.length);return o[e].code}}}]);