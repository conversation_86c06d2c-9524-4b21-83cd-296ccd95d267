(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,2454],{1469:(e,a,s)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),!function(e,a){for(var s in a)Object.defineProperty(e,s,{enumerable:!0,get:a[s]})}(a,{default:function(){return c},getImageProps:function(){return i}});let t=s(8229),r=s(8883),o=s(3063),l=t._(s(1193));function i(e){let{props:a}=(0,r.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(a))void 0===s&&delete a[e];return{props:a}}let c=o.Image},2439:(e,a,s)=>{"use strict";s.d(a,{S3:()=>m,i7:()=>c,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>n});var t=s(6104),r=s(5317);let o={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},l={users:"users"};class i{static async calculateActiveDays(e){try{var a,s;let o,c=await (0,r.x7)((0,r.H9)(t.db,l.users,e));if(!c.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let n=c.data(),d=(null==(a=n.joinedDate)?void 0:a.toDate())||new Date,u=null==(s=n.lastActiveDayUpdate)?void 0:s.toDate(),m=n.activeDays||0,f=n.plan||"Trial",h=new Date,g=h.toDateString(),p=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(m)),console.log("   - Last update: ".concat(p||"Never")),console.log("   - Today: ".concat(g)),console.log("   - Plan: ".concat(f)),p===g)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:m,shouldUpdate:!1,isNewDay:!1};if("Admin"===f)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await i.updateLastActiveDayUpdate(e),{activeDays:m,shouldUpdate:!1,isNewDay:!0};if(await i.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await i.updateLastActiveDayUpdate(e),{activeDays:m,shouldUpdate:!1,isNewDay:!0};return o="Trial"===f?Math.floor((h.getTime()-d.getTime())/864e5)+1:m+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(m," → ").concat(o)),{activeDays:o,shouldUpdate:o!==m,isNewDay:!0}}catch(a){return console.error("Error calculating active days for user ".concat(e,":"),a),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let a=await i.calculateActiveDays(e);if(a.shouldUpdate){let s=(0,r.H9)(t.db,l.users,e);await (0,r.mZ)(s,{[o.activeDays]:a.activeDays,[o.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(a.activeDays))}else a.isNewDay&&await i.updateLastActiveDayUpdate(e);return a.activeDays}catch(a){throw console.error("Error updating active days for user ".concat(e,":"),a),a}}static async updateLastActiveDayUpdate(e){try{let a=(0,r.H9)(t.db,l.users,e);await (0,r.mZ)(a,{[o.lastActiveDayUpdate]:r.Dc.now()})}catch(a){console.error("Error updating last active day timestamp for user ".concat(e,":"),a)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:a}=await s.e(9567).then(s.bind(s,9567));return await a(e,new Date)}catch(a){return console.error("Error checking leave status for user ".concat(e,":"),a),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,r.getDocs)((0,r.collection)(t.db,l.users)),a=0,s=0,o=0;for(let t of e.docs)try{a++;let e=await i.calculateActiveDays(t.id);(e.shouldUpdate||e.isNewDay)&&(await i.updateUserActiveDays(t.id),e.shouldUpdate&&s++)}catch(e){o++,console.error("Error processing active days for user ".concat(t.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(a," users")),console.log("   - Updated: ".concat(s," users")),console.log("   - Errors: ".concat(o," users")),{processed:a,updated:s,errors:o}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let a=await (0,r.x7)((0,r.H9)(t.db,l.users,e));if(!a.exists())return 0;return a.data().activeDays||0}catch(a){return console.error("Error getting active days for user ".concat(e,":"),a),0}}static async initializeActiveDaysForNewUser(e){try{let a=(0,r.H9)(t.db,l.users,e);await (0,r.mZ)(a,{[o.activeDays]:1,[o.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(a){throw console.error("Error initializing active days for user ".concat(e,":"),a),a}}}let c=i.calculateActiveDays,n=i.updateUserActiveDays,d=i.processAllUsersActiveDays,u=i.getUserActiveDays,m=i.initializeActiveDaysForNewUser},3587:(e,a,s)=>{Promise.resolve().then(s.bind(s,6616))},6616:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>p});var t=s(5155),r=s(2115),o=s(6874),l=s.n(o),i=s(6766),c=s(3004),n=s(5317),d=s(6104),u=s(6681),m=s(3592),f=s(2439),h=s(4752),g=s.n(h);function p(){let{user:e,loading:a}=(0,u.hD)(),[s,o]=(0,r.useState)({name:"",email:"",mobile:"",password:"",confirmPassword:"",referralCode:""}),[h,p]=(0,r.useState)(!1),[y,w]=(0,r.useState)(!1),[v,b]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("ref");e&&o(a=>({...a,referralCode:e}))},[]);let x=e=>{let{name:a,value:s}=e.target;o(e=>({...e,[a]:s}))},D=()=>{let{name:e,email:a,mobile:t,password:r,confirmPassword:o}=s;if(!e||!a||!t||!r||!o)throw Error("Please fill in all required fields");if(e.length<2)throw Error("Name must be at least 2 characters long");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))throw Error("Please enter a valid email address");if(!/^[6-9]\d{9}$/.test(t))throw Error("Please enter a valid 10-digit mobile number");if(r.length<6)throw Error("Password must be at least 6 characters long");if(r!==o)throw Error("Passwords do not match")},N=async e=>{e.preventDefault();try{D(),p(!0),console.log("Creating user with email and password...");let e=(await (0,c.eJ)(d.j2,s.email,s.password)).user;console.log("Firebase Auth user created successfully:",e.uid),console.log("Generating referral code...");let a=Date.now().toString().slice(-4),t=Math.random().toString(36).substring(2,4).toUpperCase(),r="TN".concat(a).concat(t);console.log("Generated referral code:",r);let o={[m.Yr.name]:s.name.trim(),[m.Yr.email]:s.email.toLowerCase(),[m.Yr.mobile]:s.mobile,[m.Yr.referralCode]:r,[m.Yr.referredBy]:s.referralCode||"",[m.Yr.referralBonusCredited]:!1,[m.Yr.plan]:"Trial",[m.Yr.planExpiry]:null,[m.Yr.activeDays]:1,[m.Yr.joinedDate]:n.Dc.now(),[m.Yr.wallet]:0,[m.Yr.totalTranslations]:0,[m.Yr.todayTranslations]:0,[m.Yr.lastTranslationDate]:null,status:"active"};console.log("Creating user document with data:",o),console.log("User UID:",e.uid),console.log("Collection:",m.COLLECTIONS.users),console.log("Document path:","".concat(m.COLLECTIONS.users,"/").concat(e.uid)),console.log("Creating user document in Firestore...");let l=(0,n.H9)(d.db,m.COLLECTIONS.users,e.uid);console.log("Document reference created:",l.path),console.log("About to create document with data:",JSON.stringify(o,null,2));try{console.log("Attempting to create document..."),console.log("User UID:",e.uid),console.log("Document path:",l.path),console.log("Auth user email:",e.email),console.log("Auth user verified:",e.emailVerified),await (0,n.BN)(l,o),console.log("✅ User document created successfully"),await (0,f.S3)(e.uid),console.log("✅ Active days initialized for new user");let a=await (0,n.x7)(l);if(a.exists())console.log("✅ Document verification successful:",a.data()),console.log("✅ Registration completed successfully - both Auth and Firestore created");else throw console.error("❌ Document was not created properly"),Error("User document was not created properly")}catch(e){throw console.error("❌ Firestore setDoc failed:",e),console.error("❌ Firestore error code:",e.code),console.error("❌ Firestore error message:",e.message),console.error("❌ Full error object:",JSON.stringify(e,null,2)),console.error("❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed"),console.error("❌ User account exists but profile is incomplete"),Error("Failed to create user profile: ".concat(e.message,". Your account was created but profile setup failed. Please contact support."))}console.log("User registered successfully. Referral bonus will be processed when upgraded to paid plan."),g().fire({icon:"success",title:"Registration Successful!",text:"Your account and profile have been created successfully. Welcome to Instra Global!",timer:2e3,showConfirmButton:!1}).then(()=>{console.log("✅ Complete registration successful - redirecting to dashboard..."),window.location.href="/dashboard"})}catch(a){console.error("Registration error:",a),console.error("Error code:",a.code),console.error("Error message:",a.message),console.error("Full error object:",JSON.stringify(a,null,2));let e="An error occurred during registration";if(a.message.includes("fill in all"))e=a.message;else if(a.message.includes("Name must be"))e=a.message;else if(a.message.includes("valid email"))e=a.message;else if(a.message.includes("valid 10-digit"))e=a.message;else if(a.message.includes("Password must be"))e=a.message;else if(a.message.includes("Passwords do not match"))e=a.message;else if(a.message.includes("email address is already registered"))e=a.message;else if(a.message.includes("mobile number is already registered"))e=a.message;else switch(a.code){case"auth/email-already-in-use":e="An account with this email already exists";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/weak-password":e="Password is too weak";break;default:e=a.message||"Registration failed"}g().fire({icon:"error",title:"Registration Failed",text:e})}finally{p(!1)}};return a?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,t.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,t.jsx)(i.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,t.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Create Account"}),(0,t.jsx)("p",{className:"text-white/80",children:"Join Instra Global and start earning today"})]}),(0,t.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-white font-medium mb-2",children:"Full Name *"}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",value:s.name,onChange:x,className:"form-input",placeholder:"Enter your full name",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address *"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:s.email,onChange:x,className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"mobile",className:"block text-white font-medium mb-2",children:"Mobile Number *"}),(0,t.jsx)("input",{type:"tel",id:"mobile",name:"mobile",value:s.mobile,onChange:x,className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:y?"text":"password",id:"password",name:"password",value:s.password,onChange:x,className:"form-input pr-12",placeholder:"Enter password (min 6 characters)",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>w(!y),className:"password-toggle-btn","aria-label":y?"Hide password":"Show password",children:(0,t.jsx)("i",{className:"fas ".concat(y?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:v?"text":"password",id:"confirmPassword",name:"confirmPassword",value:s.confirmPassword,onChange:x,className:"form-input pr-12",placeholder:"Confirm your password",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>b(!v),className:"password-toggle-btn","aria-label":v?"Hide confirm password":"Show confirm password",children:(0,t.jsx)("i",{className:"fas ".concat(v?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"referralCode",className:"block text-white font-medium mb-2",children:"Referral Code (Optional)"}),(0,t.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:s.referralCode,onChange:x,className:"form-input",placeholder:"Enter referral code if you have one"})]}),(0,t.jsx)("button",{type:"submit",disabled:h,className:"w-full btn-primary flex items-center justify-center mt-6",children:h?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Account..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create Account"]})})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("div",{className:"text-white/60",children:["Already have an account?"," ",(0,t.jsx)(l(),{href:"/login",className:"text-white font-semibold hover:underline",children:"Sign in here"})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)(l(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6766:(e,a,s)=>{"use strict";s.d(a,{default:()=>r.a});var t=s(1469),r=s.n(t)}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,3063,6681,3592,8441,1684,7358],()=>a(3587)),_N_E=e.O()}]);