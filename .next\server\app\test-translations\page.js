(()=>{var e={};e.id=118,e.ids=[118],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8213:(e,t,s)=>{Promise.resolve().then(s.bind(s,53538))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},50664:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-translations\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-translations\\page.tsx","default")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53538:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(60687),i=s(43210),n=s(28879);function a(){let[e,t]=(0,i.useState)([]),[s,a]=(0,i.useState)({totalTranslations:0,currentBatch:0,totalBatches:0,translationsInCurrentBatch:0}),[d,o]=(0,i.useState)(!1),[l,c]=(0,i.useState)(null),x=()=>{a((0,n.Dt)())},u=async()=>{o(!0),c(null);try{let e=await (0,n.bQ)();t(e.slice(0,10)),x()}catch(e){c(e.message)}finally{o(!1)}},p=async()=>{o(!0),c(null);try{let e=await (0,n.initializeTranslationSystem)();t(e.slice(0,10)),x()}catch(e){c(e.message)}finally{o(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen p-4",children:[(0,r.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-white mb-4",children:"Translation System Test"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:s.totalTranslations}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Total Translations"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-400",children:s.currentBatch}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Current Batch"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:s.totalBatches}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Total Batches"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-400",children:s.translationsInCurrentBatch}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"In Current Batch"})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)("button",{onClick:u,disabled:d,className:"btn-primary px-4 py-2 rounded-lg disabled:opacity-50",children:d?"Loading...":"Load from File"}),(0,r.jsx)("button",{onClick:p,disabled:d,className:"btn-primary px-4 py-2 rounded-lg disabled:opacity-50",children:d?"Loading...":"Initialize System"}),(0,r.jsx)("button",{onClick:()=>{(0,n.Qy)(),t([]),x()},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Clear Storage"})]}),l&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:(0,r.jsxs)("p",{className:"text-red-400",children:["Error: ",l]})})]}),(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-bold text-white mb-4",children:["Loaded Translations (",e.length,")"]}),0===e.length?(0,r.jsx)("p",{className:"text-white/60 text-center py-8",children:"No translations loaded. Click a button above to test."}):(0,r.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("h3",{className:"text-white font-medium text-sm mb-2",children:"English Text:"}),(0,r.jsx)("p",{className:"text-white/90 bg-white/5 p-3 rounded border-l-4 border-blue-400",children:e.english})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-3",children:[e.hindi&&(0,r.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Hindi:"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:e.hindi})]}),e.spanish&&(0,r.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Spanish:"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:e.spanish})]}),e.french&&(0,r.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"French:"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:e.french})]}),e.german&&(0,r.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"German:"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:e.german})]}),e.italian&&(0,r.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Italian:"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:e.italian})]}),e.portuguese&&(0,r.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Portuguese:"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:e.portuguese})]})]}),(0,r.jsxs)("div",{className:"text-xs text-white/60 mt-3 space-y-1",children:[(0,r.jsxs)("p",{children:["ID: ",e.id]}),(0,r.jsxs)("p",{children:["Category: ",e.category]}),(0,r.jsxs)("p",{children:["Batch: ",e.batchIndex]})]})]},e.id))})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64506:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>l});var r=s(65239),i=s(48088),n=s(88170),a=s.n(n),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let l={children:["",{children:["test-translations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,50664)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-translations\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-translations\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-translations/page",pathname:"/test-translations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94645:(e,t,s)=>{Promise.resolve().then(s.bind(s,50664))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[2579,6803,8879],()=>s(64506));module.exports=r})();