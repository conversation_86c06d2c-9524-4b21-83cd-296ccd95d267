(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[118],{4176:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var a=t(5155),i=t(2115),l=t(3663);function d(){let[e,s]=(0,i.useState)([]),[t,d]=(0,i.useState)({totalTranslations:0,currentBatch:0,totalBatches:0,translationsInCurrentBatch:0}),[n,r]=(0,i.useState)(!1),[c,x]=(0,i.useState)(null),h=()=>{d((0,l.Dt)())},m=async()=>{r(!0),x(null);try{let e=await (0,l.bQ)();s(e.slice(0,10)),h()}catch(e){x(e.message)}finally{r(!1)}},o=async()=>{r(!0),x(null);try{let e=await (0,l.initializeTranslationSystem)();s(e.slice(0,10)),h()}catch(e){x(e.message)}finally{r(!1)}};return(0,i.useEffect)(()=>{h()},[]),(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-white mb-4",children:"Translation System Test"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:t.totalTranslations}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Total Translations"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-400",children:t.currentBatch}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Current Batch"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:t.totalBatches}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Total Batches"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-400",children:t.translationsInCurrentBatch}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"In Current Batch"})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)("button",{onClick:m,disabled:n,className:"btn-primary px-4 py-2 rounded-lg disabled:opacity-50",children:n?"Loading...":"Load from File"}),(0,a.jsx)("button",{onClick:o,disabled:n,className:"btn-primary px-4 py-2 rounded-lg disabled:opacity-50",children:n?"Loading...":"Initialize System"}),(0,a.jsx)("button",{onClick:()=>{(0,l.Qy)(),s([]),h()},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Clear Storage"})]}),c&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:(0,a.jsxs)("p",{className:"text-red-400",children:["Error: ",c]})})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-white mb-4",children:["Loaded Translations (",e.length,")"]}),0===e.length?(0,a.jsx)("p",{className:"text-white/60 text-center py-8",children:"No translations loaded. Click a button above to test."}):(0,a.jsx)("div",{className:"space-y-4",children:e.map((e,s)=>(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("h3",{className:"text-white font-medium text-sm mb-2",children:"English Text:"}),(0,a.jsx)("p",{className:"text-white/90 bg-white/5 p-3 rounded border-l-4 border-blue-400",children:e.english})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-3",children:[e.hindi&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Hindi:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.hindi})]}),e.spanish&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Spanish:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.spanish})]}),e.french&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"French:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.french})]}),e.german&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"German:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.german})]}),e.italian&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Italian:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.italian})]}),e.portuguese&&(0,a.jsxs)("div",{className:"bg-white/5 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-white/60 mb-1",children:"Portuguese:"}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:e.portuguese})]})]}),(0,a.jsxs)("div",{className:"text-xs text-white/60 mt-3 space-y-1",children:[(0,a.jsxs)("p",{children:["ID: ",e.id]}),(0,a.jsxs)("p",{children:["Category: ",e.category]}),(0,a.jsxs)("p",{children:["Batch: ",e.batchIndex]})]})]},e.id))})]})]})}},8451:(e,s,t)=>{Promise.resolve().then(t.bind(t,4176))}},e=>{var s=s=>e(e.s=s);e.O(0,[3663,8441,1684,7358],()=>s(8451)),_N_E=e.O()}]);