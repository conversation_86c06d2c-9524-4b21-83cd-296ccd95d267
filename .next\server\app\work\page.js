(()=>{var e={};e.id=4246,e.ids=[4246],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14483:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),l=s(87979),o=s(744),c=s(55986),d=s(3582);s(87087);var x=s(28879);s(27878);var m=s(70038),u=s(98873),h=s(77567);function p({onProgressRestored:e,onRecoveryComplete:t}){let{user:s,loading:i}=(0,l.hD)(),[n,o]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null),[x,u]=(0,a.useState)(!1);return n?(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Checking for Previous Session"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Looking for any unsaved work..."})]})})}):x&&c?(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4",children:(0,r.jsx)("i",{className:"fas fa-history text-yellow-600 text-xl"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Previous Session Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"We found unsaved work from your previous session. Would you like to restore it?"}),(0,r.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,r.jsxs)("p",{className:"mb-2",children:[(0,r.jsx)("strong",{children:"Progress:"})," ",c.completedTranslations,"/50 translations"]}),(0,r.jsxs)("p",{className:"mb-2",children:[(0,r.jsx)("strong",{children:"Last saved:"})," ",new Date(c.lastSaved).toLocaleString()]}),c.userTypedText&&(0,r.jsxs)("p",{className:"mb-2",children:[(0,r.jsx)("strong",{children:"Current text:"}),' "',c.userTypedText.substring(0,50),'..."']})]})}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{onClick:()=>{try{if(s?.uid){m.i.clearWorkProgress();let e=`work_progress_backup_${s.uid}`;localStorage.removeItem(e)}h.A.fire({icon:"info",title:"Starting Fresh",text:"Previous session cleared. Starting a new work session.",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error clearing session:",e)}finally{u(!1),t?.()}},className:"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"Start Fresh"}),(0,r.jsx)("button",{onClick:()=>{if(c)try{m.i.saveWorkProgress(c),e?.(c),h.A.fire({icon:"success",title:"Session Restored!",html:`
          <div class="text-center">
            <p class="mb-3">Your previous work session has been successfully restored.</p>
            <div class="bg-green-50 p-3 rounded-lg">
              <p class="text-sm text-green-700">
                <strong>Progress:</strong> ${c.completedTranslations}/50 translations completed
              </p>
              <p class="text-sm text-green-700">
                <strong>Last saved:</strong> ${new Date(c.lastSaved).toLocaleString()}
              </p>
            </div>
          </div>
        `,confirmButtonText:"Continue Working",confirmButtonColor:"#10b981"})}catch(e){console.error("Error restoring session:",e),h.A.fire({icon:"error",title:"Restoration Failed",text:"Failed to restore your session. Starting fresh."})}finally{u(!1),t?.()}},className:"flex-1 px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 transition-colors",children:"Restore Session"})]})]})})}):null}function g(){let{user:e,loading:t}=(0,l.Nu)(),{hasBlockingNotifications:s,isChecking:i,markAllAsRead:m}=(0,o.J)(e?.uid||null),{isBlocked:g,leaveStatus:f}=(0,c.l)({userId:e?.uid||null,checkInterval:3e4,enabled:!!e});console.log("WorkPage render:",{user:e?.uid,loading:t,hasBlockingNotifications:s,isChecking:i,isLeaveBlocked:g});let[b,j]=(0,a.useState)(null),[v,w]=(0,a.useState)(0),[N,y]=(0,a.useState)(0),[C,S]=(0,a.useState)(0),[k,T]=(0,a.useState)(!1),[P,D]=(0,a.useState)(!1),[A,q]=(0,a.useState)([]),[E,B]=(0,a.useState)(!0),[L,_]=(0,a.useState)(""),[M,R]=(0,a.useState)(""),[$,F]=(0,a.useState)([]),[U,O]=(0,a.useState)(!1),[Y,I]=(0,a.useState)(!1),[W,J]=(0,a.useState)(0),[z,G]=(0,a.useState)(""),[K,H]=(0,a.useState)(!1),[Q,X]=(0,a.useState)(!1),[V,Z]=(0,a.useState)(0),[ee,et]=(0,a.useState)(!1),[es,er]=(0,a.useState)(0),[ea,ei]=(0,a.useState)(!1),[en,el]=(0,a.useState)(""),[eo,ec]=(0,a.useState)(null),[ed,ex]=(0,a.useState)(!1),[em,eu]=(0,a.useState)(!1),[eh,ep]=(0,a.useState)(!1),[eg,ef]=(0,a.useState)(!1),[eb,ej]=(0,a.useState)(!1),[ev,ew]=(0,a.useState)({earningPerBatch:25,plan:"Trial"}),[eN,ey]=(0,a.useState)(null),[eC,eS]=(0,a.useState)(0),[ek,eT]=(0,a.useState)(0),eP=()=>{F([]),ei(!1),el("")},eD=(e=A)=>{if(0===e.length)return;let t=Math.floor(Math.random()*e.length),s=e[t],r=(0,x.jQ)(),a=x.cb.find(e=>e.code===r);j({id:`step_${Date.now()}_${Math.random()}`,englishText:s.english,targetLanguage:r,targetLanguageName:a?.name||"Unknown",targetTranslation:s[r]||"Translation not available",userTypedText:"",selectedLanguage:"",isTypingComplete:!1,isLanguageSelected:!1,isConverted:!1,isSubmitted:!1}),_(""),R(""),F([]),O(!1),G(""),H(!1),X(!1),et(!1),ei(!1),el("")},eA=(0,a.useCallback)(e=>{if(!b||U)return;let t=e.target.value,s=Date.now();0===V&&(Z(s),er(0));let r=e_(t,b.englishText);if(r.length>0&&!Y){let s=r[0];if(t.length>s+1){let r=t.substring(0,s+1);e.target.value=r,_(r),F(e_(r,b.englishText)),ea||(ei(!0),el(`Typing error at position ${s+1}`));return}}if(0===r.length&&!Y&&!ea&&eq(t,s)){_(M),e.target.value=M,et(!0);let t=en.includes("speed")?"Fast Typing Detected!":"Paste Not Allowed!",s=en.includes("speed")?`${en}. Please type at a moderate pace and continue.`:`${en}. Please continue typing manually.`;h.A.fire({icon:"warning",title:t,text:s,timer:2e3,showConfirmButton:!1,toast:!0,position:"top-end"}),setTimeout(()=>{et(!1)},1e3);return}0===r.length&&R(t),_(t),F(r),r.length>0?ea||(ei(!0),el("Typing error detected")):ea&&(ei(!1),el("")),t===b.englishText&&0===r.length&&(O(!0),h.A.fire({icon:"success",title:"Perfect!",text:"Text typed correctly. Now select the target language.",timer:2e3,showConfirmButton:!1})),Z(s),er(t.length)},[b,U,ee,ea,Y,L,M,en,V]),eq=(e,t)=>{let s=t-V,r=e.length-L.length;if(0===V||e.length<4||ea||$.length>0||1>=Math.abs(r))return!1;if(r>5)return console.log("\uD83D\uDEAB Paste detected: More than 5 characters at once"),el("More than 5 characters added at once"),!0;if(r>3&&s<50)return console.log("\uD83D\uDEAB Paste detected: Unrealistic typing speed (>3 chars in <50ms)"),el("Typing speed too fast (possible paste)"),!0;if(r>15)return console.log("\uD83D\uDEAB Paste detected: Large text block"),el("Large text block added instantly"),!0;if(e.length>30&&e===b?.englishText.substring(0,e.length)){let s=t-(0===V?t:V),r=e.length/(s/1e3);if(r>10&&s>1e3)return console.log("\uD83D\uDEAB Paste detected: Perfect match with high speed",{charsPerSecond:r,totalTime:s}),el("Perfect text match with unrealistic speed"),!0}if(r>3){let t=e.substring(L.length);if(t.trim().split(/\s+/).length>=2&&t.includes(" "))return console.log("\uD83D\uDEAB Paste detected: Multiple words added at once"),el("Multiple words added simultaneously"),!0}return!1},eE=(0,a.useCallback)(e=>{!Y&&((e.ctrlKey||e.metaKey)&&"v"===e.key&&(e.preventDefault(),h.A.fire({icon:"warning",title:"Paste Not Allowed!",text:"Keyboard paste shortcuts are disabled. Please continue typing manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1})),e.repeat&&(console.log("\uD83D\uDEAB Long press detected"),"Backspace"!==e.key&&"Delete"!==e.key&&e.preventDefault())),$.length>0&&setTimeout(()=>{0===e_(e.target.value,b?.englishText||"").length&&eP()},10)},[Y,$,b]),eB=(0,a.useCallback)(e=>{Y||(e.preventDefault(),h.A.fire({icon:"warning",title:"Drag & Drop Not Allowed!",text:"Please continue typing the text manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1}))},[Y]),eL=(0,a.useCallback)(e=>{Y||(e.preventDefault(),h.A.fire({icon:"warning",title:"Context Menu Disabled",text:"Right-click menu is disabled to prevent paste operations.",timer:1500}))},[Y]),e_=(e,t)=>{let s=[];for(let r=0;r<e.length;r++)(r>=t.length||e[r]!==t[r])&&s.push(r);return s},eM=e=>{b&&U&&(G(e),e===b.targetLanguage?(H(!0),h.A.fire({icon:"success",title:"Correct Language!",text:"You selected the correct language. Click Convert to see the translation.",timer:2e3,showConfirmButton:!1})):(H(!1),h.A.fire({icon:"error",title:"Wrong Language!",text:`Please select ${b.targetLanguageName} language.`,timer:2e3,showConfirmButton:!1})))},eR=async()=>{if(k&&!P&&!(C<50)){if(g)return void h.A.fire({icon:"warning",title:"Submission Not Available",text:f.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{D(!0);let t=ev.earningPerBatch;for(let t=0;t<50;t++)await (0,d.yx)(e.uid);await (0,d.updateWalletBalance)(e.uid,t),await (0,d.addTransaction)(e.uid,{type:"translation_earning",amount:t,description:"Batch completion reward - 50 translations completed"});let s=Math.min(v+50,50);w(s),y(N+50);let r=new Date().toDateString(),a=`translation_session_${e.uid}_${r}`;localStorage.removeItem(a),S(0),T(!1),ej(!1),ef(!0),h.A.fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:`
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${t} Earned!</p>
            <p class="mb-2">50 translations completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(e){console.error("Error submitting translations:",e),h.A.fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your translations. Please try again."})}finally{D(!1)}}};return t||E||i?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:t?"Loading...":i?"Checking notifications...":"Loading translations..."})]})}):t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading work page..."})]})}):e?s&&e?(0,r.jsx)(u.A,{userId:e.uid,onAllRead:m}):eg&&e?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center shadow-2xl",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("i",{className:"fas fa-check-circle text-6xl text-green-400 mb-4"}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Daily Work Completed! \uD83C\uDF89"}),(0,r.jsx)("p",{className:"text-white/80 text-lg",children:"You've successfully completed your 50 translations for today."})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-xl p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("span",{className:"text-white/70",children:"Today's Earnings:"}),(0,r.jsxs)("span",{className:"text-green-400 font-bold text-xl",children:["₹",ev.earningPerBatch]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("span",{className:"text-white/70",children:"Translations Completed:"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:"50/50"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-white/70",children:"Next Session:"}),(0,r.jsx)("span",{className:"text-yellow-400 font-semibold",children:"Tomorrow"})]})]}),(0,r.jsxs)("div",{className:"text-white/60 text-sm mb-6",children:[(0,r.jsxs)("p",{className:"mb-2",children:[(0,r.jsx)("i",{className:"fas fa-clock mr-2"}),"Your work session is locked until tomorrow"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("i",{className:"fas fa-calendar-alt mr-2"}),"Come back tomorrow for your next 50 translations"]})]}),(0,r.jsxs)("button",{onClick:()=>window.location.href="/dashboard",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105",children:[(0,r.jsx)("i",{className:"fas fa-home mr-2"}),"Go to Dashboard"]})]})}):(0,r.jsxs)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:[em&&(0,r.jsx)(p,{onProgressRestored:e=>{j(e.currentStep),_(e.userTypedText),R(e.userTypedText),G(e.selectedLanguage),O(e.isTypingComplete),S(e.completedTranslations),ec(new Date(e.lastSaved))},onRecoveryComplete:()=>{ep(!0),eu(!1)}}),(0,r.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,r.jsxs)("div",{className:"text-white text-right",children:[(0,r.jsxs)("p",{className:"text-sm",children:["Plan: ",ev.plan]}),(0,r.jsxs)("p",{className:"text-sm",children:["₹",ev.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-blue-400",children:v}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"Today TL"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-green-400",children:N}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"Total TL"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-lg font-bold text-purple-400",children:Math.max(0,50-C)}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"TL Left"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[ek,"/","Trial"===ev.plan?"2":"30"]}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,r.jsx)("p",{className:`text-lg font-bold ${Y?"text-green-400":"text-gray-400"}`,children:Y?W:"0"}),(0,r.jsx)("p",{className:"text-white/80 text-xs",children:"Copy Days"})]})]}),(0,r.jsx)("div",{className:"flex items-center justify-center mt-3",children:ed?(0,r.jsxs)("span",{className:"text-yellow-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-spinner fa-spin mr-1"}),"Saving progress..."]}):eo?(0,r.jsxs)("span",{className:"text-green-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-check mr-1"}),"Last saved: ",eo.toLocaleTimeString()]}):(0,r.jsxs)("span",{className:"text-blue-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-shield-alt mr-1"}),"Auto-save protection enabled"]})})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,r.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,r.jsxs)("button",{onClick:()=>{_(""),R(""),F([]),O(!1),G(""),H(!1),X(!1),et(!1),ei(!1),el(""),Z(0),er(0),h.A.fire({icon:"info",title:"Reset Complete!",text:"You can now start typing again. Please type carefully.",timer:2e3,showConfirmButton:!1})},className:"glass-button px-3 py-1 text-white text-sm",title:"Clear typed text and reset",children:[(0,r.jsx)("i",{className:"fas fa-eraser mr-1"}),"Reset"]})]}),!b&&!E&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-8",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"No Translation Available"}),(0,r.jsx)("p",{className:"text-white/80 mb-6",children:"Unable to load translation data. This could be due to:"}),(0,r.jsxs)("ul",{className:"text-white/70 text-left max-w-md mx-auto mb-6",children:[(0,r.jsx)("li",{className:"mb-2",children:"• Translation data file not found"}),(0,r.jsx)("li",{className:"mb-2",children:"• Network connectivity issues"}),(0,r.jsx)("li",{className:"mb-2",children:"• Server maintenance"})]}),(0,r.jsxs)("button",{onClick:()=>window.location.reload(),className:"btn-primary px-6 py-3 rounded-lg font-semibold",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Retry Loading"]})]})}),b&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold",children:[(0,r.jsx)("i",{className:"fas fa-keyboard mr-2"}),"Step 1: Type the English text below"]}),Y?(0,r.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(b.englishText),h.A.fire({icon:"success",title:"Copied!",text:"English text copied to clipboard",timer:1500,showConfirmButton:!1})},className:"group relative bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 animate-pulse",title:"Copy English text",children:[(0,r.jsx)("i",{className:"fas fa-copy mr-2"}),(0,r.jsx)("span",{className:"text-sm",children:"Copy"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]}):(0,r.jsxs)("div",{className:"text-white/60 text-xs bg-white/10 px-3 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-lock mr-1"}),"Copy disabled - Type manually"]})]}),(0,r.jsxs)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3",children:[(0,r.jsx)("div",{className:"max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent",children:(0,r.jsx)("p",{className:"text-white text-base md:text-lg font-mono leading-relaxed",children:$.length>0&&!Y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"text-green-400",children:b.englishText.substring(0,$[0])}),(0,r.jsx)("span",{className:"bg-red-500 text-white px-1 rounded animate-pulse",children:b.englishText[$[0]]}),(0,r.jsx)("span",{className:"text-white/60",children:b.englishText.substring($[0]+1)})]}):b.englishText})}),(0,r.jsxs)("div",{className:"text-xs text-white/60 mt-2",children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-1"}),$.length>0&&!Y?`Error at highlighted character (position ${$[0]+1})`:"Scroll to see full text if needed"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("textarea",{value:L,onChange:eA,onKeyDown:eE,onDrop:eB,onContextMenu:eL,disabled:U,placeholder:$.length>0?"Fix the highlighted error and continue typing...":Y?"Type or paste the English text here...":"Type the English text here (copy-paste not allowed). Fast typists: please type at moderate speed to avoid triggering anti-paste protection.",className:`w-full h-24 md:h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono text-sm md:text-base leading-relaxed ${$.length>0?"border-red-500":""} ${U?"border-green-500 bg-green-500/10":""}`,onPaste:e=>{Y||(e.preventDefault(),h.A.fire({icon:"warning",title:"Paste Not Allowed!",text:"Please continue typing the text manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1}))},onDragOver:e=>{Y||e.preventDefault()},spellCheck:!1,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off"}),Y&&(0,r.jsxs)("button",{onClick:async()=>{try{let e=await navigator.clipboard.readText();_(e),eA({target:{value:e}}),h.A.fire({icon:"success",title:"Pasted!",text:"Text pasted from clipboard",timer:1500,showConfirmButton:!1})}catch(e){h.A.fire({icon:"error",title:"Paste Failed",text:"Could not access clipboard",timer:1500,showConfirmButton:!1})}},className:"group absolute top-3 right-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-2 px-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 animate-bounce disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:animate-none",title:"Paste from clipboard",disabled:U,children:[(0,r.jsx)("i",{className:"fas fa-paste mr-1"}),(0,r.jsx)("span",{className:"text-xs",children:"Paste"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]})]}),$.length>0&&(0,r.jsxs)("div",{className:"mt-2 text-red-400 text-sm bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),(0,r.jsx)("strong",{children:"Typing Error Detected"})]}),(0,r.jsxs)("div",{className:"text-red-300 text-xs mb-2",children:["Error at position ",$[0]+1,': Expected "',b.englishText[$[0]],'" but got "',L[$[0]]||"nothing",'"']}),(0,r.jsxs)("div",{className:"text-red-200 text-xs",children:[(0,r.jsx)("i",{className:"fas fa-edit mr-1"}),"Edit the text box to correct the mistake, then continue typing."]})]}),U&&(0,r.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Perfect! Text typed correctly."]}),ee&&!Y&&(0,r.jsxs)("div",{className:"mt-2 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-yellow-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),(0,r.jsx)("strong",{children:"Paste Attempt Detected"})]}),(0,r.jsx)("div",{className:"text-yellow-300 text-xs mt-1",children:en.includes("speed")?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-1"}),"Fast typing detected. Please type at a moderate pace. You can continue typing normally."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-clipboard mr-1"}),"Paste operation blocked. Please continue typing manually from where you left off."]})}),(0,r.jsxs)("div",{className:"text-yellow-200 text-xs mt-2",children:[(0,r.jsx)("i",{className:"fas fa-arrow-right mr-1"}),"This message will disappear automatically. Continue typing normally."]})]}),$.length>0&&!Y&&(0,r.jsx)("div",{className:"mt-3 text-center",children:(0,r.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-lg p-3",children:[(0,r.jsxs)("div",{className:"text-blue-400 text-sm font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-lightbulb mr-2"}),"How to Fix the Error:"]}),(0,r.jsxs)("div",{className:"text-blue-300 text-xs space-y-1",children:[(0,r.jsx)("div",{children:"1. Click in the text box and edit the incorrect character"}),(0,r.jsxs)("div",{children:['2. Change it to the correct character: "',b.englishText[$[0]],'"']}),(0,r.jsx)("div",{children:"3. Continue typing the rest of the text"})]})]})}),L&&!U&&0===$.length&&(0,r.jsx)("div",{className:"mt-3 text-center",children:(0,r.jsxs)("button",{onClick:()=>{eA({target:{value:L}})},className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",title:"Check if typed text is correct",children:[(0,r.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Check Text"]})})]}),U&&(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-globe mr-2"}),"Step 2: Select the target language - ",b.targetLanguageName]}),(0,r.jsxs)("select",{value:z,onChange:e=>eM(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:[(0,r.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Select target language..."}),x.cb.map(e=>(0,r.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))]}),z&&!K&&(0,r.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-times-circle mr-1"}),"Wrong language! Please select ",b.targetLanguageName,"."]}),K&&(0,r.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,r.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Correct language selected!"]})]}),K&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("button",{onClick:()=>{b&&K&&(X(!0),j(e=>e?{...e,isConverted:!0}:null))},disabled:Q,className:`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${Q?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"}`,children:[(0,r.jsx)("i",{className:"fas fa-exchange-alt mr-2"}),"Convert to ",b.targetLanguageName]})}),Q&&(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-language mr-2"}),b.targetLanguageName," Translation:"]}),(0,r.jsx)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-green-400",children:(0,r.jsx)("p",{className:"text-white text-lg",children:b.targetTranslation})}),(0,r.jsx)("div",{className:"text-center mt-4",children:(0,r.jsxs)("button",{onClick:()=>{if(!b||!Q)return;if(C>=50)return void h.A.fire({icon:"warning",title:"Daily Limit Reached!",text:"You have already completed 50 translations for today. Please submit your batch to earn rewards.",timer:3e3,showConfirmButton:!1});j(e=>e?{...e,isSubmitted:!0}:null);let t=C+1;S(t);let s=new Date().toDateString(),r=`translation_session_${e.uid}_${s}`;localStorage.setItem(r,t.toString()),t<50?h.A.fire({icon:"success",title:"Translation Submitted!",text:`Progress: ${t}/50 translations completed.`,timer:2e3,showConfirmButton:!1}).then(()=>{eD()}):(T(!0),ej(!0),h.A.fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1}),j(null),X(!1),O(!1),_(""),G(""),H(!1))},disabled:C>=50,className:`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${C>=50?"btn-disabled cursor-not-allowed opacity-50":"btn-success hover:scale-105"}`,children:[(0,r.jsx)("i",{className:"fas fa-check mr-2"}),C>=50?"Daily Limit Reached":"Submit Translation"]})})]}),eb&&!eg&&(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 mb-4 shadow-lg",children:[(0,r.jsx)("h3",{className:"text-white font-bold text-xl mb-2",children:"\uD83C\uDF89 Congratulations! You've completed 50 translations!"}),(0,r.jsx)("p",{className:"text-white/90 mb-4",children:"Click the button below to submit your daily batch and receive your earnings."}),(0,r.jsx)("p",{className:"text-white/80 text-sm",children:"⚠️ After submission, you won't be able to work until tomorrow."})]}),(0,r.jsx)("button",{onClick:eR,disabled:P,className:"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 px-12 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none animate-pulse",children:P?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),"Submitting Final Batch..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-trophy mr-2"}),"Submit Final Batch & Earn ₹",ev.earningPerBatch]})})]}),k&&!eb&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("button",{onClick:eR,disabled:P,className:"btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105",children:[(0,r.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit All 50 Translations & Earn ₹",ev.earningPerBatch]})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("p",{className:"text-white/80",children:["Progress: ",C,"/50 translations completed"]}),(0,r.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:`${C/50*100}%`}})})]})]})]})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Authenticating..."})]})})}},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19645:(e,t,s)=>{Promise.resolve().then(s.bind(s,30766))},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30766:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx","default")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},48060:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["work",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,30766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/work/page",pathname:"/work",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54557:(e,t,s)=>{Promise.resolve().then(s.bind(s,14483))},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[2579,6803,3582,6951,8879],()=>s(48060));module.exports=r})();