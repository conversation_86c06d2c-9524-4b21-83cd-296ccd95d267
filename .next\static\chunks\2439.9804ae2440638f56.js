"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439],{2439:(a,e,t)=>{t.d(e,{S3:()=>u,i7:()=>n,mH:()=>d,nd:()=>y,updateUserActiveDays:()=>l});var s=t(6104),c=t(5317);let o={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},r={users:"users"};class i{static async calculateActiveDays(a){try{var e,t;let o,n=await (0,c.x7)((0,c.H9)(s.db,r.users,a));if(!n.exists())return console.error("User ".concat(a," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let l=n.data(),d=(null==(e=l.joinedDate)?void 0:e.toDate())||new Date,y=null==(t=l.lastActiveDayUpdate)?void 0:t.toDate(),u=l.activeDays||0,D=l.plan||"Trial",v=new Date,p=v.toDateString(),g=y?y.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(a,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(u)),console.log("   - Last update: ".concat(g||"Never")),console.log("   - Today: ".concat(p)),console.log("   - Plan: ".concat(D)),g===p)return console.log("✅ Already updated today for user ".concat(a)),{activeDays:u,shouldUpdate:!1,isNewDay:!1};if("Admin"===D)return console.log("⏭️ Skipping active days increment for admin user ".concat(a)),await i.updateLastActiveDayUpdate(a),{activeDays:u,shouldUpdate:!1,isNewDay:!0};if(await i.isUserOnLeaveToday(a))return console.log("\uD83C\uDFD6️ User ".concat(a," is on leave today, not incrementing active days")),await i.updateLastActiveDayUpdate(a),{activeDays:u,shouldUpdate:!1,isNewDay:!0};return o="Trial"===D?Math.floor((v.getTime()-d.getTime())/864e5)+1:u+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(u," → ").concat(o)),{activeDays:o,shouldUpdate:o!==u,isNewDay:!0}}catch(e){return console.error("Error calculating active days for user ".concat(a,":"),e),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(a){try{let e=await i.calculateActiveDays(a);if(e.shouldUpdate){let t=(0,c.H9)(s.db,r.users,a);await (0,c.mZ)(t,{[o.activeDays]:e.activeDays,[o.lastActiveDayUpdate]:c.Dc.now()}),console.log("✅ Updated active days for user ".concat(a,": ").concat(e.activeDays))}else e.isNewDay&&await i.updateLastActiveDayUpdate(a);return e.activeDays}catch(e){throw console.error("Error updating active days for user ".concat(a,":"),e),e}}static async updateLastActiveDayUpdate(a){try{let e=(0,c.H9)(s.db,r.users,a);await (0,c.mZ)(e,{[o.lastActiveDayUpdate]:c.Dc.now()})}catch(e){console.error("Error updating last active day timestamp for user ".concat(a,":"),e)}}static async isUserOnLeaveToday(a){try{let{isUserOnLeave:e}=await t.e(9567).then(t.bind(t,9567));return await e(a,new Date)}catch(e){return console.error("Error checking leave status for user ".concat(a,":"),e),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let a=await (0,c.getDocs)((0,c.collection)(s.db,r.users)),e=0,t=0,o=0;for(let s of a.docs)try{e++;let a=await i.calculateActiveDays(s.id);(a.shouldUpdate||a.isNewDay)&&(await i.updateUserActiveDays(s.id),a.shouldUpdate&&t++)}catch(a){o++,console.error("Error processing active days for user ".concat(s.id,":"),a)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(e," users")),console.log("   - Updated: ".concat(t," users")),console.log("   - Errors: ".concat(o," users")),{processed:e,updated:t,errors:o}}catch(a){throw console.error("Error in daily active days processing:",a),a}}static async getUserActiveDays(a){try{let e=await (0,c.x7)((0,c.H9)(s.db,r.users,a));if(!e.exists())return 0;return e.data().activeDays||0}catch(e){return console.error("Error getting active days for user ".concat(a,":"),e),0}}static async initializeActiveDaysForNewUser(a){try{let e=(0,c.H9)(s.db,r.users,a);await (0,c.mZ)(e,{[o.activeDays]:1,[o.lastActiveDayUpdate]:c.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(a,": Day 1"))}catch(e){throw console.error("Error initializing active days for user ".concat(a,":"),e),e}}}let n=i.calculateActiveDays,l=i.updateUserActiveDays,d=i.processAllUsersActiveDays,y=i.getUserActiveDays,u=i.initializeActiveDaysForNewUser}}]);