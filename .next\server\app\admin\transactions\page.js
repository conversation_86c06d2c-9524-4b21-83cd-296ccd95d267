(()=>{var e={};e.id=4426,e.ids=[4426],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},8574:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\transactions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\transactions\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33644:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,W:()=>o});var s=r(60687),a=r(43210),n=r.n(a);function i({currentPage:e,totalPages:t,totalItems:r,itemsPerPage:a,onPageChange:i,onItemsPerPageChange:o,showItemsPerPage:l=!0,className:c=""}){let d=Math.min(e*a,r),u=t>1?(()=>{let r=[],s=[];for(let s=Math.max(2,e-2);s<=Math.min(t-1,e+2);s++)r.push(s);return e-2>2?s.push(1,"..."):s.push(1),s.push(...r),e+2<t-1?s.push("...",t):s.push(t),s})():[];return(0,s.jsxs)("div",{className:`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 ${c}`,children:[l&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"Show:"}),(0,s.jsxs)("select",{value:a,onChange:e=>o(Number(e.target.value)),className:"px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:10,children:"10"}),(0,s.jsx)("option",{value:25,children:"25"}),(0,s.jsx)("option",{value:50,children:"50"}),(0,s.jsx)("option",{value:100,children:"100"})]}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:"per page"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-700",children:r>0?(0,s.jsxs)(s.Fragment,{children:["Showing ",(0,s.jsx)("span",{className:"font-medium",children:(e-1)*a+1})," to"," ",(0,s.jsx)("span",{className:"font-medium",children:d})," of"," ",(0,s.jsx)("span",{className:"font-medium",children:r})," results"]}):"No results found"}),t>1&&(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("button",{onClick:()=>i(e-1),disabled:1===e,className:`px-3 py-2 text-sm font-medium rounded-md ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:text-gray-900 hover:bg-gray-100"}`,children:(0,s.jsx)("i",{className:"fas fa-chevron-left"})}),u.map((t,r)=>(0,s.jsx)(n().Fragment,{children:"..."===t?(0,s.jsx)("span",{className:"px-3 py-2 text-sm text-gray-500",children:"..."}):(0,s.jsx)("button",{onClick:()=>i(t),className:`px-3 py-2 text-sm font-medium rounded-md ${e===t?"bg-blue-600 text-white":"text-gray-700 hover:text-gray-900 hover:bg-gray-100"}`,children:t})},r)),(0,s.jsx)("button",{onClick:()=>i(e+1),disabled:e===t,className:`px-3 py-2 text-sm font-medium rounded-md ${e===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:text-gray-900 hover:bg-gray-100"}`,children:(0,s.jsx)("i",{className:"fas fa-chevron-right"})})]})]})}function o(e,t=25){let[r,s]=n().useState(1),[a,i]=n().useState(t),l=Math.ceil(e.length/a),c=(r-1)*a,d=e.slice(c,c+a);return{currentPage:r,setCurrentPage:s,itemsPerPage:a,setItemsPerPage:e=>{i(e),s(1)},totalPages:l,currentItems:d,totalItems:e.length}}},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41772:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),a=r(43210),n=r(85814),i=r.n(n),o=r(87979),l=r(75535),c=r(33784),d=r(3582),u=r(83475),x=r(33644),p=r(77567);function m(){let{user:e,loading:t,isAdmin:r}=(0,o.wC)(),[n,m]=(0,a.useState)([]),[g,h]=(0,a.useState)(!0),[f,y]=(0,a.useState)(""),[b,N]=(0,a.useState)(""),[D,v]=(0,a.useState)(""),j=async()=>{try{h(!0);let e=(0,l.P)((0,l.collection)(c.db,d.COLLECTIONS.transactions),(0,l.My)("date","desc"),(0,l.AB)(500)),t=await (0,l.getDocs)(e),r=[];for(let e of t.docs){let t=e.data(),s="Unknown User",a="<EMAIL>",n="Unknown Mobile";try{let e=await (0,l.getDocs)((0,l.P)((0,l.collection)(c.db,d.COLLECTIONS.users),(0,l._M)("__name__","==",t.userId)));if(!e.empty){let t=e.docs[0].data();s=t.name||"Unknown User",a=t.email||"<EMAIL>",n=t.mobile||"Unknown Mobile"}}catch(e){console.error("Error fetching user data:",e)}r.push({id:e.id,userId:t.userId,userName:s,userEmail:a,userMobile:n,type:t.type,amount:t.amount,description:t.description,date:t.date?.toDate()||new Date,status:t.status||"completed"})}m(r)}catch(e){console.error("Error loading transactions:",e),p.A.fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{h(!1)}},w=n.filter(e=>{let t=!f||e.type===f,r=!b||e.status===b,s=!D||String(e.userName||"").toLowerCase().includes(D.toLowerCase())||String(e.userEmail||"").toLowerCase().includes(D.toLowerCase())||String(e.userMobile||"").toLowerCase().includes(D.toLowerCase())||String(e.description||"").toLowerCase().includes(D.toLowerCase());return t&&r&&s}),{currentPage:S,setCurrentPage:C,itemsPerPage:q,setItemsPerPage:A,totalPages:k,currentItems:P,totalItems:T}=(0,x.W)(w,25),L=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),E=e=>{switch(e){case"video_earning":return"Translate Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},U=e=>{switch(e){case"video_earning":return"fas fa-language text-green-500";case"withdrawal":return"fas fa-download text-red-500";case"bonus":return"fas fa-gift text-yellow-500";case"referral":return"fas fa-users text-blue-500";default:return"fas fa-exchange-alt text-gray-500"}};return t||g?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading transactions..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(i(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Transactions"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",T," | Page ",S," of ",k]}),(0,s.jsxs)("button",{onClick:()=>{if(0===w.length)return void p.A.fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=(0,u.sL)(w);(0,u.Bf)(e,"transactions"),p.A.fire({icon:"success",title:"Export Complete",text:`Exported ${w.length} transactions to CSV file.`,timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:j,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,s.jsx)("input",{type:"text",value:D,onChange:e=>v(e.target.value),placeholder:"Search user, email, mobile, or description...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:f,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Types"}),(0,s.jsx)("option",{value:"video_earning",children:"Translate Earning"}),(0,s.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,s.jsx)("option",{value:"bonus",children:"Bonus"}),(0,s.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,s.jsxs)("select",{value:b,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"All Status"}),(0,s.jsx)("option",{value:"completed",children:"Completed"}),(0,s.jsx)("option",{value:"pending",children:"Pending"}),(0,s.jsx)("option",{value:"failed",children:"Failed"}),(0,s.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:()=>{y(""),N(""),v("")},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"Clear Filters"})})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,s.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[P.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.userMobile})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:`${U(e.type)} mr-2`}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:E(e.type)})]})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.description})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:`text-sm font-medium ${e.amount>0?"text-green-600":"text-red-600"}`,children:[e.amount>0?"+":"",L(e.amount)]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[e.date.toLocaleDateString()," ",e.date.toLocaleTimeString()]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"failed"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id)),0===P.length&&(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:7,className:"px-6 py-12 text-center",children:(0,s.jsxs)("div",{className:"text-gray-500",children:[(0,s.jsx)("i",{className:"fas fa-receipt text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"No transactions found"}),(0,s.jsx)("p",{className:"text-sm",children:0===w.length?"No transactions match your current filters.":"No transactions on this page."})]})})})]})]})}),T>0&&(0,s.jsx)("div",{className:"px-6 py-4 border-t border-gray-200",children:(0,s.jsx)(x.A,{currentPage:S,totalPages:k,totalItems:T,itemsPerPage:q,onPageChange:C,onItemsPerPageChange:A,className:"justify-between"})})]})})]})}},41792:e=>{"use strict";e.exports=require("node:querystring")},50519:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["transactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8574)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\transactions\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\transactions\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/transactions/page",pathname:"/admin/transactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75440:(e,t,r)=>{Promise.resolve().then(r.bind(r,8574))},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83475:(e,t,r)=>{"use strict";function s(e,t,r){if(!e||0===e.length)return void alert("No data to export");let s=r||Object.keys(e[0]),a=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],n=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(t=>{let r=e[t];if(null==r)return"";let s=a.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof r){let e=r.replace(/"/g,'""');return`"${e}"`}return r instanceof Date?`"${r.toLocaleDateString()}"`:"object"==typeof r&&null!==r&&r.toDate?`"${r.toDate().toLocaleDateString()}"`:s&&("number"==typeof r||!isNaN(Number(r)))?`"${r}"`:"number"==typeof r?r.toString():`"${String(r)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(n);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function a(e){return e.map(t=>{let r=0,s=null,a="No",n=t.quickTranslationAdvantageExpiry||t.quickVideoAdvantageExpiry;if(n)try{n instanceof Date?s=n:n.toDate&&"function"==typeof n.toDate?s=n.toDate():s=new Date(n);let i=new Date,o=s.getTime()-i.getTime();a=(r=Math.max(0,Math.ceil(o/864e5)))>0?"Yes":"No",5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}:`,{expiryField:n,expiryFieldType:typeof n,copyPasteExpiryDate:s,copyPasteRemainingDays:r,copyPastePermission:a,hasQuickTranslationAdvantageExpiry:!!t.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!t.quickVideoAdvantageExpiry})}catch(e){console.error(`❌ Error calculating copy-paste days for user ${t.email}:`,e)}else 5>e.indexOf(t)&&console.log(`📊 Export debug for user ${t.email}: No copy-paste expiry field found`);return{"User ID":t.id||"",Name:t.name||"",Email:t.email||"",Mobile:String(t.mobile||""),"Referral Code":t.referralCode||"","Referred By":t.referredBy||"Direct",Plan:t.plan||"","Plan Expiry":t.planExpiry instanceof Date?t.planExpiry.toLocaleDateString():t.planExpiry?new Date(t.planExpiry).toLocaleDateString():"","Active Days":t.activeDays||0,"Total Translations":t.totalTranslations||t.totalVideos||0,"Today Translations":t.todayTranslations||t.todayVideos||0,"Last Translation Date":t.lastTranslationDate instanceof Date?t.lastTranslationDate.toLocaleDateString():t.lastTranslationDate?new Date(t.lastTranslationDate).toLocaleDateString():t.lastVideoDate instanceof Date?t.lastVideoDate.toLocaleDateString():t.lastVideoDate?new Date(t.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":a,"Copy-Paste Remaining Days":r,"Copy-Paste Expiry":s?s.toLocaleDateString():"","Copy-Paste Granted By":t.quickTranslationAdvantageGrantedBy||t.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":t.quickTranslationAdvantageGrantedAt?t.quickTranslationAdvantageGrantedAt instanceof Date?t.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(t.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":t.wallet||0,"Referral Bonus Credited":t.referralBonusCredited?"Yes":"No",Status:t.status||"","Joined Date":t.joinedDate instanceof Date?t.joinedDate.toLocaleDateString():t.joinedDate?new Date(t.joinedDate).toLocaleDateString():"","Joined Time":t.joinedDate instanceof Date?t.joinedDate.toLocaleTimeString():t.joinedDate?new Date(t.joinedDate).toLocaleTimeString():""}})}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function o(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}r.d(t,{Bf:()=>s,Fz:()=>a,Pe:()=>o,dB:()=>i,sL:()=>n})},85168:(e,t,r)=>{Promise.resolve().then(r.bind(r,41772))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2579,6803,3582],()=>r(50519));module.exports=s})();