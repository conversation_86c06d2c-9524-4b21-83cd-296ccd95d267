{"version": 3, "file": "SpanOptions.js", "sourceRoot": "", "sources": ["../../../src/trace/SpanOptions.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TimeInput } from '../common/Time';\nimport { SpanAttributes } from './attributes';\nimport { Link } from './link';\nimport { SpanKind } from './span_kind';\n\n/**\n * Options needed for span creation\n */\nexport interface SpanOptions {\n  /**\n   * The SpanKind of a span\n   * @default {@link SpanKind.INTERNAL}\n   */\n  kind?: SpanKind;\n\n  /** A span's attributes */\n  attributes?: SpanAttributes;\n\n  /** {@link Link}s span to other spans */\n  links?: Link[];\n\n  /** A manually specified start time for the created `Span` object. */\n  startTime?: TimeInput;\n\n  /** The new span should be a root span. (Ignore parent from context). */\n  root?: boolean;\n}\n"]}