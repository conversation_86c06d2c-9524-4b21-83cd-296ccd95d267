'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/useAuth'
import { getAllUsers } from '@/lib/adminDataService'
import { formatUsersForExport, downloadCSV } from '@/lib/csvExport'
import { checkCopyPastePermission } from '@/lib/copyPasteService'

export default function TestCopyPasteExportPage() {
  const { user, loading } = useRequireAuth()
  const [testResults, setTestResults] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const testCopyPasteExport = async () => {
    setIsProcessing(true)
    setTestResults(null)

    try {
      console.log('🧪 Testing copy-paste export functionality...')
      
      // Get all users
      const allUsers = await getAllUsers()
      console.log(`📊 Retrieved ${allUsers.length} users for testing`)

      // Test first 5 users for copy-paste data
      const testUsers = allUsers.slice(0, 5)
      const testResults = []

      for (const user of testUsers) {
        try {
          // Check copy-paste permission using centralized service
          const permission = await checkCopyPastePermission(user.id)
          
          const testResult = {
            email: user.email,
            name: user.name,
            hasQuickTranslationAdvantageExpiry: !!user.quickTranslationAdvantageExpiry,
            hasQuickVideoAdvantageExpiry: !!user.quickVideoAdvantageExpiry,
            quickTranslationAdvantageExpiry: user.quickTranslationAdvantageExpiry,
            quickVideoAdvantageExpiry: user.quickVideoAdvantageExpiry,
            servicePermission: permission.hasPermission,
            serviceDaysRemaining: permission.daysRemaining,
            serviceExpiryDate: permission.expiryDate
          }
          
          testResults.push(testResult)
          console.log(`📋 User ${user.email}:`, testResult)
        } catch (error) {
          console.error(`❌ Error testing user ${user.email}:`, error)
        }
      }

      // Test export formatting
      const exportData = formatUsersForExport(testUsers)
      console.log('📊 Export data sample:', exportData.slice(0, 2))

      // Count users with copy-paste data
      const usersWithCopyPaste = allUsers.filter(user => 
        user.quickTranslationAdvantageExpiry || user.quickVideoAdvantageExpiry
      )

      setTestResults({
        success: true,
        totalUsers: allUsers.length,
        usersWithCopyPaste: usersWithCopyPaste.length,
        testUsers: testResults,
        exportSample: exportData.slice(0, 2)
      })

    } catch (error: any) {
      console.error('❌ Test failed:', error)
      setTestResults({
        success: false,
        error: error.message
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const downloadTestExport = async () => {
    try {
      const allUsers = await getAllUsers()
      const exportData = formatUsersForExport(allUsers)
      downloadCSV(exportData, 'test_copy_paste_export')
      
      alert(`Downloaded test export with ${allUsers.length} users. Check the Copy-Paste columns!`)
    } catch (error) {
      console.error('Export failed:', error)
      alert('Export failed. Check console for details.')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700">
      <div className="max-w-4xl mx-auto">
        <div className="glass-card p-6">
          <h1 className="text-2xl font-bold text-white mb-6">Test Copy-Paste Export</h1>
          
          <div className="space-y-4">
            <div className="flex gap-3">
              <button
                onClick={testCopyPasteExport}
                disabled={isProcessing}
                className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold disabled:opacity-50"
              >
                {isProcessing ? 'Testing...' : 'Test Copy-Paste Data'}
              </button>

              <button
                onClick={downloadTestExport}
                className="bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold"
              >
                Download Test Export
              </button>
            </div>

            {testResults && (
              <div className="p-4 bg-white/10 rounded-lg">
                {testResults.success ? (
                  <div className="text-white space-y-4">
                    <h3 className="font-bold text-green-400">Test Results:</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p><strong>Total Users:</strong> {testResults.totalUsers}</p>
                        <p><strong>Users with Copy-Paste:</strong> {testResults.usersWithCopyPaste}</p>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-bold mb-2">Sample Users Test:</h4>
                      <div className="space-y-2 max-h-60 overflow-y-auto">
                        {testResults.testUsers.map((user: any, index: number) => (
                          <div key={index} className="p-2 bg-white/5 rounded text-sm">
                            <p><strong>{user.email}</strong></p>
                            <p>Service Permission: {user.servicePermission ? 'Yes' : 'No'}</p>
                            <p>Service Days Remaining: {user.serviceDaysRemaining}</p>
                            <p>Has Expiry Field: {user.hasQuickTranslationAdvantageExpiry ? 'Yes' : 'No'}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-bold mb-2">Export Sample:</h4>
                      <div className="text-xs bg-black/20 p-2 rounded overflow-x-auto">
                        <pre>{JSON.stringify(testResults.exportSample, null, 2)}</pre>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-red-400">
                    <h3 className="font-bold">Test Failed:</h3>
                    <p>{testResults.error}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="mt-8 p-4 bg-white/5 rounded-lg">
            <h3 className="text-white font-bold mb-2">Instructions:</h3>
            <ul className="text-white/80 text-sm space-y-1">
              <li>• Click "Test Copy-Paste Data" to analyze first 5 users</li>
              <li>• Click "Download Test Export" to get full CSV with copy-paste data</li>
              <li>• Check console for detailed debugging information</li>
              <li>• Verify Copy-Paste columns in the downloaded CSV</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
