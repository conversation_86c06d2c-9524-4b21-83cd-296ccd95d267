// CSV Export Utility Functions

export function downloadCSV(data: any[], filename: string, headers?: string[]) {
  if (!data || data.length === 0) {
    alert('No data to export')
    return
  }

  // Generate headers from first object if not provided
  const csvHeaders = headers || Object.keys(data[0])

  // Fields that should be treated as text to preserve full numeric values
  const textFields = [
    'Account Number', 'Mobile Number', 'Mobile', 'Phone', 'Contact',
    'User ID', 'Referral Code', 'IFSC Code', 'Bank Account', 'Account No'
  ]

  // Create CSV content
  const csvContent = [
    // Headers
    csvHeaders.join(','),
    // Data rows
    ...data.map(row =>
      csvHeaders.map(header => {
        const value = row[header]
        // Handle different data types
        if (value === null || value === undefined) return ''

        // Check if this field should be treated as text (to preserve large numbers)
        const isTextField = textFields.some(field =>
          header.toLowerCase().includes(field.toLowerCase())
        )

        if (typeof value === 'string') {
          // Escape quotes and wrap in quotes
          const escaped = value.replace(/"/g, '""')
          return `"${escaped}"`
        }

        if (value instanceof Date) {
          return `"${value.toLocaleDateString()}"`
        }

        if (typeof value === 'object' && value !== null && value.toDate) {
          // Handle Firestore Timestamp objects
          return `"${value.toDate().toLocaleDateString()}"`
        }

        // For numeric values that should be treated as text (like account numbers)
        if (isTextField && (typeof value === 'number' || !isNaN(Number(value)))) {
          // Force as text by wrapping in quotes and using string representation
          return `"${value}"`
        }

        // For regular numbers, convert to string normally
        if (typeof value === 'number') {
          return value.toString()
        }

        // For everything else, wrap in quotes to ensure proper CSV formatting
        return `"${String(value)}"`
      }).join(',')
    )
  ].join('\n')

  // Create and download file with UTF-8 BOM for better Excel compatibility
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// Format user data for export
export function formatUsersForExport(users: any[]) {
  return users.map(user => {
    // Calculate copy-paste remaining days with better debugging
    let copyPasteRemainingDays = 0
    let copyPasteExpiryDate = null
    let copyPastePermission = 'No'

    // Check for copy-paste expiry in multiple field names (prioritize new field name)
    const expiryField = user.quickTranslationAdvantageExpiry || user.quickVideoAdvantageExpiry

    if (expiryField) {
      try {
        // Handle both Date objects and Firestore Timestamps
        if (expiryField instanceof Date) {
          copyPasteExpiryDate = expiryField
        } else if (expiryField.toDate && typeof expiryField.toDate === 'function') {
          // Firestore Timestamp
          copyPasteExpiryDate = expiryField.toDate()
        } else if (typeof expiryField === 'string' || typeof expiryField === 'number') {
          copyPasteExpiryDate = new Date(expiryField)
        } else {
          copyPasteExpiryDate = new Date(expiryField)
        }

        const now = new Date()
        const diffTime = copyPasteExpiryDate.getTime() - now.getTime()
        copyPasteRemainingDays = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)))
        copyPastePermission = copyPasteRemainingDays > 0 ? 'Yes' : 'No'

        // Debug logging for first few users
        if (users.indexOf(user) < 5) {
          console.log(`📊 Export debug for user ${user.email}:`, {
            expiryField: expiryField,
            expiryFieldType: typeof expiryField,
            copyPasteExpiryDate: copyPasteExpiryDate,
            copyPasteRemainingDays: copyPasteRemainingDays,
            copyPastePermission: copyPastePermission,
            hasQuickTranslationAdvantageExpiry: !!user.quickTranslationAdvantageExpiry,
            hasQuickVideoAdvantageExpiry: !!user.quickVideoAdvantageExpiry
          })
        }
      } catch (error) {
        console.error(`❌ Error calculating copy-paste days for user ${user.email}:`, error)
      }
    } else {
      // Debug for users without copy-paste permission
      if (users.indexOf(user) < 5) {
        console.log(`📊 Export debug for user ${user.email}: No copy-paste expiry field found`)
      }
    }

    return {
      'User ID': user.id || '',
      'Name': user.name || '',
      'Email': user.email || '',
      'Mobile': String(user.mobile || ''), // Ensure mobile is treated as text
      'Referral Code': user.referralCode || '',
      'Referred By': user.referredBy || 'Direct',
      'Plan': user.plan || '',
      'Plan Expiry': user.planExpiry instanceof Date ? user.planExpiry.toLocaleDateString() : (user.planExpiry ? new Date(user.planExpiry).toLocaleDateString() : ''),
      'Active Days': user.activeDays || 0,
      'Total Translations': user.totalTranslations || user.totalVideos || 0,
      'Today Translations': user.todayTranslations || user.todayVideos || 0,
      'Last Translation Date': user.lastTranslationDate instanceof Date ? user.lastTranslationDate.toLocaleDateString() : (user.lastTranslationDate ? new Date(user.lastTranslationDate).toLocaleDateString() : (user.lastVideoDate instanceof Date ? user.lastVideoDate.toLocaleDateString() : (user.lastVideoDate ? new Date(user.lastVideoDate).toLocaleDateString() : ''))),
      'Copy-Paste Permission': copyPastePermission,
      'Copy-Paste Remaining Days': copyPasteRemainingDays,
      'Copy-Paste Expiry': copyPasteExpiryDate ? copyPasteExpiryDate.toLocaleDateString() : '',
      'Copy-Paste Granted By': user.quickTranslationAdvantageGrantedBy || user.quickVideoAdvantageGrantedBy || '',
      'Copy-Paste Granted At': user.quickTranslationAdvantageGrantedAt ? (user.quickTranslationAdvantageGrantedAt instanceof Date ? user.quickTranslationAdvantageGrantedAt.toLocaleDateString() : new Date(user.quickTranslationAdvantageGrantedAt).toLocaleDateString()) : '',
      'Wallet Balance': user.wallet || 0,
      'Referral Bonus Credited': user.referralBonusCredited ? 'Yes' : 'No',
      'Status': user.status || '',
      'Joined Date': user.joinedDate instanceof Date ? user.joinedDate.toLocaleDateString() : (user.joinedDate ? new Date(user.joinedDate).toLocaleDateString() : ''),
      'Joined Time': user.joinedDate instanceof Date ? user.joinedDate.toLocaleTimeString() : (user.joinedDate ? new Date(user.joinedDate).toLocaleTimeString() : '')
    }
  })
}

// Format transactions for export
export function formatTransactionsForExport(transactions: any[]) {
  return transactions.map(transaction => ({
    'User ID': transaction.userId || '',
    'User Name': transaction.userName || '',
    'User Email': transaction.userEmail || '',
    'User Mobile': String(transaction.userMobile || ''), // Ensure mobile is treated as text
    'Type': transaction.type || '',
    'Amount': transaction.amount || 0,
    'Description': transaction.description || '',
    'Status': transaction.status || '',
    'Date': transaction.date instanceof Date ? transaction.date.toLocaleDateString() : (transaction.date ? new Date(transaction.date).toLocaleDateString() : ''),
    'Time': transaction.date instanceof Date ? transaction.date.toLocaleTimeString() : (transaction.date ? new Date(transaction.date).toLocaleTimeString() : '')
  }))
}

// Format withdrawals for export
export function formatWithdrawalsForExport(withdrawals: any[]) {
  return withdrawals.map(withdrawal => ({
    'User ID': withdrawal.userId || '',
    'User Name': withdrawal.userName || '',
    'User Email': withdrawal.userEmail || '',
    'Mobile Number': String(withdrawal.userMobile || ''), // Ensure mobile is treated as text
    'User Plan': withdrawal.userPlan || '',
    'Active Days': withdrawal.userActiveDays || 0,
    'Wallet Balance': withdrawal.walletBalance || 0,
    'Withdrawal Amount': withdrawal.amount || 0,
    'Account Holder Name': withdrawal.bankDetails?.accountHolderName || '',
    'Bank Name': withdrawal.bankDetails?.bankName || '',
    'Account Number': String(withdrawal.bankDetails?.accountNumber || ''), // Force as text to preserve full number
    'IFSC Code': withdrawal.bankDetails?.ifscCode || '',
    'Status': withdrawal.status || 'pending',
    'Request Date': withdrawal.requestDate instanceof Date ? withdrawal.requestDate.toLocaleDateString() : (withdrawal.requestDate ? new Date(withdrawal.requestDate).toLocaleDateString() : ''),
    'Request Time': withdrawal.requestDate instanceof Date ? withdrawal.requestDate.toLocaleTimeString() : (withdrawal.requestDate ? new Date(withdrawal.requestDate).toLocaleTimeString() : ''),
    'Admin Notes': withdrawal.adminNotes || ''
  }))
}

// Format notifications for export
export function formatNotificationsForExport(notifications: any[]) {
  return notifications.map(notification => ({
    'Title': notification.title,
    'Message': notification.message,
    'Type': notification.type,
    'Target': notification.target,
    'Status': notification.status,
    'Created Date': notification.createdAt instanceof Date ? notification.createdAt.toLocaleDateString() : (notification.createdAt ? new Date(notification.createdAt).toLocaleDateString() : ''),
    'Sent Date': notification.sentAt instanceof Date ? notification.sentAt.toLocaleDateString() : (notification.sentAt ? new Date(notification.sentAt).toLocaleDateString() : '')
  }))
}
