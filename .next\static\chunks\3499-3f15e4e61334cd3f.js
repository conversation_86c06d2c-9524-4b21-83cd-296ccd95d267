"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3499,9567],{6572:(e,t,a)=>{a.d(t,{l:()=>o});var r=a(2115),s=a(9567);function o(e){let{userId:t,checkInterval:a=3e4,enabled:o=!0}=e,[n,c]=(0,r.useState)({blocked:!1,lastChecked:new Date}),[l,i]=(0,r.useState)(!1),d=(0,r.useCallback)(async()=>{if(t&&o)try{i(!0);let e=await (0,s.q8)(t);return c({blocked:e.blocked,reason:e.reason,lastChecked:new Date}),e}catch(e){return console.error("Error checking leave status:",e),c(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{i(!1)}},[t,o]);return(0,r.useEffect)(()=>{t&&o&&d()},[t,o,d]),(0,r.useEffect)(()=>{if(!t||!o||a<=0)return;let e=setInterval(()=>{d()},a);return()=>clearInterval(e)},[t,o,a,d]),{leaveStatus:n,isChecking:l,checkLeaveStatus:d,isBlocked:n.blocked}}},7460:(e,t,a)=>{a.d(t,{J:()=>o});var r=a(2115),s=a(3592);function o(e){let[t,a]=(0,r.useState)(!1),[o,n]=(0,r.useState)(!0);(0,r.useEffect)(()=>{e?c():n(!1)},[e]);let c=async()=>{try{n(!0);let t=await (0,s.iA)(e);a(t)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{n(!1)}};return{hasBlockingNotifications:t,isChecking:o,checkForBlockingNotifications:c,markAllAsRead:()=>{a(!1)}}}},8647:(e,t,a)=>{a.d(t,{A:()=>n});var r=a(5155),s=a(2115),o=a(3592);function n(e){let{userId:t,onAllRead:a}=e,[n,c]=(0,s.useState)([]),[l,i]=(0,s.useState)(0),[d,u]=(0,s.useState)(!0);(0,s.useEffect)(()=>{t&&g()},[t]);let g=async()=>{try{u(!0);let e=await (0,o.AX)(t);c(e),0===e.length&&a()}catch(e){console.error("Error loading notifications:",e),a()}finally{u(!1)}},m=async()=>{let e=n[l];(null==e?void 0:e.id)&&(await (0,o.bA)(e.id,t),l<n.length-1?i(l+1):a())};if(d)return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===n.length)return null;let f=n[l];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(f.type)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,r.jsxs)("p",{className:"text-blue-100 text-sm",children:[l+1," of ",n.length," notifications"]})]})]}),(0,r.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:f.title}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,r.jsx)("p",{className:"text-gray-800 leading-relaxed",children:f.message})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,r.jsxs)("span",{children:["From: ",f.createdBy]}),(0,r.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60)," minutes ago"):t<86400?"".concat(Math.floor(t/3600)," hours ago"):"".concat(Math.floor(t/86400)," days ago")})(f.createdAt)})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[l+1,"/",n.length]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((l+1)/n.length*100,"%")}})})]}),(0,r.jsxs)("button",{onClick:m,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,r.jsx)("i",{className:"fas fa-check"}),(0,r.jsx)("span",{children:l<n.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("i",{className:"fas fa-info-circle"}),(0,r.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}},9567:(e,t,a)=>{a.d(t,{applyUserLeave:()=>d,cancelUserLeave:()=>g,createAdminLeave:()=>n,deleteAdminLeave:()=>l,getAdminLeaves:()=>c,getUserLeaves:()=>u,getUserMonthlyLeaveCount:()=>m,isAdminLeaveDay:()=>i,isUserOnLeave:()=>f,q8:()=>h});var r=a(6104),s=a(5317);let o={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function n(e){try{return(await (0,s.gS)((0,s.collection)(r.db,o.adminLeaves),{...e,date:s.Dc.fromDate(e.date),createdAt:s.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function c(){try{let e=(0,s.P)((0,s.collection)(r.db,o.adminLeaves),(0,s.My)("date","asc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function l(e){try{await (0,s.kd)((0,s.H9)(r.db,o.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function i(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let n=(0,s.P)((0,s.collection)(r.db,o.adminLeaves),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(a))),c=await (0,s.getDocs)(n),l=!c.empty;return l?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",c.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),l}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function d(e){try{let t,a,n,c=new Date,l=c.getFullYear(),i=c.getMonth()+1,d=await m(e.userId,l,i),u="pending";return d<4&&(u="approved",t="system",n=s.Dc.now(),a="Auto-approved: ".concat(d+1,"/").concat(4," monthly leaves used")),{id:(await (0,s.gS)((0,s.collection)(r.db,o.userLeaves),{...e,date:s.Dc.fromDate(e.date),status:u,appliedAt:s.Dc.now(),...t&&{reviewedBy:t},...n&&{reviewedAt:n},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function u(e){try{let t=(0,s.P)((0,s.collection)(r.db,o.userLeaves),(0,s._M)("userId","==",e),(0,s.My)("date","desc"));return(await (0,s.getDocs)(t)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:null==(t=e.data().reviewedAt)?void 0:t.toDate()}})}catch(e){throw console.error("Error getting user leaves:",e),e}}async function g(e){try{await (0,s.kd)((0,s.H9)(r.db,o.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function m(e,t,a){try{let n=new Date(t,a-1,1),c=new Date(t,a,0,23,59,59,999),l=(0,s.P)((0,s.collection)(r.db,o.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(n)),(0,s._M)("date","<=",s.Dc.fromDate(c)));return(await (0,s.getDocs)(l)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function f(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let n=new Date(t);n.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",n.toISOString());let c=(0,s.P)((0,s.collection)(r.db,o.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(a)),(0,s._M)("date","<=",s.Dc.fromDate(n))),l=await (0,s.getDocs)(c),i=!l.empty;return i?console.log("\uD83D\uDC64 Found user leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),i}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function h(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await i(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await f(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}}}]);