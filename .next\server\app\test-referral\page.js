(()=>{var e={};e.id=2215,e.ids=[2215],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3954:(e,r,s)=>{Promise.resolve().then(s.bind(s,39398))},4573:e=>{"use strict";e.exports=require("node:buffer")},5348:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>u});var t=s(65239),i=s(48088),o=s(88170),n=s.n(o),a=s(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);s.d(r,d);let u={children:["",{children:["test-referral",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,39398)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-referral\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-referral\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-referral/page",pathname:"/test-referral",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30411:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(60687),i=s(43210),o=s(87979),n=s(3582);function a(){let{user:e,loading:r}=(0,o.Nu)(),[s,a]=(0,i.useState)(""),[d,u]=(0,i.useState)(null),[l,c]=(0,i.useState)(!1),p=async()=>{if(!s.trim())return void alert("Please enter a user ID");c(!0),u(null);try{let e=await (0,n._I)(s.trim());u(e)}catch(e){u({success:!1,message:`Error: ${e?.message||"Unknown error"}`})}finally{c(!1)}};return r?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,t.jsx)("div",{className:"min-h-screen p-4",children:(0,t.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Referral Bonus"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white mb-2",children:"User ID:"}),(0,t.jsx)("input",{type:"text",value:s,onChange:e=>a(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID to process referral bonus"})]}),(0,t.jsx)("button",{onClick:p,disabled:l,className:"w-full btn-primary py-3 rounded-lg font-semibold disabled:opacity-50",children:l?"Processing...":"Process Referral Bonus"}),d&&(0,t.jsxs)("div",{className:`p-4 rounded-lg ${d.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,t.jsx)("h3",{className:`font-bold ${d.success?"text-green-400":"text-red-400"}`,children:d.success?"Success!":"Failed"}),(0,t.jsx)("p",{className:"text-white mt-2",children:d.message})]})]}),(0,t.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,t.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Enter the user ID of someone who was referred"}),(0,t.jsx)("li",{children:"• User must be on a paid plan (Junior, Senior, Expert)"}),(0,t.jsx)("li",{children:"• User must have been referred by someone"}),(0,t.jsx)("li",{children:"• Referral bonus must not have been credited already"})]})]})]})})})}},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},39398:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-referral\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-referral\\page.tsx","default")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},50810:(e,r,s)=>{Promise.resolve().then(s.bind(s,30411))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[2579,6803,3582],()=>s(5348));module.exports=t})();