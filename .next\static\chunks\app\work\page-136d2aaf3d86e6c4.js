(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4246,7718],{1512:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(5155),r=s(2115),n=s(6874),i=s.n(n),o=s(6681),l=s(7460),c=s(6572),d=s(3592),u=s(9567),m=s(3663),h=s(7718),x=s(5738),g=s(8647),p=s(4752),f=s.n(p);function b(e){let{onProgressRestored:t,onRecoveryComplete:s}=e,{user:n,loading:i}=(0,o.hD)(),[l,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)(null),[m,h]=(0,r.useState)(!1);(0,r.useEffect)(()=>{n&&!i&&g();let e=setTimeout(()=>{l&&(console.log("Session recovery timeout - proceeding without recovery"),c(!1),null==s||s())},1e4);return()=>clearTimeout(e)},[n,i,l,s]);let g=async()=>{try{c(!0);let e=x.i.getWorkProgress();if(e&&e.userId===(null==n?void 0:n.uid)){let t=new Date(e.lastSaved);if((new Date().getTime()-t.getTime())/36e5<24){let s=new Date().toDateString();if(t.toDateString()===s&&e.completedTranslations>0){u(e),h(!0);return}}}let t="work_progress_backup_".concat(null==n?void 0:n.uid),a=localStorage.getItem(t);if(a)try{let e=JSON.parse(a),t=new Date(e.lastSaved),s=new Date().toDateString();if(t.toDateString()===s&&e.completedTranslations>0){u(e),h(!0);return}}catch(e){console.error("Error parsing backup data:",e)}null==s||s()}catch(e){console.error("Error checking for recoverable session:",e),null==s||s()}finally{c(!1)}};return l?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Checking for Previous Session"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Looking for any unsaved work..."})]})})}):m&&d?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4",children:(0,a.jsx)("i",{className:"fas fa-history text-yellow-600 text-xl"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Previous Session Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We found unsaved work from your previous session. Would you like to restore it?"}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,a.jsxs)("p",{className:"mb-2",children:[(0,a.jsx)("strong",{children:"Progress:"})," ",d.completedTranslations,"/50 translations"]}),(0,a.jsxs)("p",{className:"mb-2",children:[(0,a.jsx)("strong",{children:"Last saved:"})," ",new Date(d.lastSaved).toLocaleString()]}),d.userTypedText&&(0,a.jsxs)("p",{className:"mb-2",children:[(0,a.jsx)("strong",{children:"Current text:"}),' "',d.userTypedText.substring(0,50),'..."']})]})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{try{if(null==n?void 0:n.uid){x.i.clearWorkProgress();let e="work_progress_backup_".concat(n.uid);localStorage.removeItem(e)}f().fire({icon:"info",title:"Starting Fresh",text:"Previous session cleared. Starting a new work session.",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error clearing session:",e)}finally{h(!1),null==s||s()}},className:"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"Start Fresh"}),(0,a.jsx)("button",{onClick:()=>{if(d)try{x.i.saveWorkProgress(d),null==t||t(d),f().fire({icon:"success",title:"Session Restored!",html:'\n          <div class="text-center">\n            <p class="mb-3">Your previous work session has been successfully restored.</p>\n            <div class="bg-green-50 p-3 rounded-lg">\n              <p class="text-sm text-green-700">\n                <strong>Progress:</strong> '.concat(d.completedTranslations,'/50 translations completed\n              </p>\n              <p class="text-sm text-green-700">\n                <strong>Last saved:</strong> ').concat(new Date(d.lastSaved).toLocaleString(),"\n              </p>\n            </div>\n          </div>\n        "),confirmButtonText:"Continue Working",confirmButtonColor:"#10b981"})}catch(e){console.error("Error restoring session:",e),f().fire({icon:"error",title:"Restoration Failed",text:"Failed to restore your session. Starting fresh."})}finally{h(!1),null==s||s()}},className:"flex-1 px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 transition-colors",children:"Restore Session"})]})]})})}):null}function y(){let{user:e,loading:t}=(0,o.Nu)(),{hasBlockingNotifications:n,isChecking:p,markAllAsRead:y}=(0,l.J)((null==e?void 0:e.uid)||null),{isBlocked:w,leaveStatus:v}=(0,c.l)({userId:(null==e?void 0:e.uid)||null,checkInterval:3e4,enabled:!!e});console.log("WorkPage render:",{user:null==e?void 0:e.uid,loading:t,hasBlockingNotifications:n,isChecking:p,isLeaveBlocked:w});let[j,N]=(0,r.useState)(null),[k,C]=(0,r.useState)(0),[P,D]=(0,r.useState)(0),[T,S]=(0,r.useState)(0),[E,B]=(0,r.useState)(!1),[L,R]=(0,r.useState)(!1),[A,_]=(0,r.useState)([]),[F,W]=(0,r.useState)(!0),[I,M]=(0,r.useState)(""),[U,Y]=(0,r.useState)(""),[q,O]=(0,r.useState)([]),[G,z]=(0,r.useState)(!1),[H,K]=(0,r.useState)(!1),[V,J]=(0,r.useState)(0),[Z,Q]=(0,r.useState)(""),[X,$]=(0,r.useState)(!1),[ee,et]=(0,r.useState)(!1),[es,ea]=(0,r.useState)(0),[er,en]=(0,r.useState)(!1),[ei,eo]=(0,r.useState)(0),[el,ec]=(0,r.useState)(!1),[ed,eu]=(0,r.useState)(""),[em,eh]=(0,r.useState)(null),[ex,eg]=(0,r.useState)(!1),[ep,ef]=(0,r.useState)(!1),[eb,ey]=(0,r.useState)(!1),[ew,ev]=(0,r.useState)(!1),[ej,eN]=(0,r.useState)(!1),[ek,eC]=(0,r.useState)({earningPerBatch:25,plan:"Trial"}),[eP,eD]=(0,r.useState)(null),[eT,eS]=(0,r.useState)(0),[eE,eB]=(0,r.useState)(0);(0,r.useEffect)(()=>{if(e&&!eb){ef(!0);let e=setTimeout(()=>{console.log("Session recovery timeout - proceeding without recovery"),ey(!0),ef(!1)},15e3);return()=>clearTimeout(e)}e&&eb&&eL()},[e,eb]),(0,r.useEffect)(()=>{B(T>=50),eN(T>=50&&!ew)},[T,ew]),(0,r.useEffect)(()=>{if(!e||!j)return;let t=setInterval(()=>{eW()},1e4);return()=>clearInterval(t)},[e,j,I,Z,G,T]),(0,r.useEffect)(()=>{if(e&&j&&I){let e=setTimeout(()=>{eW()},2e3);return()=>clearTimeout(e)}},[I]),(0,r.useEffect)(()=>{let t=t=>{if(e&&j&&(I||T>0))return eW(),t.preventDefault(),t.returnValue="You have unsaved work progress. Are you sure you want to leave?",t.returnValue};return window.addEventListener("beforeunload",t),()=>window.removeEventListener("beforeunload",t)},[e,j,I,T]),(0,r.useEffect)(()=>{if(!(null==e?void 0:e.uid))return;let t=t=>{let{userId:s,hasPermission:a,timestamp:r,updatedBy:n}=t.detail;s===e.uid&&(console.log("\uD83D\uDCE1 Received copy-paste permission update: ".concat(a," (by ").concat(n,")")),K(a),f().fire({icon:a?"success":"warning",title:a?"Copy-Paste Enabled!":"Copy-Paste Disabled!",text:a?"Copy-paste permission has been enabled by admin. You can now copy and paste text.":"Copy-paste permission has been disabled by admin. Please type text manually.",timer:4e3,showConfirmButton:!0,confirmButtonText:"OK"}))},s=()=>{try{let t="copyPasteUpdate_".concat(e.uid),s=localStorage.getItem(t);if(s){let e=JSON.parse(s);Date.now()-e.timestamp<3e4&&(console.log("\uD83D\uDCE1 Found recent copy-paste update in localStorage: ".concat(e.hasPermission)),K(e.hasPermission),f().fire({icon:e.hasPermission?"success":"warning",title:e.hasPermission?"Copy-Paste Enabled!":"Copy-Paste Disabled!",text:e.hasPermission?"Copy-paste permission has been enabled by admin.":"Copy-paste permission has been disabled by admin.",timer:3e3,showConfirmButton:!1}),localStorage.removeItem(t))}}catch(e){console.error("Error checking for copy-paste updates:",e)}};window.addEventListener("copyPastePermissionChanged",t),s();let a=setInterval(s,5e3);return()=>{window.removeEventListener("copyPastePermissionChanged",t),clearInterval(a)}},[null==e?void 0:e.uid]);let eL=async()=>{try{console.log("\uD83D\uDD0D Checking work access for user:",e.uid);let t=await (0,d.isUserPlanExpired)(e.uid);if(console.log("\uD83D\uDCC5 Plan status result:",t),t.expired){console.log("\uD83D\uDEAB Work access blocked - Plan expired:",t.reason),f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-3">'.concat(t.reason,'</p>\n              <p class="text-sm text-gray-600">\n                Active Days: ').concat(t.activeDays||0," | Days Left: ").concat(t.daysLeft||0,"\n              </p>\n            </div>\n          "),confirmButtonText:"Upgrade Plan",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});return}let s=await (0,d.getVideoCountData)(e.uid);if(console.log("\uD83D\uDCCA Translation data check:",s),s.todayTranslations>=50){console.log("\uD83D\uDEAB Work access blocked - Daily session completed"),ev(!0),f().fire({icon:"info",title:"Daily Session Completed",html:'\n            <div class="text-center">\n              <p class="mb-3">You have already completed your daily session of 50 translations!</p>\n              <p class="text-sm text-gray-600">\n                Translations completed today: '.concat(s.todayTranslations,'/50\n              </p>\n              <p class="text-sm text-green-600 mt-2">\n                Come back tomorrow for your next session.\n              </p>\n            </div>\n          '),confirmButtonText:"Go to Dashboard",allowOutsideClick:!1,allowEscapeKey:!1}).then(()=>{window.location.href="/dashboard"});return}let a=await (0,u.q8)(e.uid);if(console.log("\uD83D\uDCCA Work status result:",a),a.blocked){console.log("\uD83D\uDEAB Work access blocked:",a.reason),f().fire({icon:"warning",title:"Work Not Available",text:a.reason||"Work is currently blocked.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}console.log("✅ Work access allowed, proceeding with fast loading"),await e_()}catch(e){console.error("❌ Error checking work access (allowing work to proceed):",e),await e_()}},eR=async()=>{try{console.log("\uD83D\uDCCA Loading translation data for user:",e.uid);let t=await (0,d.getVideoCountData)(e.uid);console.log("\uD83D\uDCCA Translation data loaded:",t),C(t.todayTranslations),D(t.totalTranslations)}catch(e){console.error("Error loading translation data:",e)}},eA=async()=>{try{let t=await (0,d.getUserVideoSettings)(e.uid);eC({earningPerBatch:t.earningPerBatch,plan:t.plan});let s=await (0,h.checkCopyPastePermission)(e.uid);K(s.hasPermission),J(s.daysRemaining),console.log("Copy-paste permission status:",{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate})}catch(e){console.error("Error loading translation settings:",e)}},e_=async()=>{try{W(!0),console.log("\uD83D\uDE80 Fast loading work page data...");let t=await (0,m.bj)(e.uid);if(!t.canWork){console.log("\uD83D\uDEAB User cannot work today"),ev(!0);return}eC({earningPerBatch:t.userSettings.earningPerBatch,plan:t.userSettings.plan}),K(t.userSettings.hasQuickAdvantage),J(t.userSettings.copyPasteDaysRemaining),C(t.todayProgress.todayTranslations),D(t.todayProgress.totalTranslations),eB(t.todayProgress.activeDays||0);let s=t.translations.map(e=>({english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,arabic:e.arabic,chinese:e.chinese,japanese:e.japanese,korean:e.korean,turkish:e.turkish,dutch:e.dutch,swedish:e.swedish,polish:e.polish,ukrainian:e.ukrainian,greek:e.greek,hebrew:e.hebrew,vietnamese:e.vietnamese,thai:e.thai}));_(s),eI(),j||eY(s),console.log("✅ Fast initialization complete!")}catch(e){console.error("❌ Error in fast initialization:",e),eR(),eA(),eF(),eM()}finally{W(!1)}},eF=async()=>{try{let t=await (0,d.getUserData)(e.uid);if(eD(t),t){try{await (0,d.iF)(e.uid)}catch(e){console.error("Error updating active days:",e)}let s=await (0,d.isUserPlanExpired)(e.uid);eS(s.daysLeft||0),eB(s.activeDays||0),console.log("\uD83D\uDCCA Plan status loaded:",{plan:t.plan,expired:s.expired,daysLeft:s.daysLeft,activeDays:s.activeDays,reason:s.reason})}}catch(e){console.error("Error loading user data:",e)}},eW=async()=>{if(e&&j)try{eg(!0);let t={currentStep:j,userTypedText:I,selectedLanguage:Z,isTypingComplete:G,completedTranslations:T,batchProgress:T/50*100,lastSaved:new Date,userId:e.uid};x.i.saveWorkProgress(t);let s="work_progress_backup_".concat(e.uid);localStorage.setItem(s,JSON.stringify(t)),eh(new Date),console.log("✅ Work progress saved successfully")}catch(e){console.error("❌ Error saving work progress:",e)}finally{eg(!1)}},eI=()=>{let t=new Date().toDateString(),s="translation_session_".concat(e.uid,"_").concat(t),a=localStorage.getItem(s);a&&S(parseInt(a))},eM=async()=>{try{W(!0);let e=null!==j,{initializeTranslationSystem:t}=await Promise.resolve().then(s.bind(s,3663)),a=(await t()).map(e=>({english:e.english,hindi:e.hindi,spanish:e.spanish,french:e.french,german:e.german,italian:e.italian,portuguese:e.portuguese,russian:e.russian,arabic:e.arabic,chinese:e.chinese,japanese:e.japanese,korean:e.korean,turkish:e.turkish,dutch:e.dutch,swedish:e.swedish,polish:e.polish,ukrainian:e.ukrainian,greek:e.greek,hebrew:e.hebrew,vietnamese:e.vietnamese,thai:e.thai}));_(a),e?console.log("\uD83D\uDD04 Using restored translation step"):eY(a)}catch(e){console.error("Error loading translations:",e),f().fire({icon:"error",title:"Loading Error",text:"Failed to load translation data. Please refresh the page."})}finally{W(!1)}},eU=()=>{O([]),ec(!1),eu("")},eY=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A;if(0===e.length)return;let t=Math.floor(Math.random()*e.length),s=e[t],a=(0,m.jQ)(),r=m.cb.find(e=>e.code===a);N({id:"step_".concat(Date.now(),"_").concat(Math.random()),englishText:s.english,targetLanguage:a,targetLanguageName:(null==r?void 0:r.name)||"Unknown",targetTranslation:s[a]||"Translation not available",userTypedText:"",selectedLanguage:"",isTypingComplete:!1,isLanguageSelected:!1,isConverted:!1,isSubmitted:!1}),M(""),Y(""),O([]),z(!1),Q(""),$(!1),et(!1),en(!1),ec(!1),eu("")},eq=(0,r.useCallback)(e=>{if(!j||G)return;let t=e.target.value,s=Date.now();0===es&&(ea(s),eo(0));let a=eK(t,j.englishText);if(a.length>0&&!H){let s=a[0];if(t.length>s+1){let a=t.substring(0,s+1);e.target.value=a,M(a),O(eK(a,j.englishText)),el||(ec(!0),eu("Typing error at position ".concat(s+1)));return}}if(0===a.length&&!H&&!el&&eO(t,s)){M(U),e.target.value=U,en(!0);let t=ed.includes("speed")?"Fast Typing Detected!":"Paste Not Allowed!",s=ed.includes("speed")?"".concat(ed,". Please type at a moderate pace and continue."):"".concat(ed,". Please continue typing manually.");f().fire({icon:"warning",title:t,text:s,timer:2e3,showConfirmButton:!1,toast:!0,position:"top-end"}),setTimeout(()=>{en(!1)},1e3);return}0===a.length&&Y(t),M(t),O(a),a.length>0?el||(ec(!0),eu("Typing error detected")):el&&(ec(!1),eu("")),t===j.englishText&&0===a.length&&(z(!0),f().fire({icon:"success",title:"Perfect!",text:"Text typed correctly. Now select the target language.",timer:2e3,showConfirmButton:!1})),ea(s),eo(t.length)},[j,G,er,el,H,I,U,ed,es]),eO=(e,t)=>{let s=t-es,a=e.length-I.length;if(0===es||e.length<4||el||q.length>0||1>=Math.abs(a))return!1;if(a>5)return console.log("\uD83D\uDEAB Paste detected: More than 5 characters at once"),eu("More than 5 characters added at once"),!0;if(a>3&&s<50)return console.log("\uD83D\uDEAB Paste detected: Unrealistic typing speed (>3 chars in <50ms)"),eu("Typing speed too fast (possible paste)"),!0;if(a>15)return console.log("\uD83D\uDEAB Paste detected: Large text block"),eu("Large text block added instantly"),!0;if(e.length>30&&e===(null==j?void 0:j.englishText.substring(0,e.length))){let s=t-(0===es?t:es),a=e.length/(s/1e3);if(a>10&&s>1e3)return console.log("\uD83D\uDEAB Paste detected: Perfect match with high speed",{charsPerSecond:a,totalTime:s}),eu("Perfect text match with unrealistic speed"),!0}if(a>3){let t=e.substring(I.length);if(t.trim().split(/\s+/).length>=2&&t.includes(" "))return console.log("\uD83D\uDEAB Paste detected: Multiple words added at once"),eu("Multiple words added simultaneously"),!0}return!1},eG=(0,r.useCallback)(e=>{!H&&((e.ctrlKey||e.metaKey)&&"v"===e.key&&(e.preventDefault(),f().fire({icon:"warning",title:"Paste Not Allowed!",text:"Keyboard paste shortcuts are disabled. Please continue typing manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1})),e.repeat&&(console.log("\uD83D\uDEAB Long press detected"),"Backspace"!==e.key&&"Delete"!==e.key&&e.preventDefault())),q.length>0&&setTimeout(()=>{0===eK(e.target.value,(null==j?void 0:j.englishText)||"").length&&eU()},10)},[H,q,j]),ez=(0,r.useCallback)(e=>{H||(e.preventDefault(),f().fire({icon:"warning",title:"Drag & Drop Not Allowed!",text:"Please continue typing the text manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1}))},[H]),eH=(0,r.useCallback)(e=>{H||(e.preventDefault(),f().fire({icon:"warning",title:"Context Menu Disabled",text:"Right-click menu is disabled to prevent paste operations.",timer:1500}))},[H]),eK=(e,t)=>{let s=[];for(let a=0;a<e.length;a++)(a>=t.length||e[a]!==t[a])&&s.push(a);return s},eV=e=>{j&&G&&(Q(e),e===j.targetLanguage?($(!0),f().fire({icon:"success",title:"Correct Language!",text:"You selected the correct language. Click Convert to see the translation.",timer:2e3,showConfirmButton:!1})):($(!1),f().fire({icon:"error",title:"Wrong Language!",text:"Please select ".concat(j.targetLanguageName," language."),timer:2e3,showConfirmButton:!1})))},eJ=async()=>{if(E&&!L&&!(T<50)){if(w)return void f().fire({icon:"warning",title:"Submission Not Available",text:v.reason||"Translation submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});try{R(!0);let t=ek.earningPerBatch;for(let t=0;t<50;t++)await (0,d.yx)(e.uid);await (0,d.updateWalletBalance)(e.uid,t),await (0,d.addTransaction)(e.uid,{type:"translation_earning",amount:t,description:"Batch completion reward - 50 translations completed"});let s=Math.min(k+50,50);C(s),D(P+50);let a=new Date().toDateString(),r="translation_session_".concat(e.uid,"_").concat(a);localStorage.removeItem(r),S(0),B(!1),eN(!1),ev(!0),f().fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:'\n          <div class="text-center">\n            <p class="text-lg font-bold text-green-600 mb-2">₹'.concat(t,' Earned!</p>\n            <p class="mb-2">50 translations completed and submitted</p>\n            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>\n            <p class="text-sm text-blue-600 font-semibold">\n              \uD83C\uDF89 Your daily session is complete! Come back tomorrow for your next session.\n            </p>\n          </div>\n        '),confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(e){console.error("Error submitting translations:",e),f().fire({icon:"error",title:"Submission Failed",text:"There was an error submitting your translations. Please try again."})}finally{R(!1)}}};return t||F||p?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:t?"Loading...":p?"Checking notifications...":"Loading translations..."})]})}):t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading work page..."})]})}):e?n&&e?(0,a.jsx)(g.A,{userId:e.uid,onAllRead:y}):ew&&e?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center shadow-2xl",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("i",{className:"fas fa-check-circle text-6xl text-green-400 mb-4"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Daily Work Completed! \uD83C\uDF89"}),(0,a.jsx)("p",{className:"text-white/80 text-lg",children:"You've successfully completed your 50 translations for today."})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-xl p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("span",{className:"text-white/70",children:"Today's Earnings:"}),(0,a.jsxs)("span",{className:"text-green-400 font-bold text-xl",children:["₹",ek.earningPerBatch]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("span",{className:"text-white/70",children:"Translations Completed:"}),(0,a.jsx)("span",{className:"text-white font-semibold",children:"50/50"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-white/70",children:"Next Session:"}),(0,a.jsx)("span",{className:"text-yellow-400 font-semibold",children:"Tomorrow"})]})]}),(0,a.jsxs)("div",{className:"text-white/60 text-sm mb-6",children:[(0,a.jsxs)("p",{className:"mb-2",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),"Your work session is locked until tomorrow"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("i",{className:"fas fa-calendar-alt mr-2"}),"Come back tomorrow for your next 50 translations"]})]}),(0,a.jsxs)("button",{onClick:()=>window.location.href="/dashboard",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105",children:[(0,a.jsx)("i",{className:"fas fa-home mr-2"}),"Go to Dashboard"]})]})}):(0,a.jsxs)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:[ep&&(0,a.jsx)(b,{onProgressRestored:e=>{N(e.currentStep),M(e.userTypedText),Y(e.userTypedText),Q(e.selectedLanguage),z(e.isTypingComplete),S(e.completedTranslations),eh(new Date(e.lastSaved))},onRecoveryComplete:()=>{ey(!0),ef(!1)}}),(0,a.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)(i(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Translate Text & Earn"}),(0,a.jsxs)("div",{className:"text-white text-right",children:[(0,a.jsxs)("p",{className:"text-sm",children:["Plan: ",ek.plan]}),(0,a.jsxs)("p",{className:"text-sm",children:["₹",ek.earningPerBatch,"/batch (50 translations)"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-2 text-center",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-blue-400",children:k}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Today TL"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-green-400",children:P}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Total TL"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold text-purple-400",children:Math.max(0,50-T)}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"TL Left"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[eE,"/","Trial"===ek.plan?"2":"30"]}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,a.jsx)("p",{className:"text-lg font-bold ".concat(H?"text-green-400":"text-gray-400"),children:H?V:"0"}),(0,a.jsx)("p",{className:"text-white/80 text-xs",children:"Copy Days"})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-center mt-3",children:ex?(0,a.jsxs)("span",{className:"text-yellow-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-spinner fa-spin mr-1"}),"Saving progress..."]}):em?(0,a.jsxs)("span",{className:"text-green-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-check mr-1"}),"Last saved: ",em.toLocaleTimeString()]}):(0,a.jsxs)("span",{className:"text-blue-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-1"}),"Auto-save protection enabled"]})})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-language mr-2"}),"Translate Text & Earn"]}),(0,a.jsxs)("button",{onClick:()=>{M(""),Y(""),O([]),z(!1),Q(""),$(!1),et(!1),en(!1),ec(!1),eu(""),ea(0),eo(0),f().fire({icon:"info",title:"Reset Complete!",text:"You can now start typing again. Please type carefully.",timer:2e3,showConfirmButton:!1})},className:"glass-button px-3 py-1 text-white text-sm",title:"Clear typed text and reset",children:[(0,a.jsx)("i",{className:"fas fa-eraser mr-1"}),"Reset"]})]}),!j&&!F&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-8",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"No Translation Available"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"Unable to load translation data. This could be due to:"}),(0,a.jsxs)("ul",{className:"text-white/70 text-left max-w-md mx-auto mb-6",children:[(0,a.jsx)("li",{className:"mb-2",children:"• Translation data file not found"}),(0,a.jsx)("li",{className:"mb-2",children:"• Network connectivity issues"}),(0,a.jsx)("li",{className:"mb-2",children:"• Server maintenance"})]}),(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"btn-primary px-6 py-3 rounded-lg font-semibold",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Retry Loading"]})]})}),j&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold",children:[(0,a.jsx)("i",{className:"fas fa-keyboard mr-2"}),"Step 1: Type the English text below"]}),H?(0,a.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(j.englishText),f().fire({icon:"success",title:"Copied!",text:"English text copied to clipboard",timer:1500,showConfirmButton:!1})},className:"group relative bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 animate-pulse",title:"Copy English text",children:[(0,a.jsx)("i",{className:"fas fa-copy mr-2"}),(0,a.jsx)("span",{className:"text-sm",children:"Copy"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]}):(0,a.jsxs)("div",{className:"text-white/60 text-xs bg-white/10 px-3 py-2 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-lock mr-1"}),"Copy disabled - Type manually"]})]}),(0,a.jsxs)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3",children:[(0,a.jsx)("div",{className:"max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent",children:(0,a.jsx)("p",{className:"text-white text-base md:text-lg font-mono leading-relaxed",children:q.length>0&&!H?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-green-400",children:j.englishText.substring(0,q[0])}),(0,a.jsx)("span",{className:"bg-red-500 text-white px-1 rounded animate-pulse",children:j.englishText[q[0]]}),(0,a.jsx)("span",{className:"text-white/60",children:j.englishText.substring(q[0]+1)})]}):j.englishText})}),(0,a.jsxs)("div",{className:"text-xs text-white/60 mt-2",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),q.length>0&&!H?"Error at highlighted character (position ".concat(q[0]+1,")"):"Scroll to see full text if needed"]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("textarea",{value:I,onChange:eq,onKeyDown:eG,onDrop:ez,onContextMenu:eH,disabled:G,placeholder:q.length>0?"Fix the highlighted error and continue typing...":H?"Type or paste the English text here...":"Type the English text here (copy-paste not allowed). Fast typists: please type at moderate speed to avoid triggering anti-paste protection.",className:"w-full h-24 md:h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono text-sm md:text-base leading-relaxed ".concat(q.length>0?"border-red-500":""," ").concat(G?"border-green-500 bg-green-500/10":""),onPaste:e=>{H||(e.preventDefault(),f().fire({icon:"warning",title:"Paste Not Allowed!",text:"Please continue typing the text manually.",timer:2e3,toast:!0,position:"top-end",showConfirmButton:!1}))},onDragOver:e=>{H||e.preventDefault()},spellCheck:!1,autoComplete:"off",autoCorrect:"off",autoCapitalize:"off"}),H&&(0,a.jsxs)("button",{onClick:async()=>{try{let e=await navigator.clipboard.readText();M(e),eq({target:{value:e}}),f().fire({icon:"success",title:"Pasted!",text:"Text pasted from clipboard",timer:1500,showConfirmButton:!1})}catch(e){f().fire({icon:"error",title:"Paste Failed",text:"Could not access clipboard",timer:1500,showConfirmButton:!1})}},className:"group absolute top-3 right-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-2 px-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 animate-bounce disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:animate-none",title:"Paste from clipboard",disabled:G,children:[(0,a.jsx)("i",{className:"fas fa-paste mr-1"}),(0,a.jsx)("span",{className:"text-xs",children:"Paste"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]})]}),q.length>0&&(0,a.jsxs)("div",{className:"mt-2 text-red-400 text-sm bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),(0,a.jsx)("strong",{children:"Typing Error Detected"})]}),(0,a.jsxs)("div",{className:"text-red-300 text-xs mb-2",children:["Error at position ",q[0]+1,': Expected "',j.englishText[q[0]],'" but got "',I[q[0]]||"nothing",'"']}),(0,a.jsxs)("div",{className:"text-red-200 text-xs",children:[(0,a.jsx)("i",{className:"fas fa-edit mr-1"}),"Edit the text box to correct the mistake, then continue typing."]})]}),G&&(0,a.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Perfect! Text typed correctly."]}),er&&!H&&(0,a.jsxs)("div",{className:"mt-2 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-yellow-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),(0,a.jsx)("strong",{children:"Paste Attempt Detected"})]}),(0,a.jsx)("div",{className:"text-yellow-300 text-xs mt-1",children:ed.includes("speed")?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-1"}),"Fast typing detected. Please type at a moderate pace. You can continue typing normally."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-clipboard mr-1"}),"Paste operation blocked. Please continue typing manually from where you left off."]})}),(0,a.jsxs)("div",{className:"text-yellow-200 text-xs mt-2",children:[(0,a.jsx)("i",{className:"fas fa-arrow-right mr-1"}),"This message will disappear automatically. Continue typing normally."]})]}),q.length>0&&!H&&(0,a.jsx)("div",{className:"mt-3 text-center",children:(0,a.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"text-blue-400 text-sm font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-lightbulb mr-2"}),"How to Fix the Error:"]}),(0,a.jsxs)("div",{className:"text-blue-300 text-xs space-y-1",children:[(0,a.jsx)("div",{children:"1. Click in the text box and edit the incorrect character"}),(0,a.jsxs)("div",{children:['2. Change it to the correct character: "',j.englishText[q[0]],'"']}),(0,a.jsx)("div",{children:"3. Continue typing the rest of the text"})]})]})}),I&&!G&&0===q.length&&(0,a.jsx)("div",{className:"mt-3 text-center",children:(0,a.jsxs)("button",{onClick:()=>{eq({target:{value:I}})},className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",title:"Check if typed text is correct",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Check Text"]})})]}),G&&(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-globe mr-2"}),"Step 2: Select the target language - ",j.targetLanguageName]}),(0,a.jsxs)("select",{value:Z,onChange:e=>eV(e.target.value),className:"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200",children:[(0,a.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Select target language..."}),m.cb.map(e=>(0,a.jsxs)("option",{value:e.code,className:"bg-gray-800 text-white",children:[e.flag," ",e.name]},e.code))]}),Z&&!X&&(0,a.jsxs)("div",{className:"mt-2 text-red-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-times-circle mr-1"}),"Wrong language! Please select ",j.targetLanguageName,"."]}),X&&(0,a.jsxs)("div",{className:"mt-2 text-green-400 text-sm",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-1"}),"Correct language selected!"]})]}),X&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("button",{onClick:()=>{j&&X&&(et(!0),N(e=>e?{...e,isConverted:!0}:null))},disabled:ee,className:"px-8 py-3 rounded-lg font-semibold transition-all duration-300 ".concat(ee?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:scale-105"),children:[(0,a.jsx)("i",{className:"fas fa-exchange-alt mr-2"}),"Convert to ",j.targetLanguageName]})}),ee&&(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-language mr-2"}),j.targetLanguageName," Translation:"]}),(0,a.jsx)("div",{className:"bg-white/5 p-3 rounded border-l-4 border-green-400",children:(0,a.jsx)("p",{className:"text-white text-lg",children:j.targetTranslation})}),(0,a.jsx)("div",{className:"text-center mt-4",children:(0,a.jsxs)("button",{onClick:()=>{if(!j||!ee)return;if(T>=50)return void f().fire({icon:"warning",title:"Daily Limit Reached!",text:"You have already completed 50 translations for today. Please submit your batch to earn rewards.",timer:3e3,showConfirmButton:!1});N(e=>e?{...e,isSubmitted:!0}:null);let t=T+1;S(t);let s=new Date().toDateString(),a="translation_session_".concat(e.uid,"_").concat(s);localStorage.setItem(a,t.toString()),t<50?f().fire({icon:"success",title:"Translation Submitted!",text:"Progress: ".concat(t,"/50 translations completed."),timer:2e3,showConfirmButton:!1}).then(()=>{eY()}):(B(!0),eN(!0),f().fire({icon:"success",title:"\uD83C\uDF89 All Translations Completed!",text:'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1}),N(null),et(!1),z(!1),M(""),Q(""),$(!1))},disabled:T>=50,className:"px-6 py-3 rounded-lg font-semibold transition-all duration-300 ".concat(T>=50?"btn-disabled cursor-not-allowed opacity-50":"btn-success hover:scale-105"),children:[(0,a.jsx)("i",{className:"fas fa-check mr-2"}),T>=50?"Daily Limit Reached":"Submit Translation"]})})]}),ej&&!ew&&(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 mb-4 shadow-lg",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-xl mb-2",children:"\uD83C\uDF89 Congratulations! You've completed 50 translations!"}),(0,a.jsx)("p",{className:"text-white/90 mb-4",children:"Click the button below to submit your daily batch and receive your earnings."}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"⚠️ After submission, you won't be able to work until tomorrow."})]}),(0,a.jsx)("button",{onClick:eJ,disabled:L,className:"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 px-12 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none animate-pulse",children:L?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),"Submitting Final Batch..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-trophy mr-2"}),"Submit Final Batch & Earn ₹",ek.earningPerBatch]})})]}),E&&!ej&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("button",{onClick:eJ,disabled:L,className:"btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105",children:[(0,a.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Submit All 50 Translations & Earn ₹",ek.earningPerBatch]})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-white/80",children:["Progress: ",T,"/50 translations completed"]}),(0,a.jsx)("div",{className:"w-full bg-white/20 rounded-full h-2 mt-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300",style:{width:"".concat(T/50*100,"%")}})})]})]})]})]}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Authenticating..."})]})})}},7718:(e,t,s)=>{"use strict";s.d(t,{Mk:()=>m,checkCopyPastePermission:()=>l,grantCopyPastePermission:()=>c,i7:()=>u,removeCopyPastePermission:()=>d});var a=s(6104),r=s(5317);let n={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},i={users:"users"};class o{static async checkCopyPastePermission(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,i.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let s=t.data()[n.quickTranslationAdvantageExpiry];if(!s)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let o=s.toDate(),l=new Date,c=o>l,d=c?Math.ceil((o.getTime()-l.getTime())/864e5):0;return{hasPermission:c,daysRemaining:d,expiryDate:o}}catch(t){return console.error("Error checking copy-paste permission for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let s=new Date;s.setDate(s.getDate()+t);let o=(0,r.H9)(a.db,i.users,e);await (0,r.mZ)(o,{[n.quickTranslationAdvantageExpiry]:r.Dc.fromDate(s),[n.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(t," days (expires: ").concat(s.toDateString(),")"))}catch(t){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),t),t}}static async removeCopyPastePermission(e){try{let t=(0,r.H9)(a.db,i.users,e);await (0,r.mZ)(t,{[n.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(t){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,r.x7)((0,r.H9)(a.db,i.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let s=t.data(),o=s[n.quickTranslationAdvantageExpiry],l=s[n.lastCopyPasteReduction];if(!o)return{reduced:!1,daysRemaining:0,expired:!1};let c=new Date().toDateString();if((l?l.toDate().toDateString():null)===c){let e=o.toDate(),t=new Date,s=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:s,expired:0===s}}let d=o.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let m=(0,r.H9)(a.db,i.users,e);if(u<=new Date)return await (0,r.mZ)(m,{[n.quickTranslationAdvantageExpiry]:null,[n.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(m,{[n.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[n.lastCopyPasteReduction]:r.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(t," days remaining")),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error("Error reducing copy-paste days for user ".concat(e,":"),t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,i.users)),t=0,s=0,n=0,l=0;for(let a of e.docs)try{t++;let e=await o.reduceCopyPasteDays(a.id);e.reduced&&(s++,e.expired&&n++)}catch(e){l++,console.error("Error processing copy-paste reduction for user ".concat(a.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Reduced: ".concat(s," users")),console.log("   - Expired: ".concat(n," users")),console.log("   - Errors: ".concat(l," users")),{processed:t,reduced:s,expired:n,errors:l}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await o.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error("Error getting copy-paste status for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let l=o.checkCopyPastePermission,c=o.grantCopyPastePermission,d=o.removeCopyPastePermission,u=o.reduceCopyPasteDays,m=o.processAllUsersCopyPasteReduction;o.getCopyPasteStatus},8763:(e,t,s)=>{Promise.resolve().then(s.bind(s,1512))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,6681,3592,3499,3663,8441,1684,7358],()=>t(8763)),_N_E=e.O()}]);