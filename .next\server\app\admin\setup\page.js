(()=>{var e={};e.id=6912,e.ids=[6912],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3516:(e,s,t)=>{Promise.resolve().then(t.bind(t,5936))},4573:e=>{"use strict";e.exports=require("node:buffer")},5936:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(60687),i=t(43210),n=t(85814),a=t.n(n),o=t(30474),l=t(63385),c=t(75535),d=t(33784),u=t(3582),p=t(77567);function m(){let[e,s]=(0,i.useState)(!1),[t,n]=(0,i.useState)(!1),m=async()=>{s(!0);try{let e="<EMAIL>",s=(await (0,l.eJ)(d.j2,e,"123456")).user,t={[u.Yr.name]:"Instra Global Admin",[u.Yr.email]:e,[u.Yr.mobile]:"9999999999",[u.Yr.referralCode]:"TN0000",[u.Yr.referredBy]:"",[u.Yr.plan]:"Admin",[u.Yr.planExpiry]:null,[u.Yr.activeDays]:999999,[u.Yr.totalTranslations]:0,[u.Yr.todayTranslations]:0,[u.Yr.lastTranslationDate]:null,[u.Yr.wallet]:0,[u.Yr.joinedDate]:c.Dc.now(),status:"active",[u.Yr.translationDuration]:300,role:"admin",isAdmin:!0,permissions:["all"]};await (0,c.BN)((0,c.H9)(d.db,u.COLLECTIONS.users,s.uid),t);let r={email:e,name:"Instra Global Admin",role:"super_admin",permissions:["all"],createdAt:c.Dc.now(),isActive:!0};await (0,c.BN)((0,c.H9)(d.db,"admins",s.uid),r),await d.j2.signOut(),n(!0),p.A.fire({icon:"success",title:"Admin Account Created!",html:`
          <div class="text-left">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> 123456</p>
            <br>
            <p>The admin account has been successfully created. You can now login using these credentials.</p>
          </div>
        `,confirmButtonText:"Go to Admin Login"}).then(()=>{window.location.href="/admin/login"})}catch(s){console.error("Error creating admin account:",s);let e="Failed to create admin account";"auth/email-already-in-use"===s.code?(e="Admin account already exists! You can login with: <EMAIL> / 123456",p.A.fire({icon:"info",title:"Admin Account Exists",html:`
            <div class="text-left">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> 123456</p>
              <br>
              <p>The admin account already exists. Use these credentials to login.</p>
            </div>
          `,confirmButtonText:"Go to Admin Login"}).then(()=>{window.location.href="/admin/login"})):p.A.fire({icon:"error",title:"Setup Failed",text:e})}finally{s(!1)}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(o.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:50,height:50,className:"mr-3"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-white",children:"Instra Global"})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Setup"}),(0,r.jsx)("p",{className:"text-white/80",children:"Create the admin account for Instra Global"})]}),t?(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-green-400 text-6xl mb-4",children:(0,r.jsx)("i",{className:"fas fa-check-circle"})}),(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Setup Complete!"}),(0,r.jsx)("p",{className:"text-white/80 mb-6",children:"Admin account has been created successfully."}),(0,r.jsxs)(a(),{href:"/admin/login",className:"btn-primary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Go to Admin Login"]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 border border-blue-500/30",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Admin Account Details"]}),(0,r.jsxs)("div",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Password:"})," 123456"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Role:"})," Super Administrator"]})]})]}),(0,r.jsx)("button",{onClick:m,disabled:e,className:"w-full btn-primary flex items-center justify-center",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Admin Account..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Create Admin Account"]})}),(0,r.jsx)("div",{className:"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30",children:(0,r.jsxs)("div",{className:"flex items-start text-yellow-300",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle mr-2 mt-1"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("p",{className:"font-semibold mb-1",children:"Security Notice:"}),(0,r.jsx)("p",{children:"This will create an admin account with full system access. Make sure to change the password after first login for security."})]})]})})]}),(0,r.jsxs)("div",{className:"mt-8 text-center space-y-2",children:[(0,r.jsxs)(a(),{href:"/admin/login",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]}),(0,r.jsx)("br",{}),(0,r.jsxs)(a(),{href:"/",className:"text-white/60 hover:text-white/80 transition-colors inline-flex items-center text-sm",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},45372:(e,s,t)=>{Promise.resolve().then(t.bind(t,94606))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},62054:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c={children:["",{children:["admin",{children:["setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94606)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\setup\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\setup\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/setup/page",pathname:"/admin/setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94606:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\setup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\setup\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[2579,6803,3582],()=>t(62054));module.exports=r})();