(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,7718,8297],{239:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(5155),r=t(2115),i=t(6874),c=t.n(i),n=t(6681),l=t(2439),o=t(7718),d=t(4752),u=t.n(d);function m(){let{user:e,loading:s,isAdmin:t}=(0,n.wC)(),[i,d]=(0,r.useState)(!1),[m,y]=(0,r.useState)(null),x=async()=>{if(e&&(await u().fire({icon:"warning",title:"Manual Active Days Increment",text:"This will increment active days for all eligible users. Are you sure?",showCancelButton:!0,confirmButtonText:"Yes, Proceed",cancelButtonText:"Cancel"})).isConfirmed)try{d(!0),console.log("\uD83D\uDD27 Manual active days increment triggered by admin: ".concat(e.uid)),console.log("\uD83D\uDCC5 Processing active days increment...");let s=await (0,l.mH)();console.log("\uD83D\uDCCB Processing copy-paste reduction...");let t=await (0,o.Mk)(),a={success:!0,totalUsers:s.processed,updatedUsers:s.updated,skippedUsers:0,errors:s.errors,activeDaysResult:s,copyPasteResult:t};y(a),u().fire({icon:"success",title:"Process Completed",html:'\n          <div class="text-left">\n            <p><strong>Active Days Results:</strong></p>\n            <ul>\n              <li>Processed: '.concat(s.processed," users</li>\n              <li>Updated: ").concat(s.updated," users</li>\n              <li>Errors: ").concat(s.errors," users</li>\n            </ul>\n            <br>\n            <p><strong>Copy-Paste Results:</strong></p>\n            <ul>\n              <li>Processed: ").concat(t.processed," users</li>\n              <li>Reduced: ").concat(t.reduced," users</li>\n              <li>Expired: ").concat(t.expired," users</li>\n              <li>Errors: ").concat(t.errors," users</li>\n            </ul>\n          </div>\n        "),confirmButtonText:"OK"})}catch(e){console.error("Error triggering manual increment:",e),u().fire({icon:"error",title:"Process Failed",text:"An error occurred while processing active days increment."})}finally{d(!1)}};if(s)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading active days manager..."})]})});let p=(()=>{let e=new Date(new Date);return e.setDate(e.getDate()+1),e.setHours(0,0,0,0),e})();return(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("div",{className:"glass-card p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-white mb-2",children:[(0,a.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Active Days Manager"]}),(0,a.jsx)("p",{className:"text-white/80",children:"Manage daily active days increment system"})]}),(0,a.jsxs)(c(),{href:"/admin",className:"btn-secondary",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]})]})}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"System Status"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),"Next Scheduled Run"]}),(0,a.jsx)("p",{className:"text-white/80 text-lg",children:p.toLocaleString()}),(0,a.jsx)("p",{className:"text-white/60 text-sm mt-1",children:"Runs automatically at midnight (12:00 AM) daily"})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,a.jsx)("i",{className:"fas fa-cogs mr-2"}),"System Rules"]}),(0,a.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Increments active days for all active users"}),(0,a.jsx)("li",{children:"• Skips admin users automatically"}),(0,a.jsx)("li",{children:"• Skips users on leave"}),(0,a.jsx)("li",{children:"• Runs once per day at midnight"})]})]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-hand-pointer mr-2"}),"Manual Controls"]}),(0,a.jsx)("div",{className:"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30 mb-4",children:(0,a.jsxs)("div",{className:"flex items-start text-yellow-300",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle mr-2 mt-1"}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"font-semibold mb-1",children:"Warning:"}),(0,a.jsx)("p",{children:"Manual trigger will increment active days for all eligible users immediately. This should only be used for testing or emergency situations."})]})]})}),(0,a.jsx)("button",{onClick:x,disabled:i,className:"btn-primary",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-play mr-2"}),"Manual Trigger Active Days Increment"]})})]}),m&&(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Last Execution Result"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:m.totalUsers}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Total Users"})]}),(0,a.jsxs)("div",{className:"bg-green-500/20 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-400",children:m.updatedUsers}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Successfully Updated"})]}),(0,a.jsxs)("div",{className:"bg-yellow-500/20 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:m.skippedUsers}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Skipped Users"})]}),(0,a.jsxs)("div",{className:"bg-red-500/20 rounded-lg p-4 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-400",children:m.errors}),(0,a.jsx)("div",{className:"text-white/80 text-sm",children:"Errors"})]})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(m.success?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"),children:m.success?"Success":"Failed"})})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mt-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-question-circle mr-2"}),"How It Works"]}),(0,a.jsxs)("div",{className:"space-y-4 text-white/80",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:"Automatic Scheduling"}),(0,a.jsx)("p",{className:"text-sm",children:"System runs automatically every day at midnight (12:00 AM)"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:"User Filtering"}),(0,a.jsx)("p",{className:"text-sm",children:"Identifies eligible users (active, non-admin, not on leave)"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:"Increment Process"}),(0,a.jsx)("p",{className:"text-sm",children:"Increases active days by 1 for each eligible user"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:"Logging"}),(0,a.jsx)("p",{className:"text-sm",children:"Records all actions and results for audit purposes"})]})]})]})]})]})}},2439:(e,s,t)=>{"use strict";t.d(s,{S3:()=>m,i7:()=>l,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>o});var a=t(6104),r=t(5317);let i={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},c={users:"users"};class n{static async calculateActiveDays(e){try{var s,t;let i,l=await (0,r.x7)((0,r.H9)(a.db,c.users,e));if(!l.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let o=l.data(),d=(null==(s=o.joinedDate)?void 0:s.toDate())||new Date,u=null==(t=o.lastActiveDayUpdate)?void 0:t.toDate(),m=o.activeDays||0,y=o.plan||"Trial",x=new Date,p=x.toDateString(),h=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(m)),console.log("   - Last update: ".concat(h||"Never")),console.log("   - Today: ".concat(p)),console.log("   - Plan: ".concat(y)),h===p)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:m,shouldUpdate:!1,isNewDay:!1};if("Admin"===y)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await n.updateLastActiveDayUpdate(e),{activeDays:m,shouldUpdate:!1,isNewDay:!0};if(await n.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await n.updateLastActiveDayUpdate(e),{activeDays:m,shouldUpdate:!1,isNewDay:!0};return i="Trial"===y?Math.floor((x.getTime()-d.getTime())/864e5)+1:m+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(m," → ").concat(i)),{activeDays:i,shouldUpdate:i!==m,isNewDay:!0}}catch(s){return console.error("Error calculating active days for user ".concat(e,":"),s),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let s=await n.calculateActiveDays(e);if(s.shouldUpdate){let t=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(t,{[i.activeDays]:s.activeDays,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(s.activeDays))}else s.isNewDay&&await n.updateLastActiveDayUpdate(e);return s.activeDays}catch(s){throw console.error("Error updating active days for user ".concat(e,":"),s),s}}static async updateLastActiveDayUpdate(e){try{let s=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(s,{[i.lastActiveDayUpdate]:r.Dc.now()})}catch(s){console.error("Error updating last active day timestamp for user ".concat(e,":"),s)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:s}=await t.e(9567).then(t.bind(t,9567));return await s(e,new Date)}catch(s){return console.error("Error checking leave status for user ".concat(e,":"),s),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,c.users)),s=0,t=0,i=0;for(let a of e.docs)try{s++;let e=await n.calculateActiveDays(a.id);(e.shouldUpdate||e.isNewDay)&&(await n.updateUserActiveDays(a.id),e.shouldUpdate&&t++)}catch(e){i++,console.error("Error processing active days for user ".concat(a.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(s," users")),console.log("   - Updated: ".concat(t," users")),console.log("   - Errors: ".concat(i," users")),{processed:s,updated:t,errors:i}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,c.users,e));if(!s.exists())return 0;return s.data().activeDays||0}catch(s){return console.error("Error getting active days for user ".concat(e,":"),s),0}}static async initializeActiveDaysForNewUser(e){try{let s=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(s,{[i.activeDays]:1,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(s){throw console.error("Error initializing active days for user ".concat(e,":"),s),s}}}let l=n.calculateActiveDays,o=n.updateUserActiveDays,d=n.processAllUsersActiveDays,u=n.getUserActiveDays,m=n.initializeActiveDaysForNewUser},7718:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>m,checkCopyPastePermission:()=>l,grantCopyPastePermission:()=>o,i7:()=>u,removeCopyPastePermission:()=>d});var a=t(6104),r=t(5317);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},c={users:"users"};class n{static async checkCopyPastePermission(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,c.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[i.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let n=t.toDate(),l=new Date,o=n>l,d=o?Math.ceil((n.getTime()-l.getTime())/864e5):0;return{hasPermission:o,daysRemaining:d,expiryDate:n}}catch(s){return console.error("Error checking copy-paste permission for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let n=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(n,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(t),[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(s," days (expires: ").concat(t.toDateString(),")"))}catch(s){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),s),s}}static async removeCopyPastePermission(e){try{let s=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(s,{[i.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(s){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,c.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),n=t[i.quickTranslationAdvantageExpiry],l=t[i.lastCopyPasteReduction];if(!n)return{reduced:!1,daysRemaining:0,expired:!1};let o=new Date().toDateString();if((l?l.toDate().toDateString():null)===o){let e=n.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=n.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let m=(0,r.H9)(a.db,c.users,e);if(u<=new Date)return await (0,r.mZ)(m,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(m,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[i.lastCopyPasteReduction]:r.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(s," days remaining")),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error("Error reducing copy-paste days for user ".concat(e,":"),s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,c.users)),s=0,t=0,i=0,l=0;for(let a of e.docs)try{s++;let e=await n.reduceCopyPasteDays(a.id);e.reduced&&(t++,e.expired&&i++)}catch(e){l++,console.error("Error processing copy-paste reduction for user ".concat(a.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(s," users")),console.log("   - Reduced: ".concat(t," users")),console.log("   - Expired: ".concat(i," users")),console.log("   - Errors: ".concat(l," users")),{processed:s,reduced:t,expired:i,errors:l}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await n.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error("Error getting copy-paste status for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let l=n.checkCopyPastePermission,o=n.grantCopyPastePermission,d=n.removeCopyPastePermission,u=n.reduceCopyPasteDays,m=n.processAllUsersCopyPasteReduction;n.getCopyPasteStatus},9409:(e,s,t)=>{Promise.resolve().then(t.bind(t,239))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,6681,8441,1684,7358],()=>s(9409)),_N_E=e.O()}]);