"use strict";exports.id=1391,exports.ids=[1391,3772],exports.modules={91391:(t,a,e)=>{e.d(a,{CF:()=>c,Pn:()=>n,TK:()=>D,getWithdrawals:()=>w,hG:()=>p,lo:()=>d,nQ:()=>u,searchUsers:()=>l,updateWithdrawalStatus:()=>h});var r=e(75535),o=e(33784),s=e(3582);let i=new Map;async function n(){let t="dashboard-stats",a=function(t){let a=i.get(t);return a&&Date.now()-a.timestamp<3e5?a.data:null}(t);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let e=r.Dc.fromDate(a),n=await (0,r.getDocs)((0,r.collection)(o.db,s.COLLECTIONS.users)),d=n.size,l=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r._M)(s.Yr.joinedDate,">=",e)),c=(await (0,r.getDocs)(l)).size,u=0,w=0,D=0,p=0;n.forEach(t=>{let e=t.data(),r=Number(e[s.Yr.totalTranslations])||0,o=Number(e[s.Yr.wallet])||0;u+=r,w+=o;let i=e[s.Yr.lastTranslationDate]?.toDate();if(i&&i.toDateString()===a.toDateString()){let t=Number(e[s.Yr.todayTranslations])||0;D+=t}});try{let t=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.transactions),(0,r._M)(s.Yr.type,"==","translation_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(t)).forEach(t=>{let e=t.data(),r=e[s.Yr.date]?.toDate();if(r&&r>=a){let t=Number(e[s.Yr.amount])||0;p+=t}})}catch(t){console.warn("Could not fetch today's transactions:",t)}let h=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),y=(await (0,r.getDocs)(h)).size,g=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.withdrawals),(0,r._M)("date",">=",e)),E=(await (0,r.getDocs)(g)).size,C={totalUsers:Number(d)||0,totalTranslations:Number(u)||0,totalEarnings:Number(w)||0,pendingWithdrawals:Number(y)||0,todayUsers:Number(c)||0,todayTranslations:Number(D)||0,todayEarnings:Number(p)||0,todayWithdrawals:Number(E)||0};return i.set(t,{data:C,timestamp:Date.now()}),C}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function d(t=50,a=null){try{let e=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r.My)(s.Yr.joinedDate,"desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r.My)(s.Yr.joinedDate,"desc"),(0,r.HM)(a),(0,r.AB)(t)));let i=await (0,r.getDocs)(e);return{users:i.docs.map(t=>{let a=t.data();return{id:t.id,...a,joinedDate:a[s.Yr.joinedDate]?.toDate(),planExpiry:a[s.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:a.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:a.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:a.lastTranslationDate?.toDate(),lastVideoDate:a.lastVideoDate?.toDate(),lastCopyPasteReduction:a.lastCopyPasteReduction?.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function l(t){try{if(!t||0===t.trim().length)return[];let a=t.toLowerCase().trim(),e=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r.My)(s.Yr.joinedDate,"desc"));return(await (0,r.getDocs)(e)).docs.map(t=>{let a=t.data();return{id:t.id,...a,joinedDate:a[s.Yr.joinedDate]?.toDate(),planExpiry:a[s.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:a.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:a.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:a.lastTranslationDate?.toDate(),lastVideoDate:a.lastVideoDate?.toDate(),lastCopyPasteReduction:a.lastCopyPasteReduction?.toDate()}}).filter(t=>{let e=String(t[s.Yr.name]||"").toLowerCase(),r=String(t[s.Yr.email]||"").toLowerCase(),o=String(t[s.Yr.mobile]||"").toLowerCase(),i=String(t[s.Yr.referralCode]||"").toLowerCase();return e.includes(a)||r.includes(a)||o.includes(a)||i.includes(a)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users),(0,r.My)(s.Yr.joinedDate,"desc"));return(await (0,r.getDocs)(t)).docs.map(t=>{let a=t.data();return{id:t.id,...a,joinedDate:a[s.Yr.joinedDate]?.toDate(),planExpiry:a[s.Yr.planExpiry]?.toDate(),quickTranslationAdvantageExpiry:a.quickTranslationAdvantageExpiry?.toDate(),quickVideoAdvantageExpiry:a.quickVideoAdvantageExpiry?.toDate(),lastTranslationDate:a.lastTranslationDate?.toDate(),lastVideoDate:a.lastVideoDate?.toDate(),lastCopyPasteReduction:a.lastCopyPasteReduction?.toDate()}})}catch(t){throw console.error("Error getting all users:",t),t}}async function u(){try{let t=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.users));return(await (0,r.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function w(t=50,a=null){try{let e=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.AB)(t));a&&(e=(0,r.P)((0,r.collection)(o.db,s.COLLECTIONS.withdrawals),(0,r.My)("date","desc"),(0,r.HM)(a),(0,r.AB)(t)));let i=await (0,r.getDocs)(e);return{withdrawals:i.docs.map(t=>({id:t.id,...t.data(),date:t.data().date?.toDate()})),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function D(t,a){try{await (0,r.mZ)((0,r.H9)(o.db,s.COLLECTIONS.users,t),a),i.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function p(t){try{await (0,r.kd)((0,r.H9)(o.db,s.COLLECTIONS.users,t)),i.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function h(t,a,n){try{let d=await (0,r.x7)((0,r.H9)(o.db,s.COLLECTIONS.withdrawals,t));if(!d.exists())throw Error("Withdrawal not found");let{userId:l,amount:c,status:u}=d.data(),w={status:a,updatedAt:r.Dc.now()};if(n&&(w.adminNotes=n),await (0,r.mZ)((0,r.H9)(o.db,s.COLLECTIONS.withdrawals,t),w),"approved"===a&&"approved"!==u){let{addTransaction:t}=await Promise.resolve().then(e.bind(e,3582));await t(l,{type:"withdrawal_approved",amount:0,description:`Withdrawal approved - ₹${c} processed for transfer`})}if("rejected"===a&&"rejected"!==u){let{updateWalletBalance:t,addTransaction:a}=await Promise.resolve().then(e.bind(e,3582));await t(l,c),await a(l,{type:"withdrawal_rejected",amount:c,description:`Withdrawal rejected - ₹${c} credited back to wallet`})}i.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}};