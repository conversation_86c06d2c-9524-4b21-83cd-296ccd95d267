(()=>{var e={};e.id=2922,e.ids=[2922],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15426:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-sample-upload\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-sample-upload\\page.tsx","default")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34485:(e,s,t)=>{Promise.resolve().then(t.bind(t,15426))},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},35280:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(60687),i=t(43210),a=t(87979),o=t(27878),n=t(51705);function l(){let{user:e,loading:s}=(0,a.Nu)(),[l,d]=(0,i.useState)(null),[c,p]=(0,i.useState)(!1),u=async()=>{p(!0),d(null);try{console.log("\uD83E\uDDEA Testing daily reduction processes..."),console.log("1. Testing active days increment...");let e=await (0,n.mH)();console.log("2. Testing copy-paste reduction...");let s=await (0,o.Mk)();d({success:!0,activeDaysResult:e,copyPasteResult:s,message:"Daily reduction test completed successfully"})}catch(e){d({success:!1,error:e.message,message:"Daily reduction test failed"})}finally{p(!1)}},x=async()=>{let e=prompt("Enter user email to check copy-paste status:");if(e){p(!0);try{let{searchUsers:s}=await Promise.all([t.e(3582),t.e(3772)]).then(t.bind(t,91391)),r=(await s(e)).find(s=>s.email?.toLowerCase()===e.toLowerCase());if(!r)return void d({success:!1,message:`User not found: ${e}`});let i=await (0,o.checkCopyPastePermission)(r.id);d({success:!0,userEmail:e,userId:r.id,copyPasteStatus:i,message:"Copy-paste status retrieved successfully"})}catch(e){d({success:!1,error:e.message,message:"Failed to check copy-paste status"})}finally{p(!1)}}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Sample Upload & Daily Reduction"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"1. Download Test CSV"}),(0,r.jsx)("p",{className:"text-white/80 mb-3",children:"Download a test CSV file with sample data including copy-paste days."}),(0,r.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalTranslations,walletBalance,activeDays,copyPasteDays\<EMAIL>,50,250,10,7\<EMAIL>,100,500,15,14\<EMAIL>,25,125,5,3\<EMAIL>,75,375,12,0"],{type:"text/csv"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="test-sample-upload.csv",t.click(),URL.revokeObjectURL(s)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Download Test CSV"]})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"2. Upload Test Data"}),(0,r.jsxs)("p",{className:"text-white/80 mb-3",children:["Go to ",(0,r.jsx)("strong",{children:"Admin → Simple Upload"})," and upload the test CSV file."]}),(0,r.jsxs)("a",{href:"/admin/simple-upload",className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg inline-block",children:[(0,r.jsx)("i",{className:"fas fa-upload mr-2"}),"Go to Simple Upload"]})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"3. Test Daily Reduction"}),(0,r.jsx)("p",{className:"text-white/80 mb-3",children:"Test the daily reduction process for active days and copy-paste permissions."}),(0,r.jsx)("button",{onClick:u,disabled:c,className:"bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Testing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-clock mr-2"}),"Test Daily Reduction"]})})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"4. Check User Copy-Paste Status"}),(0,r.jsx)("p",{className:"text-white/80 mb-3",children:"Check the copy-paste permission status for a specific user."}),(0,r.jsx)("button",{onClick:x,disabled:c,className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Checking..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-search mr-2"}),"Check User Status"]})})]}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-3",children:"5. Test Export with Copy-Paste Days"}),(0,r.jsx)("p",{className:"text-white/80 mb-3",children:"Export users to verify copy-paste remaining days are included."}),(0,r.jsxs)("a",{href:"/admin/users",className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg inline-block",children:[(0,r.jsx)("i",{className:"fas fa-users mr-2"}),"Go to Users & Export"]})]}),l&&(0,r.jsxs)("div",{className:`rounded-lg p-4 ${l.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,r.jsx)("h3",{className:`font-bold ${l.success?"text-green-400":"text-red-400"}`,children:l.success?"Test Results - Success!":"Test Results - Failed"}),(0,r.jsx)("p",{className:"text-white mt-2",children:l.message}),l.activeDaysResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Active Days Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(l.activeDaysResult,null,2)})]}),l.copyPasteResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Copy-Paste Reduction Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(l.copyPasteResult,null,2)})]}),l.copyPasteStatus&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"User Copy-Paste Status:"}),(0,r.jsxs)("div",{className:"text-white/80 text-sm mt-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",l.userEmail]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"User ID:"})," ",l.userId]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Has Permission:"})," ",l.copyPasteStatus.hasPermission?"Yes":"No"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Days Remaining:"})," ",l.copyPasteStatus.daysRemaining]}),l.copyPasteStatus.expiryDate&&(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Expiry Date:"})," ",l.copyPasteStatus.expiryDate.toDateString()]})]})]}),l.error&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,r.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:l.error})]})]}),(0,r.jsxs)("div",{className:"bg-blue-500/10 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-blue-400 font-bold mb-3",children:"Testing Instructions:"}),(0,r.jsxs)("ol",{className:"text-blue-300 text-sm space-y-2 list-decimal list-inside",children:[(0,r.jsx)("li",{children:"Download the test CSV file"}),(0,r.jsx)("li",{children:"Go to Simple Upload and upload the CSV"}),(0,r.jsx)("li",{children:"Verify users are updated with copy-paste permissions"}),(0,r.jsx)("li",{children:"Test daily reduction to see copy-paste days decrease"}),(0,r.jsx)("li",{children:"Check individual user status"}),(0,r.jsx)("li",{children:"Export users to verify copy-paste remaining days are included"}),(0,r.jsx)("li",{children:"Check browser console for detailed logs"})]})]})]})]})})})}},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76341:(e,s,t)=>{Promise.resolve().then(t.bind(t,35280))},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89736:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),i=t(48088),a=t(88170),o=t.n(a),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(s,l);let d={children:["",{children:["test-sample-upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15426)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-sample-upload\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-sample-upload\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-sample-upload/page",pathname:"/test-sample-upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[2579,6803],()=>t(89736));module.exports=r})();