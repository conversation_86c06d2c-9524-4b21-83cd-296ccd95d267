(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7515],{1687:(e,a,t)=>{Promise.resolve().then(t.bind(t,5089))},5089:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>j});var s=t(5155),i=t(2115),r=t(6874),n=t.n(r),l=t(6681),c=t(3004),d=t(5317),o=t(6104),m=t(3592);let u={super_admin:["all"],admin:["users.read","users.write","users.delete","transactions.read","transactions.write","withdrawals.read","withdrawals.write","withdrawals.approve","notifications.read","notifications.write","settings.read","settings.write","leaves.read","leaves.write","reports.read"],moderator:["users.read","users.write","transactions.read","withdrawals.read","notifications.read","notifications.write","leaves.read"],support:["users.read","transactions.read","notifications.read","notifications.write"]};async function x(e,a){try{let t=(await (0,c.eJ)(o.j2,e.email,e.password)).user,s=e.permissions||u[e.role],i={[m.Yr.name]:e.name,[m.Yr.email]:e.email,[m.Yr.mobile]:"0000000000",[m.Yr.referralCode]:"TN".concat(Date.now().toString().slice(-4)),[m.Yr.referredBy]:"",[m.Yr.plan]:"Admin",[m.Yr.planExpiry]:null,[m.Yr.activeDays]:999999,[m.Yr.totalTranslations]:0,[m.Yr.todayTranslations]:0,[m.Yr.lastTranslationDate]:null,[m.Yr.wallet]:0,[m.Yr.joinedDate]:d.Dc.now(),status:"active",role:e.role,isAdmin:!0,permissions:s};await (0,d.BN)((0,d.H9)(o.db,m.COLLECTIONS.users,t.uid),i);let r={email:e.email,name:e.name,role:e.role,permissions:s,createdAt:d.Dc.now(),isActive:!0,createdBy:a||"system"};return await (0,d.BN)((0,d.H9)(o.db,"admins",t.uid),r),await f(a||"system","admin_created",{targetUserId:t.uid,targetEmail:e.email,role:e.role}),t.uid}catch(e){throw console.error("Error creating admin account:",e),e}}async function h(e,a){try{await (0,d.mZ)((0,d.H9)(o.db,"admins",e),{isActive:!1,deactivatedAt:d.Dc.now(),deactivatedBy:a}),await (0,d.mZ)((0,d.H9)(o.db,m.COLLECTIONS.users,e),{status:"inactive"}),await f(a,"admin_deactivated",{targetUserId:e})}catch(e){throw console.error("Error deactivating admin:",e),e}}async function p(){try{let e=(0,d.P)((0,d.collection)(o.db,"admins")),a=await (0,d.getDocs)(e),t=[];return a.forEach(e=>{var a,s;let i=e.data();t.push({id:e.id,name:i.name,email:i.email,role:i.role,permissions:i.permissions||[],isActive:i.isActive,createdAt:(null==(a=i.createdAt)?void 0:a.toDate())||new Date,lastLogin:null==(s=i.lastLogin)?void 0:s.toDate(),createdBy:i.createdBy})}),t}catch(e){return console.error("Error getting all admins:",e),[]}}async function f(e,a,t){try{let s={adminId:e,action:a,details:t,timestamp:d.Dc.now(),ip:"unknown"};await (0,d.gS)((0,d.collection)(o.db,"adminLogs"),s)}catch(e){console.error("Error logging admin action:",e)}}var w=t(4752),v=t.n(w);function j(){let{user:e,loading:a,isAdmin:t}=(0,l.wC)(),[r,c]=(0,i.useState)([]),[d,o]=(0,i.useState)(!0),[m,u]=(0,i.useState)(!1),[f,w]=(0,i.useState)({name:"",email:"",password:"",role:"support"});(0,i.useEffect)(()=>{t&&j()},[t]);let j=async()=>{try{o(!0);let e=await p();c(e)}catch(e){console.error("Error loading admins:",e),v().fire({icon:"error",title:"Error",text:"Failed to load admin list"})}finally{o(!1)}},N=async a=>{if(a.preventDefault(),!f.name||!f.email||!f.password)return void v().fire({icon:"error",title:"Error",text:"Please fill in all required fields"});try{await x(f,null==e?void 0:e.uid),v().fire({icon:"success",title:"Admin Created",text:"Admin account created successfully for ".concat(f.email)}),u(!1),w({name:"",email:"",password:"",role:"support"}),j()}catch(a){console.error("Error creating admin:",a);let e="Failed to create admin account";"auth/email-already-in-use"===a.code&&(e="An account with this email already exists"),v().fire({icon:"error",title:"Creation Failed",text:e})}},b=async(a,t)=>{if((await v().fire({icon:"warning",title:"Deactivate Admin",text:"Are you sure you want to deactivate ".concat(t,"?"),showCancelButton:!0,confirmButtonText:"Yes, Deactivate",cancelButtonText:"Cancel"})).isConfirmed)try{await h(a,(null==e?void 0:e.uid)||""),v().fire({icon:"success",title:"Admin Deactivated",text:"Admin account has been deactivated"}),j()}catch(e){console.error("Error deactivating admin:",e),v().fire({icon:"error",title:"Error",text:"Failed to deactivate admin"})}},g=e=>{switch(e){case"super_admin":return"bg-red-500";case"admin":return"bg-blue-500";case"moderator":return"bg-yellow-500";case"support":return"bg-green-500";default:return"bg-gray-500"}};return a||d?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:"Loading admin management..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("div",{className:"glass-card p-6 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-white mb-2",children:[(0,s.jsx)("i",{className:"fas fa-users-cog mr-3"}),"Manage Admins"]}),(0,s.jsx)("p",{className:"text-white/80",children:"Create and manage admin accounts"})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsxs)("button",{onClick:()=>u(!0),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Create Admin"]}),(0,s.jsxs)(n(),{href:"/admin",className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]})]})]})}),m&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create New Admin"]}),(0,s.jsxs)("form",{onSubmit:N,className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Name"}),(0,s.jsx)("input",{type:"text",value:f.name,onChange:e=>w({...f,name:e.target.value}),className:"form-input",placeholder:"Enter admin name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",value:f.email,onChange:e=>w({...f,email:e.target.value}),className:"form-input",placeholder:"Enter admin email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Password"}),(0,s.jsx)("input",{type:"password",value:f.password,onChange:e=>w({...f,password:e.target.value}),className:"form-input",placeholder:"Enter password",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Role"}),(0,s.jsxs)("select",{value:f.role,onChange:e=>w({...f,role:e.target.value}),className:"form-input",children:[(0,s.jsx)("option",{value:"support",children:"Support"}),(0,s.jsx)("option",{value:"moderator",children:"Moderator"}),(0,s.jsx)("option",{value:"admin",children:"Admin"}),(0,s.jsx)("option",{value:"super_admin",children:"Super Admin"})]})]}),(0,s.jsxs)("div",{className:"md:col-span-2 flex space-x-3",children:[(0,s.jsxs)("button",{type:"submit",className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-save mr-2"}),"Create Admin"]}),(0,s.jsx)("button",{type:"button",onClick:()=>u(!1),className:"btn-secondary",children:"Cancel"})]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-list mr-2"}),"Admin Accounts (",r.length,")"]}),(0,s.jsxs)("div",{className:"overflow-x-auto",children:[(0,s.jsxs)("table",{className:"w-full text-white",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-white/20",children:[(0,s.jsx)("th",{className:"text-left p-3",children:"Name"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Email"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Role"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Status"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Created"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Last Login"}),(0,s.jsx)("th",{className:"text-left p-3",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:r.map(a=>(0,s.jsxs)("tr",{className:"border-b border-white/10",children:[(0,s.jsx)("td",{className:"p-3",children:a.name}),(0,s.jsx)("td",{className:"p-3",children:a.email}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium text-white ".concat(g(a.role)),children:a.role.replace("_"," ").toUpperCase()})}),(0,s.jsx)("td",{className:"p-3",children:(0,s.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(a.isActive?"bg-green-500 text-white":"bg-red-500 text-white"),children:a.isActive?"Active":"Inactive"})}),(0,s.jsx)("td",{className:"p-3 text-sm",children:a.createdAt.toLocaleDateString()}),(0,s.jsx)("td",{className:"p-3 text-sm",children:a.lastLogin?a.lastLogin.toLocaleDateString():"Never"}),(0,s.jsx)("td",{className:"p-3",children:a.isActive&&a.email!==(null==e?void 0:e.email)&&(0,s.jsx)("button",{onClick:()=>b(a.id,a.email),className:"text-red-400 hover:text-red-300 transition-colors",title:"Deactivate Admin",children:(0,s.jsx)("i",{className:"fas fa-user-times"})})})]},a.id))})]}),0===r.length&&(0,s.jsx)("div",{className:"text-center py-8 text-white/60",children:"No admin accounts found"})]})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,6681,3592,8441,1684,7358],()=>a(1687)),_N_E=e.O()}]);