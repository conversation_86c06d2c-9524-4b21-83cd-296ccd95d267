[{"name": "generate-buildid", "duration": 348, "timestamp": 11795401756, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750764980480, "traceId": "9cf948f2006fc208"}, {"name": "load-custom-routes", "duration": 1257, "timestamp": 11795402469, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750764980480, "traceId": "9cf948f2006fc208"}, {"name": "create-dist-dir", "duration": 88205, "timestamp": 11795923344, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750764981001, "traceId": "9cf948f2006fc208"}, {"name": "create-pages-mapping", "duration": 727, "timestamp": 11797836963, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750764982915, "traceId": "9cf948f2006fc208"}, {"name": "collect-app-paths", "duration": 13874, "timestamp": 11797837814, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750764982916, "traceId": "9cf948f2006fc208"}, {"name": "create-app-mapping", "duration": 136385, "timestamp": 11797851820, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750764982930, "traceId": "9cf948f2006fc208"}, {"name": "public-dir-conflict-check", "duration": 16580, "timestamp": 11797993547, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750764983072, "traceId": "9cf948f2006fc208"}, {"name": "generate-routes-manifest", "duration": 15624, "timestamp": 11798010899, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750764983089, "traceId": "9cf948f2006fc208"}, {"name": "create-entrypoints", "duration": 1094679, "timestamp": 11809606538, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750764994685, "traceId": "9cf948f2006fc208"}, {"name": "generate-webpack-config", "duration": 4182120, "timestamp": 11810704535, "id": 16, "parentId": 14, "tags": {}, "startTime": 1750764995783, "traceId": "9cf948f2006fc208"}, {"name": "next-trace-entrypoint-plugin", "duration": 17095, "timestamp": 11815514358, "id": 18, "parentId": 17, "tags": {}, "startTime": 1750765000593, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 21896700, "timestamp": 11815777064, "id": 20, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750765000856, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 21905537, "timestamp": 11815781116, "id": 21, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750765000860, "traceId": "9cf948f2006fc208"}, {"name": "build-module-tsx", "duration": 1317614, "timestamp": 11836973414, "id": 64, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-export\\page.tsx", "layer": "rsc"}, "startTime": 1750765022052, "traceId": "9cf948f2006fc208"}, {"name": "build-module-tsx", "duration": 31732, "timestamp": 11838522177, "id": 65, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-admin\\page.tsx", "layer": "rsc"}, "startTime": 1750765023601, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22805061, "timestamp": 11815788348, "id": 49, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-copy-paste-export%2Fpage&name=app%2Ftest-copy-paste-export%2Fpage&pagePath=private-next-app-dir%2Ftest-copy-paste-export%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-copy-paste-export%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22805102, "timestamp": 11815788378, "id": 51, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-copy-paste-reduction%2Fpage&name=app%2Ftest-copy-paste-reduction%2Fpage&pagePath=private-next-app-dir%2Ftest-copy-paste-reduction%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-copy-paste-reduction%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22804788, "timestamp": 11815788764, "id": 56, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-sample-upload%2Fpage&name=app%2Ftest-sample-upload%2Fpage&pagePath=private-next-app-dir%2Ftest-sample-upload%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-sample-upload%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22812631, "timestamp": 11815781463, "id": 22, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Flogin%2Fpage&name=app%2Fadmin%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Flogin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000860, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22812497, "timestamp": 11815781652, "id": 23, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Ffix-permissions%2Fpage&name=app%2Fadmin%2Ffix-permissions%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Ffix-permissions%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Ffix-permissions%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000860, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22812341, "timestamp": 11815781828, "id": 25, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Ffix-active-days%2Fpage&name=app%2Fadmin%2Ffix-active-days%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Ffix-active-days%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Ffix-active-days%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000860, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22812301, "timestamp": 11815781885, "id": 26, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Factive-days-manager%2Fpage&name=app%2Fadmin%2Factive-days-manager%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Factive-days-manager%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Factive-days-manager%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000860, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22812748, "timestamp": 11815781938, "id": 27, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsettings%2Fpage&name=app%2Fadmin%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fsettings%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000860, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22812714, "timestamp": 11815782011, "id": 28, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fmanage-admins%2Fpage&name=app%2Fadmin%2Fmanage-admins%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fmanage-admins%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fmanage-admins%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000861, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22808765, "timestamp": 11815785979, "id": 29, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fpage&name=app%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000865, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22807436, "timestamp": 11815787316, "id": 30, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fnotifications%2Fpage&name=app%2Fadmin%2Fnotifications%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fnotifications%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fnotifications%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22807089, "timestamp": 11815787673, "id": 31, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsetup%2Fpage&name=app%2Fadmin%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsetup%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fsetup%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806948, "timestamp": 11815787830, "id": 32, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Ftest-blocking%2Fpage&name=app%2Fadmin%2Ftest-blocking%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Ftest-blocking%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Ftest-blocking%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806920, "timestamp": 11815787865, "id": 33, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fupload-users%2Fpage&name=app%2Fadmin%2Fupload-users%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fupload-users%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fupload-users%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806914, "timestamp": 11815787880, "id": 34, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsimple-upload%2Fpage&name=app%2Fadmin%2Fsimple-upload%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsimple-upload%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fsimple-upload%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806909, "timestamp": 11815787892, "id": 35, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Ftransactions%2Fpage&name=app%2Fadmin%2Ftransactions%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Ftransactions%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Ftransactions%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806897, "timestamp": 11815787910, "id": 36, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fwithdrawals%2Fpage&name=app%2Fadmin%2Fwithdrawals%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fwithdrawals%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fwithdrawals%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806888, "timestamp": 11815787926, "id": 37, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fclear-cache%2Fpage&name=app%2Fclear-cache%2Fpage&pagePath=private-next-app-dir%2Fclear-cache%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fclear-cache%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806882, "timestamp": 11815787939, "id": 38, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fforgot-password%2Fpage&name=app%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2Fforgot-password%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fforgot-password%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806861, "timestamp": 11815787971, "id": 40, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fplans%2Fpage&name=app%2Fplans%2Fpage&pagePath=private-next-app-dir%2Fplans%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fplans%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806859, "timestamp": 11815787982, "id": 41, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Frefer%2Fpage&name=app%2Frefer%2Fpage&pagePath=private-next-app-dir%2Frefer%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Frefer%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806860, "timestamp": 11815787993, "id": 42, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fregister%2Fpage&name=app%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fregister%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806854, "timestamp": 11815788006, "id": 43, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fprofile%2Fpage&name=app%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806847, "timestamp": 11815788020, "id": 44, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-copy-paste-admin%2Fpage&name=app%2Ftest-copy-paste-admin%2Fpage&pagePath=private-next-app-dir%2Ftest-copy-paste-admin%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-copy-paste-admin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806814, "timestamp": 11815788060, "id": 45, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fregistration-diagnostics%2Fpage&name=app%2Fregistration-diagnostics%2Fpage&pagePath=private-next-app-dir%2Fregistration-diagnostics%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fregistration-diagnostics%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806795, "timestamp": 11815788085, "id": 46, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fsupport%2Fpage&name=app%2Fsupport%2Fpage&pagePath=private-next-app-dir%2Fsupport%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fsupport%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806777, "timestamp": 11815788110, "id": 47, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Freset-password%2Fpage&name=app%2Freset-password%2Fpage&pagePath=private-next-app-dir%2Freset-password%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Freset-password%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806641, "timestamp": 11815788252, "id": 48, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-active-days-service%2Fpage&name=app%2Ftest-active-days-service%2Fpage&pagePath=private-next-app-dir%2Ftest-active-days-service%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-active-days-service%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806537, "timestamp": 11815788365, "id": 50, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fleaves%2Fpage&name=app%2Fadmin%2Fleaves%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fleaves%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fleaves%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806514, "timestamp": 11815788394, "id": 52, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-copy-paste-service%2Fpage&name=app%2Ftest-copy-paste-service%2Fpage&pagePath=private-next-app-dir%2Ftest-copy-paste-service%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-copy-paste-service%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806499, "timestamp": 11815788417, "id": 53, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-referral%2Fpage&name=app%2Ftest-referral%2Fpage&pagePath=private-next-app-dir%2Ftest-referral%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-referral%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806221, "timestamp": 11815788701, "id": 54, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fwallet%2Fpage&name=app%2Fwallet%2Fpage&pagePath=private-next-app-dir%2Fwallet%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fwallet%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22806180, "timestamp": 11815788748, "id": 55, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-simple-registration%2Fpage&name=app%2Ftest-simple-registration%2Fpage&pagePath=private-next-app-dir%2Ftest-simple-registration%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-simple-registration%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000867, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22805757, "timestamp": 11815789181, "id": 57, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-translations%2Fpage&name=app%2Ftest-translations%2Fpage&pagePath=private-next-app-dir%2Ftest-translations%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftest-translations%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000868, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22805641, "timestamp": 11815789304, "id": 58, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftransactions%2Fpage&name=app%2Ftransactions%2Fpage&pagePath=private-next-app-dir%2Ftransactions%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Ftransactions%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000868, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22805462, "timestamp": 11815789489, "id": 59, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Flogin%2Fpage&name=app%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Flogin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000868, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22805427, "timestamp": 11815789531, "id": 60, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000868, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22805413, "timestamp": 11815789551, "id": 61, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000868, "traceId": "9cf948f2006fc208"}, {"name": "build-module-tsx", "duration": 24632, "timestamp": 11838576025, "id": 66, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\users\\page.tsx", "layer": "rsc"}, "startTime": 1750765023655, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22829888, "timestamp": 11815787950, "id": 39, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fusers%2Fpage&name=app%2Fadmin%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fusers%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fadmin%2Fusers%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000866, "traceId": "9cf948f2006fc208"}, {"name": "build-module-tsx", "duration": 29423, "timestamp": 11838592219, "id": 67, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx", "layer": "rsc"}, "startTime": 1750765023671, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22839347, "timestamp": 11815789594, "id": 62, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fwork%2Fpage&name=app%2Fwork%2Fpage&pagePath=private-next-app-dir%2Fwork%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fwork%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000868, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 22864153, "timestamp": 11815781758, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CASUS%5COneDrive%5CDesktop%5CMY%20PROJECTS%5CNode%20Instra%5Csrc%5Capp&appPaths=%2Fnot-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750765000860, "traceId": "9cf948f2006fc208"}, {"name": "add-entry", "duration": 23019440, "timestamp": 11815789616, "id": 63, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750765000868, "traceId": "9cf948f2006fc208"}, {"name": "build-module-tsx", "duration": 138602, "timestamp": 11840946010, "id": 564, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-admin\\page.tsx", "layer": "ssr"}, "startTime": 1750765026025, "traceId": "9cf948f2006fc208"}, {"name": "build-module-tsx", "duration": 149186, "timestamp": 11840951502, "id": 565, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-export\\page.tsx", "layer": "ssr"}, "startTime": 1750765026030, "traceId": "9cf948f2006fc208"}, {"name": "build-module-tsx", "duration": 230711, "timestamp": 11840951999, "id": 566, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\users\\page.tsx", "layer": "ssr"}, "startTime": 1750765026031, "traceId": "9cf948f2006fc208"}, {"name": "build-module-tsx", "duration": 327455, "timestamp": 11841480418, "id": 567, "parentId": 17, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\work\\page.tsx", "layer": "ssr"}, "startTime": 1750765026559, "traceId": "9cf948f2006fc208"}, {"name": "build-module-ts", "duration": 40821, "timestamp": 11842447323, "id": 568, "parentId": 565, "tags": {"name": "C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\lib\\csvExport.ts", "layer": "ssr"}, "startTime": 1750765027526, "traceId": "9cf948f2006fc208"}, {"name": "make", "duration": 27579477, "timestamp": 11815768595, "id": 19, "parentId": 17, "tags": {}, "startTime": 1750765000847, "traceId": "9cf948f2006fc208"}, {"name": "get-entries", "duration": 46556, "timestamp": 11843356689, "id": 570, "parentId": 569, "tags": {}, "startTime": 1750765028435, "traceId": "9cf948f2006fc208"}, {"name": "node-file-trace-plugin", "duration": 392154, "timestamp": 11843428376, "id": 571, "parentId": 569, "tags": {"traceEntryCount": "86"}, "startTime": 1750765028507, "traceId": "9cf948f2006fc208"}, {"name": "collect-traced-files", "duration": 5972, "timestamp": 11843820559, "id": 572, "parentId": 569, "tags": {}, "startTime": 1750765028899, "traceId": "9cf948f2006fc208"}, {"name": "finish-modules", "duration": 470707, "timestamp": 11843356050, "id": 569, "parentId": 18, "tags": {}, "startTime": 1750765028435, "traceId": "9cf948f2006fc208"}, {"name": "chunk-graph", "duration": 367751, "timestamp": 11844201227, "id": 574, "parentId": 573, "tags": {}, "startTime": 1750765029280, "traceId": "9cf948f2006fc208"}, {"name": "optimize-modules", "duration": 88, "timestamp": 11844569382, "id": 576, "parentId": 573, "tags": {}, "startTime": 1750765029648, "traceId": "9cf948f2006fc208"}, {"name": "optimize-chunks", "duration": 363147, "timestamp": 11844569728, "id": 577, "parentId": 573, "tags": {}, "startTime": 1750765029648, "traceId": "9cf948f2006fc208"}, {"name": "optimize-tree", "duration": 891, "timestamp": 11844933400, "id": 578, "parentId": 573, "tags": {}, "startTime": 1750765030012, "traceId": "9cf948f2006fc208"}, {"name": "optimize-chunk-modules", "duration": 94738, "timestamp": 11844935236, "id": 579, "parentId": 573, "tags": {}, "startTime": 1750765030014, "traceId": "9cf948f2006fc208"}, {"name": "optimize", "duration": 461666, "timestamp": 11844569225, "id": 575, "parentId": 573, "tags": {}, "startTime": 1750765029648, "traceId": "9cf948f2006fc208"}, {"name": "module-hash", "duration": 103567, "timestamp": 11845117211, "id": 580, "parentId": 573, "tags": {}, "startTime": 1750765030196, "traceId": "9cf948f2006fc208"}, {"name": "code-generation", "duration": 159258, "timestamp": 11845220916, "id": 581, "parentId": 573, "tags": {}, "startTime": 1750765030299, "traceId": "9cf948f2006fc208"}, {"name": "hash", "duration": 40016, "timestamp": 11845404296, "id": 582, "parentId": 573, "tags": {}, "startTime": 1750765030483, "traceId": "9cf948f2006fc208"}, {"name": "code-generation-jobs", "duration": 964, "timestamp": 11845444303, "id": 583, "parentId": 573, "tags": {}, "startTime": 1750765030523, "traceId": "9cf948f2006fc208"}, {"name": "module-assets", "duration": 901, "timestamp": 11845445050, "id": 584, "parentId": 573, "tags": {}, "startTime": 1750765030524, "traceId": "9cf948f2006fc208"}, {"name": "create-chunk-assets", "duration": 17643, "timestamp": 11845445993, "id": 585, "parentId": 573, "tags": {}, "startTime": 1750765030525, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 72026, "timestamp": 11845749735, "id": 587, "parentId": 586, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750765030828, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71581, "timestamp": 11845750200, "id": 588, "parentId": 586, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71527, "timestamp": 11845750258, "id": 589, "parentId": 586, "tags": {"name": "../app/admin/login/page.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71469, "timestamp": 11845750321, "id": 590, "parentId": 586, "tags": {"name": "../app/admin/fix-permissions/page.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71429, "timestamp": 11845750365, "id": 591, "parentId": 586, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71429, "timestamp": 11845750385, "id": 592, "parentId": 586, "tags": {"name": "../app/admin/fix-active-days/page.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71416, "timestamp": 11845750402, "id": 593, "parentId": 586, "tags": {"name": "../app/admin/active-days-manager/page.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71403, "timestamp": 11845750419, "id": 594, "parentId": 586, "tags": {"name": "../app/admin/settings/page.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71391, "timestamp": 11845750434, "id": 595, "parentId": 586, "tags": {"name": "../app/admin/manage-admins/page.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 71182, "timestamp": 11845750647, "id": 596, "parentId": 586, "tags": {"name": "../app/admin/page.js", "cache": "HIT"}, "startTime": 1750765030829, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 47926, "timestamp": 11845773909, "id": 598, "parentId": 586, "tags": {"name": "../app/admin/setup/page.js", "cache": "HIT"}, "startTime": 1750765030852, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 47862, "timestamp": 11845773977, "id": 599, "parentId": 586, "tags": {"name": "../app/admin/test-blocking/page.js", "cache": "HIT"}, "startTime": 1750765030852, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 47848, "timestamp": 11845773995, "id": 600, "parentId": 586, "tags": {"name": "../app/admin/upload-users/page.js", "cache": "HIT"}, "startTime": 1750765030853, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 47837, "timestamp": 11845774011, "id": 601, "parentId": 586, "tags": {"name": "../app/admin/simple-upload/page.js", "cache": "HIT"}, "startTime": 1750765030853, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 38871, "timestamp": 11845782982, "id": 604, "parentId": 586, "tags": {"name": "../app/clear-cache/page.js", "cache": "HIT"}, "startTime": 1750765030862, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 38815, "timestamp": 11845783042, "id": 605, "parentId": 586, "tags": {"name": "../app/forgot-password/page.js", "cache": "HIT"}, "startTime": 1750765030862, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 17120, "timestamp": 11845804742, "id": 607, "parentId": 586, "tags": {"name": "../app/plans/page.js", "cache": "HIT"}, "startTime": 1750765030883, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 17044, "timestamp": 11845804823, "id": 608, "parentId": 586, "tags": {"name": "../app/refer/page.js", "cache": "HIT"}, "startTime": 1750765030883, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 17023, "timestamp": 11845804848, "id": 609, "parentId": 586, "tags": {"name": "../app/register/page.js", "cache": "HIT"}, "startTime": 1750765030883, "traceId": "9cf948f2006fc208"}, {"name": "minify-js", "duration": 17007, "timestamp": 11845804868, "id": 610, "parentId": 586, "tags": {"name": "../app/profile/page.js", "cache": "HIT"}, "startTime": 1750765030883, "traceId": "9cf948f2006fc208"}]