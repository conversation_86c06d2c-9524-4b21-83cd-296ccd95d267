"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/work/page",{

/***/ "(app-pages-browser)/./src/app/work/page.tsx":
/*!*******************************!*\
  !*** ./src/app/work/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useBlockingNotifications */ \"(app-pages-browser)/./src/hooks/useBlockingNotifications.ts\");\n/* harmony import */ var _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useLeaveMonitor */ \"(app-pages-browser)/./src/hooks/useLeaveMonitor.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n/* harmony import */ var _lib_leaveService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/leaveService */ \"(app-pages-browser)/./src/lib/leaveService.ts\");\n/* harmony import */ var _lib_translationManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/translationManager */ \"(app-pages-browser)/./src/lib/translationManager.ts\");\n/* harmony import */ var _lib_copyPasteService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/copyPasteService */ \"(app-pages-browser)/./src/lib/copyPasteService.ts\");\n/* harmony import */ var _lib_sessionManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/sessionManager */ \"(app-pages-browser)/./src/lib/sessionManager.ts\");\n/* harmony import */ var _components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/BlockingNotificationModal */ \"(app-pages-browser)/./src/components/BlockingNotificationModal.tsx\");\n/* harmony import */ var _components_SessionRecovery__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/SessionRecovery */ \"(app-pages-browser)/./src/components/SessionRecovery.tsx\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_13__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WorkPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth)();\n    const { hasBlockingNotifications, isChecking, markAllAsRead } = (0,_hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications)((user === null || user === void 0 ? void 0 : user.uid) || null);\n    const { isBlocked: isLeaveBlocked, leaveStatus } = (0,_hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor)({\n        userId: (user === null || user === void 0 ? void 0 : user.uid) || null,\n        checkInterval: 30000,\n        enabled: !!user\n    });\n    // Debug logging\n    console.log('WorkPage render:', {\n        user: user === null || user === void 0 ? void 0 : user.uid,\n        loading,\n        hasBlockingNotifications,\n        isChecking,\n        isLeaveBlocked\n    });\n    // New Translation Workflow State\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [todayTranslations, setTodayTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalTranslationsCompleted, setTotalTranslationsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [localTranslationCount, setLocalTranslationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [canSubmitBatch, setCanSubmitBatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationItems, setTranslationItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingTranslations, setIsLoadingTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Typing validation state\n    const [userTypedText, setUserTypedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [previousValidText, setPreviousValidText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('') // Track previous valid state for paste reversion\n    ;\n    const [typingErrors, setTypingErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isTypingComplete, setIsTypingComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasQuickAdvantage, setHasQuickAdvantage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copyPasteDaysRemaining, setCopyPasteDaysRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Language selection state\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLanguageCorrect, setIsLanguageCorrect] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTranslation, setShowTranslation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Paste detection state\n    const [lastInputTime, setLastInputTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pasteDetected, setPasteDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastInputLength, setLastInputLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [typingBlocked, setTypingBlocked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [blockReason, setBlockReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Auto-save state\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAutoSaving, setIsAutoSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Session recovery state\n    const [showSessionRecovery, setShowSessionRecovery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionRecoveryComplete, setSessionRecoveryComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Daily completion state\n    const [isDailyCompleted, setIsDailyCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFinalSubmit, setShowFinalSubmit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationSettings, setTranslationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        earningPerBatch: 25,\n        plan: 'Trial'\n    });\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [daysLeft, setDaysLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [activeDays, setActiveDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            if (user && !sessionRecoveryComplete) {\n                // Only show session recovery if we haven't completed it yet\n                setShowSessionRecovery(true);\n                // Add fallback timeout to prevent indefinite blocking\n                const fallbackTimeout = setTimeout({\n                    \"WorkPage.useEffect.fallbackTimeout\": ()=>{\n                        console.log('Session recovery timeout - proceeding without recovery');\n                        setSessionRecoveryComplete(true);\n                        setShowSessionRecovery(false);\n                    }\n                }[\"WorkPage.useEffect.fallbackTimeout\"], 15000) // 15 second timeout\n                ;\n                return ({\n                    \"WorkPage.useEffect\": ()=>clearTimeout(fallbackTimeout)\n                })[\"WorkPage.useEffect\"];\n            } else if (user && sessionRecoveryComplete) {\n                // If user is logged in and recovery is complete, proceed with work access\n                checkWorkAccess();\n            }\n        }\n    }[\"WorkPage.useEffect\"], [\n        user,\n        sessionRecoveryComplete\n    ]);\n    const handleSessionRecoveryComplete = ()=>{\n        setSessionRecoveryComplete(true);\n        setShowSessionRecovery(false);\n    // Don't call checkWorkAccess here - it will be called by the useEffect above\n    };\n    const handleProgressRestored = (progress)=>{\n        // Restore the work state from recovered progress\n        setCurrentStep(progress.currentStep);\n        setUserTypedText(progress.userTypedText);\n        setPreviousValidText(progress.userTypedText) // Set previous valid text to current restored text\n        ;\n        setSelectedLanguage(progress.selectedLanguage);\n        setIsTypingComplete(progress.isTypingComplete);\n        setLocalTranslationCount(progress.completedTranslations);\n        setLastSaved(new Date(progress.lastSaved));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            setCanSubmitBatch(localTranslationCount >= 50);\n            setShowFinalSubmit(localTranslationCount >= 50 && !isDailyCompleted);\n        }\n    }[\"WorkPage.useEffect\"], [\n        localTranslationCount,\n        isDailyCompleted\n    ]);\n    // Auto-save work progress every 10 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            if (!user || !currentStep) return;\n            const autoSaveInterval = setInterval({\n                \"WorkPage.useEffect.autoSaveInterval\": ()=>{\n                    saveWorkProgress();\n                }\n            }[\"WorkPage.useEffect.autoSaveInterval\"], 10000) // Save every 10 seconds\n            ;\n            return ({\n                \"WorkPage.useEffect\": ()=>clearInterval(autoSaveInterval)\n            })[\"WorkPage.useEffect\"];\n        }\n    }[\"WorkPage.useEffect\"], [\n        user,\n        currentStep,\n        userTypedText,\n        selectedLanguage,\n        isTypingComplete,\n        localTranslationCount\n    ]);\n    // Save progress when user types\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            if (user && currentStep && userTypedText) {\n                const debounceTimer = setTimeout({\n                    \"WorkPage.useEffect.debounceTimer\": ()=>{\n                        saveWorkProgress();\n                    }\n                }[\"WorkPage.useEffect.debounceTimer\"], 2000) // Save 2 seconds after user stops typing\n                ;\n                return ({\n                    \"WorkPage.useEffect\": ()=>clearTimeout(debounceTimer)\n                })[\"WorkPage.useEffect\"];\n            }\n        }\n    }[\"WorkPage.useEffect\"], [\n        userTypedText\n    ]);\n    // Save progress before page unload\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"WorkPage.useEffect.handleBeforeUnload\": (e)=>{\n                    if (user && currentStep && (userTypedText || localTranslationCount > 0)) {\n                        saveWorkProgress();\n                        e.preventDefault();\n                        e.returnValue = 'You have unsaved work progress. Are you sure you want to leave?';\n                        return e.returnValue;\n                    }\n                }\n            }[\"WorkPage.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"WorkPage.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"WorkPage.useEffect\"];\n        }\n    }[\"WorkPage.useEffect\"], [\n        user,\n        currentStep,\n        userTypedText,\n        localTranslationCount\n    ]);\n    // Listen for real-time copy-paste permission changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkPage.useEffect\": ()=>{\n            if (!(user === null || user === void 0 ? void 0 : user.uid)) return;\n            const handleCopyPastePermissionChange = {\n                \"WorkPage.useEffect.handleCopyPastePermissionChange\": (event)=>{\n                    const { userId, hasPermission, timestamp, updatedBy } = event.detail;\n                    if (userId === user.uid) {\n                        console.log(\"\\uD83D\\uDCE1 Received copy-paste permission update: \".concat(hasPermission, \" (by \").concat(updatedBy, \")\"));\n                        setHasQuickAdvantage(hasPermission);\n                        // Show notification to user\n                        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                            icon: hasPermission ? 'success' : 'warning',\n                            title: hasPermission ? 'Copy-Paste Enabled!' : 'Copy-Paste Disabled!',\n                            text: hasPermission ? 'Copy-paste permission has been enabled by admin. You can now copy and paste text.' : 'Copy-paste permission has been disabled by admin. Please type text manually.',\n                            timer: 4000,\n                            showConfirmButton: true,\n                            confirmButtonText: 'OK'\n                        });\n                    }\n                }\n            }[\"WorkPage.useEffect.handleCopyPastePermissionChange\"];\n            // Check localStorage for updates (in case event was missed)\n            const checkForUpdates = {\n                \"WorkPage.useEffect.checkForUpdates\": ()=>{\n                    try {\n                        const updateKey = \"copyPasteUpdate_\".concat(user.uid);\n                        const updateData = localStorage.getItem(updateKey);\n                        if (updateData) {\n                            const parsed = JSON.parse(updateData);\n                            const timeDiff = Date.now() - parsed.timestamp;\n                            // If update is less than 30 seconds old, apply it\n                            if (timeDiff < 30000) {\n                                console.log(\"\\uD83D\\uDCE1 Found recent copy-paste update in localStorage: \".concat(parsed.hasPermission));\n                                setHasQuickAdvantage(parsed.hasPermission);\n                                // Show notification\n                                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                    icon: parsed.hasPermission ? 'success' : 'warning',\n                                    title: parsed.hasPermission ? 'Copy-Paste Enabled!' : 'Copy-Paste Disabled!',\n                                    text: parsed.hasPermission ? 'Copy-paste permission has been enabled by admin.' : 'Copy-paste permission has been disabled by admin.',\n                                    timer: 3000,\n                                    showConfirmButton: false\n                                });\n                                // Clear the update after applying\n                                localStorage.removeItem(updateKey);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error checking for copy-paste updates:', error);\n                    }\n                }\n            }[\"WorkPage.useEffect.checkForUpdates\"];\n            // Listen for custom events\n            window.addEventListener('copyPastePermissionChanged', handleCopyPastePermissionChange);\n            // Check for updates immediately and every 5 seconds\n            checkForUpdates();\n            const interval = setInterval(checkForUpdates, 5000);\n            return ({\n                \"WorkPage.useEffect\": ()=>{\n                    window.removeEventListener('copyPastePermissionChanged', handleCopyPastePermissionChange);\n                    clearInterval(interval);\n                }\n            })[\"WorkPage.useEffect\"];\n        }\n    }[\"WorkPage.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.uid\n    ]);\n    const checkWorkAccess = async ()=>{\n        try {\n            console.log('🔍 Checking work access for user:', user.uid);\n            // Check plan expiry first\n            const planStatus = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.isUserPlanExpired)(user.uid);\n            console.log('📅 Plan status result:', planStatus);\n            if (planStatus.expired) {\n                console.log('🚫 Work access blocked - Plan expired:', planStatus.reason);\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'error',\n                    title: 'Plan Expired',\n                    html: '\\n            <div class=\"text-center\">\\n              <p class=\"mb-3\">'.concat(planStatus.reason, '</p>\\n              <p class=\"text-sm text-gray-600\">\\n                Active Days: ').concat(planStatus.activeDays || 0, \" | Days Left: \").concat(planStatus.daysLeft || 0, \"\\n              </p>\\n            </div>\\n          \"),\n                    confirmButtonText: 'Upgrade Plan',\n                    showCancelButton: true,\n                    cancelButtonText: 'Go to Dashboard'\n                }).then((result)=>{\n                    if (result.isConfirmed) {\n                        window.location.href = '/plans';\n                    } else {\n                        window.location.href = '/dashboard';\n                    }\n                });\n                return;\n            }\n            // Check if user has already completed their daily session (50 translations)\n            const translationData = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getVideoCountData)(user.uid);\n            console.log('📊 Translation data check:', translationData);\n            if (translationData.todayTranslations >= 50) {\n                console.log('🚫 Work access blocked - Daily session completed');\n                setIsDailyCompleted(true);\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'info',\n                    title: 'Daily Session Completed',\n                    html: '\\n            <div class=\"text-center\">\\n              <p class=\"mb-3\">You have already completed your daily session of 50 translations!</p>\\n              <p class=\"text-sm text-gray-600\">\\n                Translations completed today: '.concat(translationData.todayTranslations, '/50\\n              </p>\\n              <p class=\"text-sm text-green-600 mt-2\">\\n                Come back tomorrow for your next session.\\n              </p>\\n            </div>\\n          '),\n                    confirmButtonText: 'Go to Dashboard',\n                    allowOutsideClick: false,\n                    allowEscapeKey: false\n                }).then(()=>{\n                    window.location.href = '/dashboard';\n                });\n                return;\n            }\n            const workStatus = await (0,_lib_leaveService__WEBPACK_IMPORTED_MODULE_7__.isWorkBlocked)(user.uid);\n            console.log('📊 Work status result:', workStatus);\n            if (workStatus.blocked) {\n                console.log('🚫 Work access blocked:', workStatus.reason);\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'warning',\n                    title: 'Work Not Available',\n                    text: workStatus.reason || 'Work is currently blocked.',\n                    confirmButtonText: 'Go to Dashboard'\n                }).then(()=>{\n                    window.location.href = '/dashboard';\n                });\n                return;\n            }\n            console.log('✅ Work access allowed, proceeding with fast loading');\n            await initializeWorkPageFast();\n        } catch (error) {\n            console.error('❌ Error checking work access (allowing work to proceed):', error);\n            await initializeWorkPageFast();\n        }\n    };\n    const loadTranslationData = async ()=>{\n        try {\n            console.log('📊 Loading translation data for user:', user.uid);\n            const data = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getVideoCountData)(user.uid);\n            console.log('📊 Translation data loaded:', data);\n            setTodayTranslations(data.todayTranslations);\n            setTotalTranslationsCompleted(data.totalTranslations);\n        } catch (error) {\n            console.error('Error loading translation data:', error);\n        }\n    };\n    const loadTranslationSettings = async ()=>{\n        try {\n            const settings = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserVideoSettings)(user.uid);\n            setTranslationSettings({\n                earningPerBatch: settings.earningPerBatch,\n                plan: settings.plan\n            });\n            // Check copy-paste permission using new service\n            const copyPastePermission = await (0,_lib_copyPasteService__WEBPACK_IMPORTED_MODULE_9__.checkCopyPastePermission)(user.uid);\n            setHasQuickAdvantage(copyPastePermission.hasPermission);\n            setCopyPasteDaysRemaining(copyPastePermission.daysRemaining);\n            console.log('Copy-paste permission status:', {\n                hasPermission: copyPastePermission.hasPermission,\n                daysRemaining: copyPastePermission.daysRemaining,\n                expiryDate: copyPastePermission.expiryDate\n            });\n        } catch (error) {\n            console.error('Error loading translation settings:', error);\n        }\n    };\n    // Fast initialization - loads all data in one go\n    const initializeWorkPageFast = async ()=>{\n        try {\n            setIsLoadingTranslations(true);\n            console.log('🚀 Fast loading work page data...');\n            const workData = await (0,_lib_translationManager__WEBPACK_IMPORTED_MODULE_8__.initializeWorkPageData)(user.uid);\n            if (!workData.canWork) {\n                console.log('🚫 User cannot work today');\n                setIsDailyCompleted(true);\n                return;\n            }\n            // Set all data from cache\n            setTranslationSettings({\n                earningPerBatch: workData.userSettings.earningPerBatch,\n                plan: workData.userSettings.plan\n            });\n            setHasQuickAdvantage(workData.userSettings.hasQuickAdvantage);\n            setCopyPasteDaysRemaining(workData.userSettings.copyPasteDaysRemaining);\n            setTodayTranslations(workData.todayProgress.todayTranslations);\n            setTotalTranslationsCompleted(workData.todayProgress.totalTranslations);\n            setActiveDays(workData.todayProgress.activeDays || 0);\n            // Convert translations to work format\n            const translationItems = workData.translations.map((item)=>({\n                    english: item.english,\n                    hindi: item.hindi,\n                    spanish: item.spanish,\n                    french: item.french,\n                    german: item.german,\n                    italian: item.italian,\n                    portuguese: item.portuguese,\n                    russian: item.russian,\n                    arabic: item.arabic,\n                    chinese: item.chinese,\n                    japanese: item.japanese,\n                    korean: item.korean,\n                    turkish: item.turkish,\n                    dutch: item.dutch,\n                    swedish: item.swedish,\n                    polish: item.polish,\n                    ukrainian: item.ukrainian,\n                    greek: item.greek,\n                    hebrew: item.hebrew,\n                    vietnamese: item.vietnamese,\n                    thai: item.thai\n                }));\n            setTranslationItems(translationItems);\n            // Initialize session\n            initializeSession();\n            // Generate first translation step if no restored progress\n            if (!currentStep) {\n                generateNewTranslationStep(translationItems);\n            }\n            console.log('✅ Fast initialization complete!');\n        } catch (error) {\n            console.error('❌ Error in fast initialization:', error);\n            // Fallback to old method\n            loadTranslationData();\n            loadTranslationSettings();\n            loadUserData();\n            initializeTranslations();\n        } finally{\n            setIsLoadingTranslations(false);\n        }\n    };\n    const loadUserData = async ()=>{\n        try {\n            const data = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserData)(user.uid);\n            setUserData(data);\n            if (data) {\n                try {\n                    await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.updateUserActiveDays)(user.uid);\n                } catch (error) {\n                    console.error('Error updating active days:', error);\n                }\n                const planStatus = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.isUserPlanExpired)(user.uid);\n                setDaysLeft(planStatus.daysLeft || 0);\n                setActiveDays(planStatus.activeDays || 0);\n                console.log('📊 Plan status loaded:', {\n                    plan: data.plan,\n                    expired: planStatus.expired,\n                    daysLeft: planStatus.daysLeft,\n                    activeDays: planStatus.activeDays,\n                    reason: planStatus.reason\n                });\n            }\n        } catch (error) {\n            console.error('Error loading user data:', error);\n        }\n    };\n    const saveWorkProgress = async ()=>{\n        if (!user || !currentStep) return;\n        try {\n            setIsAutoSaving(true);\n            const progress = {\n                currentStep,\n                userTypedText,\n                selectedLanguage,\n                isTypingComplete,\n                completedTranslations: localTranslationCount,\n                batchProgress: localTranslationCount / 50 * 100,\n                lastSaved: new Date(),\n                userId: user.uid\n            };\n            // Save to session manager\n            _lib_sessionManager__WEBPACK_IMPORTED_MODULE_10__.sessionManager.saveWorkProgress(progress);\n            // Also save to localStorage as backup\n            const progressKey = \"work_progress_backup_\".concat(user.uid);\n            localStorage.setItem(progressKey, JSON.stringify(progress));\n            setLastSaved(new Date());\n            console.log('✅ Work progress saved successfully');\n        } catch (error) {\n            console.error('❌ Error saving work progress:', error);\n        } finally{\n            setIsAutoSaving(false);\n        }\n    };\n    const restoreWorkProgress = ()=>{\n        if (!user) return;\n        try {\n            // Try to restore from session manager first\n            let savedProgress = _lib_sessionManager__WEBPACK_IMPORTED_MODULE_10__.sessionManager.getWorkProgress();\n            // If not found, try localStorage backup\n            if (!savedProgress) {\n                const progressKey = \"work_progress_backup_\".concat(user.uid);\n                const backupData = localStorage.getItem(progressKey);\n                if (backupData) {\n                    savedProgress = JSON.parse(backupData);\n                }\n            }\n            if (savedProgress && savedProgress.userId === user.uid) {\n                // Check if the saved progress is from today\n                const today = new Date().toDateString();\n                const savedDate = new Date(savedProgress.lastSaved).toDateString();\n                if (savedDate === today) {\n                    console.log('🔄 Restoring work progress from:', savedProgress.lastSaved);\n                    // Restore state\n                    setCurrentStep(savedProgress.currentStep);\n                    setUserTypedText(savedProgress.userTypedText);\n                    setSelectedLanguage(savedProgress.selectedLanguage);\n                    setIsTypingComplete(savedProgress.isTypingComplete);\n                    setLocalTranslationCount(savedProgress.completedTranslations);\n                    setLastSaved(new Date(savedProgress.lastSaved));\n                    // Show restoration notification\n                    sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                        icon: 'info',\n                        title: 'Work Progress Restored',\n                        text: \"Your previous work session has been restored. Progress: \".concat(savedProgress.completedTranslations, \"/50 translations.\"),\n                        timer: 3000,\n                        showConfirmButton: false\n                    });\n                    return true // Progress was restored\n                    ;\n                } else {\n                    console.log('🗑️ Clearing old work progress from:', savedDate);\n                    _lib_sessionManager__WEBPACK_IMPORTED_MODULE_10__.sessionManager.clearWorkProgress();\n                    const progressKey = \"work_progress_backup_\".concat(user.uid);\n                    localStorage.removeItem(progressKey);\n                }\n            }\n        } catch (error) {\n            console.error('❌ Error restoring work progress:', error);\n        }\n        return false // No progress was restored\n        ;\n    };\n    const initializeSession = ()=>{\n        const today = new Date().toDateString();\n        const sessionKey = \"translation_session_\".concat(user.uid, \"_\").concat(today);\n        const savedCount = localStorage.getItem(sessionKey);\n        if (savedCount) {\n            const count = parseInt(savedCount);\n            setLocalTranslationCount(count);\n        }\n    };\n    const initializeTranslations = async ()=>{\n        try {\n            setIsLoadingTranslations(true);\n            // Check if we have restored progress\n            const hasRestoredProgress = currentStep !== null;\n            // Initialize translation system with batching\n            const { initializeTranslationSystem } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/translationManager */ \"(app-pages-browser)/./src/lib/translationManager.ts\"));\n            const currentBatchTranslations = await initializeTranslationSystem();\n            // Convert TranslationData to TranslationItem format\n            const translationItems = currentBatchTranslations.map((item)=>({\n                    english: item.english,\n                    hindi: item.hindi,\n                    spanish: item.spanish,\n                    french: item.french,\n                    german: item.german,\n                    italian: item.italian,\n                    portuguese: item.portuguese,\n                    russian: item.russian,\n                    arabic: item.arabic,\n                    chinese: item.chinese,\n                    japanese: item.japanese,\n                    korean: item.korean,\n                    turkish: item.turkish,\n                    dutch: item.dutch,\n                    swedish: item.swedish,\n                    polish: item.polish,\n                    ukrainian: item.ukrainian,\n                    greek: item.greek,\n                    hebrew: item.hebrew,\n                    vietnamese: item.vietnamese,\n                    thai: item.thai\n                }));\n            setTranslationItems(translationItems);\n            // Only generate new step if we don't have restored progress\n            if (!hasRestoredProgress) {\n                generateNewTranslationStep(translationItems);\n            } else {\n                console.log('🔄 Using restored translation step');\n            }\n        } catch (error) {\n            console.error('Error loading translations:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'error',\n                title: 'Loading Error',\n                text: 'Failed to load translation data. Please refresh the page.'\n            });\n        } finally{\n            setIsLoadingTranslations(false);\n        }\n    };\n    // Reset current step (for blocked users)\n    const resetCurrentStep = ()=>{\n        setUserTypedText('');\n        setPreviousValidText('') // Clear previous valid text\n        ;\n        setTypingErrors([]);\n        setIsTypingComplete(false);\n        setSelectedLanguage('');\n        setIsLanguageCorrect(false);\n        setShowTranslation(false);\n        setPasteDetected(false);\n        setTypingBlocked(false);\n        setBlockReason('');\n        setLastInputTime(0);\n        setLastInputLength(0);\n        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n            icon: 'info',\n            title: 'Reset Complete!',\n            text: 'You can now start typing again. Please type carefully.',\n            timer: 2000,\n            showConfirmButton: false\n        });\n    };\n    // Clear only typing errors (for correction)\n    const clearTypingErrors = ()=>{\n        setTypingErrors([]);\n        setTypingBlocked(false);\n        setBlockReason('');\n    };\n    // Generate a new translation step\n    const generateNewTranslationStep = function() {\n        let items = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : translationItems;\n        if (items.length === 0) return;\n        const randomIndex = Math.floor(Math.random() * items.length);\n        const selectedItem = items[randomIndex];\n        const randomTargetLang = (0,_lib_translationManager__WEBPACK_IMPORTED_MODULE_8__.getRandomTargetLanguage)();\n        const targetLangData = _lib_translationManager__WEBPACK_IMPORTED_MODULE_8__.AVAILABLE_LANGUAGES.find((lang)=>lang.code === randomTargetLang);\n        const newStep = {\n            id: \"step_\".concat(Date.now(), \"_\").concat(Math.random()),\n            englishText: selectedItem.english,\n            targetLanguage: randomTargetLang,\n            targetLanguageName: (targetLangData === null || targetLangData === void 0 ? void 0 : targetLangData.name) || 'Unknown',\n            targetTranslation: selectedItem[randomTargetLang] || 'Translation not available',\n            userTypedText: '',\n            selectedLanguage: '',\n            isTypingComplete: false,\n            isLanguageSelected: false,\n            isConverted: false,\n            isSubmitted: false\n        };\n        setCurrentStep(newStep);\n        setUserTypedText('');\n        setPreviousValidText('') // Reset previous valid text for new step\n        ;\n        setTypingErrors([]);\n        setIsTypingComplete(false);\n        setSelectedLanguage('');\n        setIsLanguageCorrect(false);\n        setShowTranslation(false);\n        setPasteDetected(false);\n        setTypingBlocked(false);\n        setBlockReason('');\n    };\n    // Typing validation with paste detection and smart error handling\n    const handleTextInput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkPage.useCallback[handleTextInput]\": (e)=>{\n            if (!currentStep || isTypingComplete) return;\n            const newValue = e.target.value;\n            const currentTime = Date.now();\n            // Initialize timing for first input\n            if (lastInputTime === 0) {\n                setLastInputTime(currentTime);\n                setLastInputLength(0);\n            }\n            // First, validate the input to check for errors\n            const errors = validateTyping(newValue, currentStep.englishText);\n            // For non-advantage users: strict error handling - prevent typing beyond errors\n            if (errors.length > 0 && !hasQuickAdvantage) {\n                const firstErrorIndex = errors[0];\n                // If user is trying to type beyond the first error, block it\n                if (newValue.length > firstErrorIndex + 1) {\n                    // Trim to the error position + 1 character (allow the error character to be visible)\n                    const trimmedValue = newValue.substring(0, firstErrorIndex + 1);\n                    e.target.value = trimmedValue;\n                    setUserTypedText(trimmedValue);\n                    setTypingErrors(validateTyping(trimmedValue, currentStep.englishText));\n                    // Set error state silently (no popup dialogue)\n                    if (!typingBlocked) {\n                        setTypingBlocked(true);\n                        setBlockReason(\"Typing error at position \".concat(firstErrorIndex + 1));\n                    }\n                    return;\n                }\n            }\n            // Only run paste detection if there are no typing errors AND user is not currently in error correction mode\n            // Also skip if user was previously in error state (to avoid false positives during corrections)\n            if (errors.length === 0 && !hasQuickAdvantage && !typingBlocked && detectPasteAttempt(newValue, currentTime)) {\n                // Revert to the last known valid text state (not current state)\n                const revertText = previousValidText;\n                // Revert to previous valid text state\n                setUserTypedText(revertText);\n                e.target.value = revertText;\n                // Show brief warning without blocking\n                setPasteDetected(true);\n                // Provide more specific feedback based on the detection reason\n                const title = blockReason.includes('speed') ? 'Fast Typing Detected!' : 'Paste Not Allowed!';\n                const text = blockReason.includes('speed') ? \"\".concat(blockReason, \". Please type at a moderate pace and continue.\") : \"\".concat(blockReason, \". Please continue typing manually.\");\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'warning',\n                    title,\n                    text,\n                    timer: 2000,\n                    showConfirmButton: false,\n                    toast: true,\n                    position: 'top-end'\n                });\n                // Clear the paste detection flag after a short delay to allow continued typing\n                setTimeout({\n                    \"WorkPage.useCallback[handleTextInput]\": ()=>{\n                        setPasteDetected(false);\n                    }\n                }[\"WorkPage.useCallback[handleTextInput]\"], 1000);\n                return;\n            }\n            // Update previous valid text only when there are no errors\n            if (errors.length === 0) {\n                setPreviousValidText(newValue);\n            }\n            // Update the text and error state\n            setUserTypedText(newValue);\n            setTypingErrors(errors);\n            // Handle error state management\n            if (errors.length > 0) {\n                // User has typing errors\n                if (!typingBlocked) {\n                    setTypingBlocked(true);\n                    setBlockReason(\"Typing error detected\");\n                }\n            } else {\n                // No errors - clear blocked state\n                if (typingBlocked) {\n                    setTypingBlocked(false);\n                    setBlockReason('');\n                }\n            }\n            // Check if typing is complete and correct\n            if (newValue === currentStep.englishText && errors.length === 0) {\n                setIsTypingComplete(true);\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'success',\n                    title: 'Perfect!',\n                    text: 'Text typed correctly. Now select the target language.',\n                    timer: 2000,\n                    showConfirmButton: false\n                });\n            }\n            // Update timing tracking\n            setLastInputTime(currentTime);\n            setLastInputLength(newValue.length);\n        }\n    }[\"WorkPage.useCallback[handleTextInput]\"], [\n        currentStep,\n        isTypingComplete,\n        pasteDetected,\n        typingBlocked,\n        hasQuickAdvantage,\n        userTypedText,\n        previousValidText,\n        blockReason,\n        lastInputTime\n    ]);\n    // Enhanced paste detection with multiple methods (optimized for fast typists)\n    const detectPasteAttempt = (newValue, currentTime)=>{\n        const timeDiff = currentTime - lastInputTime;\n        const lengthDiff = newValue.length - userTypedText.length;\n        // Skip detection if this is the first input or very short text\n        if (lastInputTime === 0 || newValue.length < 4) {\n            return false;\n        }\n        // Skip detection if user was recently in error state (likely correcting)\n        if (typingBlocked || typingErrors.length > 0) {\n            return false;\n        }\n        // Skip detection for small changes (likely normal typing or corrections)\n        if (Math.abs(lengthDiff) <= 1) {\n            return false;\n        }\n        // Method 1: More than 5 characters at once (STRICT RULE - increased from 3 to 5)\n        if (lengthDiff > 5) {\n            console.log('🚫 Paste detected: More than 5 characters at once');\n            setBlockReason('More than 5 characters added at once');\n            return true;\n        }\n        // Method 2: Very fast typing (unrealistic speed - more than 3 chars per 50ms)\n        // Further increased threshold to allow fast typists and avoid false positives during corrections\n        if (lengthDiff > 3 && timeDiff < 50) {\n            console.log('🚫 Paste detected: Unrealistic typing speed (>3 chars in <50ms)');\n            setBlockReason('Typing speed too fast (possible paste)');\n            return true;\n        }\n        // Method 3: Large text block inserted instantly (increased threshold)\n        if (lengthDiff > 15) {\n            console.log('🚫 Paste detected: Large text block');\n            setBlockReason('Large text block added instantly');\n            return true;\n        }\n        // Method 4: Perfect text match with suspicious speed (improved calculation)\n        if (newValue.length > 30 && newValue === (currentStep === null || currentStep === void 0 ? void 0 : currentStep.englishText.substring(0, newValue.length))) {\n            // Calculate characters per second over the entire typing session\n            const totalTime = currentTime - (lastInputTime === 0 ? currentTime : lastInputTime);\n            const charsPerSecond = newValue.length / (totalTime / 1000);\n            // Flag if typing more than 10 characters per second with perfect accuracy\n            if (charsPerSecond > 10 && totalTime > 1000) {\n                console.log('🚫 Paste detected: Perfect match with high speed', {\n                    charsPerSecond,\n                    totalTime\n                });\n                setBlockReason('Perfect text match with unrealistic speed');\n                return true;\n            }\n        }\n        // Method 5: Detect clipboard-like patterns (multiple words at once)\n        if (lengthDiff > 3) {\n            const addedText = newValue.substring(userTypedText.length);\n            const wordCount = addedText.trim().split(/\\s+/).length;\n            // If adding 2+ complete words at once, likely paste\n            if (wordCount >= 2 && addedText.includes(' ')) {\n                console.log('🚫 Paste detected: Multiple words added at once');\n                setBlockReason('Multiple words added simultaneously');\n                return true;\n            }\n        }\n        return false;\n    };\n    // Additional event handlers for paste detection and error correction\n    const handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkPage.useCallback[handleKeyDown]\": (e)=>{\n            if (!hasQuickAdvantage) {\n                // Detect Ctrl+V, Cmd+V\n                if ((e.ctrlKey || e.metaKey) && e.key === 'v') {\n                    e.preventDefault();\n                    sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                        icon: 'warning',\n                        title: 'Paste Not Allowed!',\n                        text: 'Keyboard paste shortcuts are disabled. Please continue typing manually.',\n                        timer: 2000,\n                        toast: true,\n                        position: 'top-end',\n                        showConfirmButton: false\n                    });\n                }\n                // Detect long press (holding a key)\n                if (e.repeat) {\n                    console.log('🚫 Long press detected');\n                    // Allow backspace and delete for corrections\n                    if (e.key !== 'Backspace' && e.key !== 'Delete') {\n                        e.preventDefault();\n                    }\n                }\n            }\n            // Handle any key input for error correction (not just backspace)\n            if (typingErrors.length > 0) {\n                // Allow any editing - the handleTextInput will manage error state\n                setTimeout({\n                    \"WorkPage.useCallback[handleKeyDown]\": ()=>{\n                        // Check if errors are fixed after any key input\n                        const newValue = e.target.value;\n                        const newErrors = validateTyping(newValue, (currentStep === null || currentStep === void 0 ? void 0 : currentStep.englishText) || '');\n                        if (newErrors.length === 0) {\n                            clearTypingErrors();\n                        }\n                    }\n                }[\"WorkPage.useCallback[handleKeyDown]\"], 10);\n            }\n        }\n    }[\"WorkPage.useCallback[handleKeyDown]\"], [\n        hasQuickAdvantage,\n        typingErrors,\n        currentStep\n    ]);\n    // Detect drag and drop\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkPage.useCallback[handleDrop]\": (e)=>{\n            if (!hasQuickAdvantage) {\n                e.preventDefault();\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'warning',\n                    title: 'Drag & Drop Not Allowed!',\n                    text: 'Please continue typing the text manually.',\n                    timer: 2000,\n                    toast: true,\n                    position: 'top-end',\n                    showConfirmButton: false\n                });\n            }\n        }\n    }[\"WorkPage.useCallback[handleDrop]\"], [\n        hasQuickAdvantage\n    ]);\n    // Detect right-click context menu\n    const handleContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkPage.useCallback[handleContextMenu]\": (e)=>{\n            if (!hasQuickAdvantage) {\n                e.preventDefault();\n                sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                    icon: 'warning',\n                    title: 'Context Menu Disabled',\n                    text: 'Right-click menu is disabled to prevent paste operations.',\n                    timer: 1500\n                });\n            }\n        }\n    }[\"WorkPage.useCallback[handleContextMenu]\"], [\n        hasQuickAdvantage\n    ]);\n    // Validate typing character by character\n    const validateTyping = (userText, targetText)=>{\n        const errors = [];\n        for(let i = 0; i < userText.length; i++){\n            if (i >= targetText.length || userText[i] !== targetText[i]) {\n                errors.push(i);\n            }\n        }\n        return errors;\n    };\n    // Handle language selection\n    const handleLanguageSelect = (languageCode)=>{\n        if (!currentStep || !isTypingComplete) return;\n        setSelectedLanguage(languageCode);\n        if (languageCode === currentStep.targetLanguage) {\n            setIsLanguageCorrect(true);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'success',\n                title: 'Correct Language!',\n                text: 'You selected the correct language. Click Convert to see the translation.',\n                timer: 2000,\n                showConfirmButton: false\n            });\n        } else {\n            setIsLanguageCorrect(false);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'error',\n                title: 'Wrong Language!',\n                text: \"Please select \".concat(currentStep.targetLanguageName, \" language.\"),\n                timer: 2000,\n                showConfirmButton: false\n            });\n        }\n    };\n    // Handle convert button click\n    const handleConvert = ()=>{\n        if (!currentStep || !isLanguageCorrect) return;\n        setShowTranslation(true);\n        setCurrentStep((prev)=>prev ? {\n                ...prev,\n                isConverted: true\n            } : null);\n    };\n    // Handle submit translation\n    const handleSubmitTranslation = ()=>{\n        if (!currentStep || !showTranslation) return;\n        // Prevent submitting more than 50 translations\n        if (localTranslationCount >= 50) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'warning',\n                title: 'Daily Limit Reached!',\n                text: 'You have already completed 50 translations for today. Please submit your batch to earn rewards.',\n                timer: 3000,\n                showConfirmButton: false\n            });\n            return;\n        }\n        // Mark current step as submitted\n        setCurrentStep((prev)=>prev ? {\n                ...prev,\n                isSubmitted: true\n            } : null);\n        // Increment local count\n        const newLocalCount = localTranslationCount + 1;\n        setLocalTranslationCount(newLocalCount);\n        // Save to localStorage\n        const today = new Date().toDateString();\n        const sessionKey = \"translation_session_\".concat(user.uid, \"_\").concat(today);\n        localStorage.setItem(sessionKey, newLocalCount.toString());\n        // Show progress\n        if (newLocalCount < 50) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'success',\n                title: 'Translation Submitted!',\n                text: \"Progress: \".concat(newLocalCount, \"/50 translations completed.\"),\n                timer: 2000,\n                showConfirmButton: false\n            }).then(()=>{\n                // Generate next translation step\n                generateNewTranslationStep();\n            });\n        } else {\n            // Exactly 50 translations completed - show final submit option\n            setCanSubmitBatch(true);\n            setShowFinalSubmit(true);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'success',\n                title: '🎉 All Translations Completed!',\n                text: 'You have completed all 50 translations! Click \"Submit & Earn\" to get your rewards.',\n                timer: 3000,\n                showConfirmButton: false\n            });\n            // Clear current step to prevent further translations\n            setCurrentStep(null);\n            setShowTranslation(false);\n            setIsTypingComplete(false);\n            setUserTypedText('');\n            setSelectedLanguage('');\n            setIsLanguageCorrect(false);\n        }\n    };\n    const submitTranslations = async ()=>{\n        if (!canSubmitBatch || isSubmitting || localTranslationCount < 50) return;\n        // Check if work is blocked due to leave\n        if (isLeaveBlocked) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'warning',\n                title: 'Submission Not Available',\n                text: leaveStatus.reason || 'Translation submission is not available due to leave.',\n                confirmButtonText: 'Go to Dashboard'\n            }).then(()=>{\n                window.location.href = '/dashboard';\n            });\n            return;\n        }\n        try {\n            setIsSubmitting(true);\n            // Calculate earning for the batch of 50 translations\n            const batchEarningAmount = translationSettings.earningPerBatch;\n            // Update translation count in database (add 50 translations)\n            for(let i = 0; i < 50; i++){\n                await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.updateVideoCount)(user.uid);\n            }\n            // Add batch earning to wallet\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.updateWalletBalance)(user.uid, batchEarningAmount);\n            // Add transaction record for the batch\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.addTransaction)(user.uid, {\n                type: 'translation_earning',\n                amount: batchEarningAmount,\n                description: \"Batch completion reward - 50 translations completed\"\n            });\n            // Update local state\n            const newTodayTranslations = Math.min(todayTranslations + 50, 50);\n            setTodayTranslations(newTodayTranslations);\n            setTotalTranslationsCompleted(totalTranslationsCompleted + 50);\n            // Clear local session data\n            const today = new Date().toDateString();\n            const sessionKey = \"translation_session_\".concat(user.uid, \"_\").concat(today);\n            localStorage.removeItem(sessionKey);\n            setLocalTranslationCount(0);\n            setCanSubmitBatch(false);\n            setShowFinalSubmit(false);\n            setIsDailyCompleted(true) // Mark daily session as completed\n            ;\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'success',\n                title: '🎉 Daily Session Completed!',\n                html: '\\n          <div class=\"text-center\">\\n            <p class=\"text-lg font-bold text-green-600 mb-2\">₹'.concat(batchEarningAmount, ' Earned!</p>\\n            <p class=\"mb-2\">50 translations completed and submitted</p>\\n            <p class=\"text-sm text-gray-600 mb-3\">Earnings have been added to your wallet</p>\\n            <p class=\"text-sm text-blue-600 font-semibold\">\\n              \\uD83C\\uDF89 Your daily session is complete! Come back tomorrow for your next session.\\n            </p>\\n          </div>\\n        '),\n                confirmButtonText: 'Go to Dashboard',\n                timer: 6000,\n                showConfirmButton: true\n            }).then(()=>{\n                window.location.href = '/dashboard';\n            });\n        } catch (error) {\n            console.error('Error submitting translations:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                icon: 'error',\n                title: 'Submission Failed',\n                text: 'There was an error submitting your translations. Please try again.'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (loading || isLoadingTranslations || isChecking) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading translations...'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1188,\n            columnNumber: 7\n        }, this);\n    }\n    // Show loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1204,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: \"Loading work page...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1205,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1203,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1202,\n            columnNumber: 7\n        }, this);\n    }\n    // If user is not authenticated, show loading (useRequireAuth will handle redirect)\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: \"Authenticating...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1214,\n            columnNumber: 7\n        }, this);\n    }\n    // Show blocking notifications if any exist\n    if (hasBlockingNotifications && user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            userId: user.uid,\n            onAllRead: markAllAsRead\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1226,\n            columnNumber: 7\n        }, this);\n    }\n    // Show daily completion lockout\n    if (isDailyCompleted && user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center shadow-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-check-circle text-6xl text-green-400 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"Daily Work Completed! \\uD83C\\uDF89\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-lg\",\n                                children: \"You've successfully completed your 50 translations for today.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1243,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1238,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/5 rounded-xl p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/70\",\n                                        children: \"Today's Earnings:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-400 font-bold text-xl\",\n                                        children: [\n                                            \"₹\",\n                                            translationSettings.earningPerBatch\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1251,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/70\",\n                                        children: \"Translations Completed:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-semibold\",\n                                        children: \"50/50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1253,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white/70\",\n                                        children: \"Next Session:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400 font-semibold\",\n                                        children: \"Tomorrow\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1257,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white/60 text-sm mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-clock mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1265,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Your work session is locked until tomorrow\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1264,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-calendar-alt mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Come back tomorrow for your next 50 translations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1268,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1263,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/dashboard',\n                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-home mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1278,\n                                columnNumber: 13\n                            }, this),\n                            \"Go to Dashboard\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1274,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n            lineNumber: 1236,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700\",\n        children: [\n            showSessionRecovery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionRecovery__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                onProgressRestored: handleProgressRestored,\n                onRecoveryComplete: handleSessionRecoveryComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1290,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"glass-card p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard\",\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-arrow-left mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1300,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Back to Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Translate Text & Earn\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            \"Plan: \",\n                                            translationSettings.plan\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            \"₹\",\n                                            translationSettings.earningPerBatch,\n                                            \"/batch (50 translations)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1306,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1304,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-5 gap-2 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-blue-400\",\n                                        children: todayTranslations\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"Today TL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-green-400\",\n                                        children: totalTranslationsCompleted\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1317,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"Total TL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1318,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1316,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-purple-400\",\n                                        children: Math.max(0, 50 - localTranslationCount)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"TL Left\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold text-orange-400\",\n                                        children: [\n                                            activeDays,\n                                            \"/\",\n                                            translationSettings.plan === 'Trial' ? '2' : '30'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"Active Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1328,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-bold \".concat(hasQuickAdvantage ? 'text-green-400' : 'text-gray-400'),\n                                        children: hasQuickAdvantage ? copyPasteDaysRemaining : '0'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80 text-xs\",\n                                        children: \"Copy Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mt-3\",\n                        children: isAutoSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-yellow-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-spinner fa-spin mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1342,\n                                    columnNumber: 15\n                                }, this),\n                                \"Saving progress...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                            lineNumber: 1341,\n                            columnNumber: 13\n                        }, this) : lastSaved ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-check mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1347,\n                                    columnNumber: 15\n                                }, this),\n                                \"Last saved: \",\n                                lastSaved.toLocaleTimeString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                            lineNumber: 1346,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-400 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-shield-alt mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1352,\n                                    columnNumber: 15\n                                }, this),\n                                \"Auto-save protection enabled\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                            lineNumber: 1351,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1339,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-language mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1363,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Translate Text & Earn\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1362,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: resetCurrentStep,\n                                className: \"glass-button px-3 py-1 text-white text-sm\",\n                                title: \"Clear typed text and reset\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-eraser mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1371,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1361,\n                        columnNumber: 9\n                    }, this),\n                    !currentStep && !isLoadingTranslations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 rounded-lg p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1379,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-4\",\n                                    children: \"No Translation Available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/80 mb-6\",\n                                    children: \"Unable to load translation data. This could be due to:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1381,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-white/70 text-left max-w-md mx-auto mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mb-2\",\n                                            children: \"• Translation data file not found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1385,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mb-2\",\n                                            children: \"• Network connectivity issues\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1386,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mb-2\",\n                                            children: \"• Server maintenance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1384,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"btn-primary px-6 py-3 rounded-lg font-semibold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-sync-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1393,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Retry Loading\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                            lineNumber: 1378,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1377,\n                        columnNumber: 11\n                    }, this),\n                    currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-keyboard mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 1: Type the English text below\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 17\n                                            }, this),\n                                            hasQuickAdvantage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    navigator.clipboard.writeText(currentStep.englishText);\n                                                    sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                                        icon: 'success',\n                                                        title: 'Copied!',\n                                                        text: 'English text copied to clipboard',\n                                                        timer: 1500,\n                                                        showConfirmButton: false\n                                                    });\n                                                },\n                                                className: \"group relative bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 animate-pulse\",\n                                                title: \"Copy English text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-copy mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1425,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Copy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1427,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/60 text-xs bg-white/10 px-3 py-2 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-lock mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1431,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Copy disabled - Type manually\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-base md:text-lg font-mono leading-relaxed\",\n                                                    children: typingErrors.length > 0 && !hasQuickAdvantage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400\",\n                                                                children: currentStep.englishText.substring(0, typingErrors[0])\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                                lineNumber: 1442,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-red-500 text-white px-1 rounded animate-pulse\",\n                                                                children: currentStep.englishText[typingErrors[0]]\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                                lineNumber: 1445,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/60\",\n                                                                children: currentStep.englishText.substring(typingErrors[0] + 1)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                                lineNumber: 1448,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : currentStep.englishText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1438,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1437,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-white/60 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-info-circle mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1458,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    typingErrors.length > 0 && !hasQuickAdvantage ? \"Error at highlighted character (position \".concat(typingErrors[0] + 1, \")\") : \"Scroll to see full text if needed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1436,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: userTypedText,\n                                                onChange: handleTextInput,\n                                                onKeyDown: handleKeyDown,\n                                                onDrop: handleDrop,\n                                                onContextMenu: handleContextMenu,\n                                                disabled: isTypingComplete,\n                                                placeholder: typingErrors.length > 0 ? \"Fix the highlighted error and continue typing...\" : hasQuickAdvantage ? \"Type or paste the English text here...\" : \"Type the English text here (copy-paste not allowed). Fast typists: please type at moderate speed to avoid triggering anti-paste protection.\",\n                                                className: \"w-full h-24 md:h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono text-sm md:text-base leading-relaxed \".concat(typingErrors.length > 0 ? 'border-red-500' : '', \" \").concat(isTypingComplete ? 'border-green-500 bg-green-500/10' : ''),\n                                                onPaste: (e)=>{\n                                                    if (!hasQuickAdvantage) {\n                                                        e.preventDefault();\n                                                        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                                            icon: 'warning',\n                                                            title: 'Paste Not Allowed!',\n                                                            text: 'Please continue typing the text manually.',\n                                                            timer: 2000,\n                                                            toast: true,\n                                                            position: 'top-end',\n                                                            showConfirmButton: false\n                                                        });\n                                                    }\n                                                },\n                                                onDragOver: (e)=>{\n                                                    if (!hasQuickAdvantage) {\n                                                        e.preventDefault();\n                                                    }\n                                                },\n                                                spellCheck: false,\n                                                autoComplete: \"off\",\n                                                autoCorrect: \"off\",\n                                                autoCapitalize: \"off\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1467,\n                                                columnNumber: 17\n                                            }, this),\n                                            hasQuickAdvantage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        const text = await navigator.clipboard.readText();\n                                                        setUserTypedText(text);\n                                                        handleTextInput({\n                                                            target: {\n                                                                value: text\n                                                            }\n                                                        });\n                                                        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                                            icon: 'success',\n                                                            title: 'Pasted!',\n                                                            text: 'Text pasted from clipboard',\n                                                            timer: 1500,\n                                                            showConfirmButton: false\n                                                        });\n                                                    } catch (error) {\n                                                        sweetalert2__WEBPACK_IMPORTED_MODULE_13___default().fire({\n                                                            icon: 'error',\n                                                            title: 'Paste Failed',\n                                                            text: 'Could not access clipboard',\n                                                            timer: 1500,\n                                                            showConfirmButton: false\n                                                        });\n                                                    }\n                                                },\n                                                className: \"group absolute top-3 right-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-2 px-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 animate-bounce disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:animate-none\",\n                                                title: \"Paste from clipboard\",\n                                                disabled: isTypingComplete,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-paste mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Paste\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1539,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1540,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1511,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1466,\n                                        columnNumber: 15\n                                    }, this),\n                                    typingErrors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-red-400 text-sm bg-red-500/10 border border-red-500/30 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-exclamation-triangle mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1548,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Typing Error Detected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1549,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1547,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-300 text-xs mb-2\",\n                                                children: [\n                                                    \"Error at position \",\n                                                    typingErrors[0] + 1,\n                                                    ': Expected \"',\n                                                    currentStep.englishText[typingErrors[0]],\n                                                    '\" but got \"',\n                                                    userTypedText[typingErrors[0]] || 'nothing',\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1551,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-200 text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-edit mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1555,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Edit the text box to correct the mistake, then continue typing.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1554,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1546,\n                                        columnNumber: 17\n                                    }, this),\n                                    isTypingComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-green-400 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-check-circle mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1563,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Perfect! Text typed correctly.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1562,\n                                        columnNumber: 17\n                                    }, this),\n                                    pasteDetected && !hasQuickAdvantage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-400 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-exclamation-triangle mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1571,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Paste Attempt Detected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1572,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1570,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-300 text-xs mt-1\",\n                                                children: blockReason.includes('speed') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-info-circle mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1577,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Fast typing detected. Please type at a moderate pace. You can continue typing normally.\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-clipboard mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1582,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Paste operation blocked. Please continue typing manually from where you left off.\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1574,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-yellow-200 text-xs mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-arrow-right mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 1588,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"This message will disappear automatically. Continue typing normally.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1587,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1569,\n                                        columnNumber: 17\n                                    }, this),\n                                    typingErrors.length > 0 && !hasQuickAdvantage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/30 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 text-sm font-semibold mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-lightbulb mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1601,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"How to Fix the Error:\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1600,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-300 text-xs space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"1. Click in the text box and edit the incorrect character\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1605,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                '2. Change it to the correct character: \"',\n                                                                currentStep.englishText[typingErrors[0]],\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1606,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"3. Continue typing the rest of the text\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                            lineNumber: 1607,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1604,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1599,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1598,\n                                        columnNumber: 17\n                                    }, this),\n                                    userTypedText && !isTypingComplete && typingErrors.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                // Manually trigger text validation\n                                                handleTextInput({\n                                                    target: {\n                                                        value: userTypedText\n                                                    }\n                                                });\n                                            },\n                                            className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n                                            title: \"Check if typed text is correct\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-check-circle mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1624,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Check Text\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1616,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1615,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1403,\n                                columnNumber: 13\n                            }, this),\n                            isTypingComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-globe mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1635,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Step 2: Select the target language - \",\n                                            currentStep.targetLanguageName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1634,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedLanguage,\n                                        onChange: (e)=>handleLanguageSelect(e.target.value),\n                                        className: \"w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                className: \"bg-gray-800 text-white\",\n                                                children: \"Select target language...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1643,\n                                                columnNumber: 19\n                                            }, this),\n                                            _lib_translationManager__WEBPACK_IMPORTED_MODULE_8__.AVAILABLE_LANGUAGES.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: lang.code,\n                                                    className: \"bg-gray-800 text-white\",\n                                                    children: [\n                                                        lang.flag,\n                                                        \" \",\n                                                        lang.name\n                                                    ]\n                                                }, lang.code, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1645,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1638,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedLanguage && !isLanguageCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-red-400 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-times-circle mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1653,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Wrong language! Please select \",\n                                            currentStep.targetLanguageName,\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1652,\n                                        columnNumber: 19\n                                    }, this),\n                                    isLanguageCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-green-400 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-check-circle mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1660,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Correct language selected!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1659,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1633,\n                                columnNumber: 15\n                            }, this),\n                            isLanguageCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleConvert,\n                                    disabled: showTranslation,\n                                    className: \"px-8 py-3 rounded-lg font-semibold transition-all duration-300 \".concat(showTranslation ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-primary hover:scale-105'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-exchange-alt mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1679,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Convert to \",\n                                        currentStep.targetLanguageName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1670,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1669,\n                                columnNumber: 15\n                            }, this),\n                            showTranslation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-language mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1689,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentStep.targetLanguageName,\n                                            \" Translation:\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1688,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 p-3 rounded border-l-4 border-green-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg\",\n                                            children: currentStep.targetTranslation\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1693,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1692,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSubmitTranslation,\n                                            className: \"btn-success px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-check mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1703,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Submit Translation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1699,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1698,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1687,\n                                columnNumber: 15\n                            }, this),\n                            showFinalSubmit && !isDailyCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 mb-4 shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-bold text-xl mb-2\",\n                                                children: \"\\uD83C\\uDF89 Congratulations! You've completed 50 translations!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1714,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/90 mb-4\",\n                                                children: \"Click the button below to submit your daily batch and receive your earnings.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1717,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80 text-sm\",\n                                                children: \"⚠️ After submission, you won't be able to work until tomorrow.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 1720,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1713,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: submitTranslations,\n                                        disabled: isSubmitting,\n                                        className: \"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 px-12 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none animate-pulse\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-spinner fa-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1732,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submitting Final Batch...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-trophy mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                    lineNumber: 1737,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Submit Final Batch & Earn ₹\",\n                                                translationSettings.earningPerBatch\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1725,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1712,\n                                columnNumber: 15\n                            }, this),\n                            canSubmitBatch && !showFinalSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: submitTranslations,\n                                    disabled: isSubmitting,\n                                    className: \"btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-money-bill-wave mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1753,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Submit All 50 Translations & Earn ₹\",\n                                        translationSettings.earningPerBatch\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                    lineNumber: 1748,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1747,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80\",\n                                        children: [\n                                            \"Progress: \",\n                                            localTranslationCount,\n                                            \"/50 translations completed\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1761,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-white/20 rounded-full h-2 mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(localTranslationCount / 50 * 100, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                            lineNumber: 1765,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 1764,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 1760,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 1401,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 1360,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\work\\\\page.tsx\",\n        lineNumber: 1287,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkPage, \"E++vB29iQ75Ygo362K3dp6og/LU=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth,\n        _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications,\n        _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor\n    ];\n});\n_c = WorkPage;\nvar _c;\n$RefreshReg$(_c, \"WorkPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/work/page.tsx\n"));

/***/ })

});