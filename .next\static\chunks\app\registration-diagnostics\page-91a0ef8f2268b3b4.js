(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[139,6104],{921:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(5155),s=a(2115),c=a(3004),i=a(5317),n=a(6104),o=a(3592);function l(){let[e,t]=(0,s.useState)([]),[a,l]=(0,s.useState)(!1),[d,u]=(0,s.useState)(""),m=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",r=new Date().toLocaleTimeString(),s="[".concat(r,"] ").concat("success"===a?"✅":"error"===a?"❌":"warning"===a?"⚠️":"ℹ️"," ").concat(e);t(e=>[...e,s]),console.log(s)},h=()=>{t([])},g=async()=>{l(!0),h();try{var e,t,a;let r=d||"diagnostic".concat(Date.now(),"@test.com");m("\uD83D\uDE80 Starting Registration Diagnostics","info"),m("\uD83D\uDCE7 Test Email: ".concat(r)),m("\uD83D\uDD27 Firebase Project: ".concat("instra-global")),m("\n=== STEP 1: Firebase Configuration Check ==="),m("API Key: ".concat("Present")),m("Auth Domain: ".concat("instra-global.firebaseapp.com")),m("Project ID: ".concat("instra-global")),m("App ID: ".concat("Present")),m("\n=== STEP 2: Firebase Auth Test ===");let s=null;try{m("Creating Firebase Auth user..."),s=(await (0,c.eJ)(n.j2,r,"test123456")).user,m("Auth user created successfully: ".concat(s.uid),"success"),m("User email: ".concat(s.email)),m("Email verified: ".concat(s.emailVerified))}catch(e){throw m("Auth creation failed: ".concat(e.message),"error"),m("Auth error code: ".concat(e.code),"error"),e}m("\n=== STEP 3: Auth State Propagation ==="),m("Waiting 2 seconds for auth state to propagate..."),await new Promise(e=>setTimeout(e,2e3)),m("Current auth user: ".concat(null==(e=n.j2.currentUser)?void 0:e.uid)),m("Auth state matches: ".concat((null==(t=n.j2.currentUser)?void 0:t.uid)===s.uid),(null==(a=n.j2.currentUser)?void 0:a.uid)===s.uid?"success":"warning"),m("\n=== STEP 4: Referral Code Generation ===");let l="";try{m("Generating referral code..."),l=await (0,o.x4)(),m("Referral code generated: ".concat(l),"success")}catch(e){m("Referral code generation failed: ".concat(e.message),"error"),m("Using fallback referral code...","warning"),l="TN".concat(Date.now().toString().slice(-4)),m("Fallback referral code: ".concat(l))}m("\n=== STEP 5: User Data Preparation ===");let u={[o.Yr.name]:"Diagnostic Test User",[o.Yr.email]:r.toLowerCase(),[o.Yr.mobile]:"9876543210",[o.Yr.referralCode]:l,[o.Yr.referredBy]:"",[o.Yr.referralBonusCredited]:!1,[o.Yr.plan]:"Trial",[o.Yr.planExpiry]:null,[o.Yr.activeDays]:1,[o.Yr.joinedDate]:i.Dc.now(),[o.Yr.wallet]:0,[o.Yr.totalTranslations]:0,[o.Yr.todayTranslations]:0,[o.Yr.lastTranslationDate]:null,status:"active"};m("User data prepared with ".concat(Object.keys(u).length," fields"),"success"),m("Data fields: ".concat(Object.keys(u).join(", "))),m("\n=== STEP 6: Firestore Document Creation ===");try{let e=(0,i.H9)(n.db,o.COLLECTIONS.users,s.uid);m("Document path: ".concat(e.path)),m("Collection: ".concat(o.COLLECTIONS.users)),m("Document ID: ".concat(s.uid)),m("Attempting to create Firestore document..."),await (0,i.BN)(e,u),m("Firestore document created successfully!","success")}catch(e){throw m("Firestore creation failed: ".concat(e.message),"error"),m("Firestore error code: ".concat(e.code),"error"),m("Full error: ".concat(JSON.stringify(e,null,2)),"error"),e}m("\n=== STEP 7: Document Verification ===");try{let e=(0,i.H9)(n.db,o.COLLECTIONS.users,s.uid),t=await (0,i.x7)(e);if(t.exists()){let e=t.data();m("Document verification successful!","success"),m("Retrieved ".concat(Object.keys(e).length," fields")),m("Name: ".concat(e[o.Yr.name])),m("Email: ".concat(e[o.Yr.email])),m("Plan: ".concat(e[o.Yr.plan]))}else throw m("Document does not exist after creation!","error"),Error("Document verification failed")}catch(e){throw m("Document verification failed: ".concat(e.message),"error"),e}m("\n\uD83C\uDF89 All diagnostics passed! Registration should work.","success")}catch(e){m("\n\uD83D\uDCA5 Diagnostics failed at: ".concat(e.message),"error"),m("Error code: ".concat(e.code||"N/A"),"error"),m("Error stack: ".concat(e.stack),"error")}finally{l(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Registration Diagnostics"}),(0,r.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"Test Email (optional - will auto-generate if empty)"}),(0,r.jsx)("input",{type:"email",value:d,onChange:e=>u(e.target.value),placeholder:"<EMAIL>",className:"form-input"})]}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{onClick:g,disabled:a,className:"btn-primary",children:a?"Running Diagnostics...":"Run Registration Diagnostics"}),(0,r.jsx)("button",{onClick:h,disabled:a,className:"btn-secondary",children:"Clear Logs"})]})]}),(0,r.jsx)("div",{className:"bg-black/30 rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,r.jsx)("div",{className:"text-white font-mono text-sm space-y-1",children:0===e.length?(0,r.jsx)("div",{className:"text-white/60",children:'Click "Run Registration Diagnostics" to start...'}):e.map((e,t)=>(0,r.jsx)("div",{className:"whitespace-pre-wrap",children:e},t))})}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-blue-500/20 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-2",children:"What this test does:"}),(0,r.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Checks Firebase configuration"}),(0,r.jsx)("li",{children:"• Tests Firebase Auth user creation"}),(0,r.jsx)("li",{children:"• Verifies auth state propagation"}),(0,r.jsx)("li",{children:"• Tests referral code generation"}),(0,r.jsx)("li",{children:"• Attempts Firestore document creation"}),(0,r.jsx)("li",{children:"• Verifies document was created successfully"})]})]}),(0,r.jsxs)("div",{className:"mt-4 space-x-4",children:[(0,r.jsx)("a",{href:"/register",className:"btn-primary inline-block",children:"Go to Registration"}),(0,r.jsx)("a",{href:"/debug-registration-simple",className:"btn-secondary inline-block",children:"Debug Registration"})]})]})})})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>o});var r=a(3915),s=a(3004),c=a(5317),i=a(858);let n=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),o=(0,s.xI)(n),l=(0,c.aU)(n);(0,i.c7)(n)},7394:(e,t,a)=>{Promise.resolve().then(a.bind(a,921))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,3592,8441,1684,7358],()=>t(7394)),_N_E=e.O()}]);