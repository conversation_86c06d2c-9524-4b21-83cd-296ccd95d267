(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{1469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return l},getImageProps:function(){return n}});let a=t(8229),r=t(8883),i=t(3063),o=a._(t(1193));function n(e){let{props:s}=(0,r.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let l=i.Image},3621:(e,s,t)=>{Promise.resolve().then(t.bind(t,4622))},4622:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(5155),r=t(2115),i=t(6874),o=t.n(i),n=t(6766),l=t(3004),c=t(6104),d=t(6681),h=t(4752),u=t.n(h);function f(){let{user:e,loading:s}=(0,d.hD)(),[t,i]=(0,r.useState)(""),[h,f]=(0,r.useState)(""),[w,m]=(0,r.useState)(""),[x,p]=(0,r.useState)(""),[b,j]=(0,r.useState)(!1),[v,N]=(0,r.useState)(!0),[g,y]=(0,r.useState)(!1),[k,P]=(0,r.useState)(!1),[S,T]=(0,r.useState)(!1),[R,C]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e&&!s&&(window.location.href="/dashboard")},[e,s]),(0,r.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("oobCode");e?(i(e),L(e)):(N(!1),u().fire({icon:"error",title:"Invalid Reset Link",text:"This password reset link is invalid or has expired. Please request a new one.",confirmButtonText:"Go to Forgot Password"}).then(()=>{window.location.href="/forgot-password"}))},[]);let L=async e=>{try{N(!0);let s=await (0,l.RE)(c.j2,e);f(s),y(!0)}catch(s){console.error("Code verification error:",s);let e="This password reset link is invalid or has expired.";switch(s.code){case"auth/expired-action-code":e="This password reset link has expired. Please request a new one.";break;case"auth/invalid-action-code":e="This password reset link is invalid. Please request a new one.";break;case"auth/user-disabled":e="This account has been disabled. Please contact support.";break;case"auth/user-not-found":e="No account found for this reset link. The account may have been deleted."}u().fire({icon:"error",title:"Invalid Reset Link",text:e,confirmButtonText:"Request New Reset Link"}).then(()=>{window.location.href="/forgot-password"})}finally{N(!1)}},_=async e=>{if(e.preventDefault(),!w.trim())return void u().fire({icon:"error",title:"Password Required",text:"Please enter a new password"});if(w.length<6)return void u().fire({icon:"error",title:"Password Too Short",text:"Password must be at least 6 characters long"});if(w!==x)return void u().fire({icon:"error",title:"Passwords Don't Match",text:"Please make sure both passwords match"});j(!0);try{await (0,l.R4)(c.j2,t,w),C(!0),u().fire({icon:"success",title:"Password Reset Successful!",text:"Your password has been updated successfully. You can now login with your new password.",confirmButtonText:"Go to Login",confirmButtonColor:"#3b82f6"}).then(()=>{window.location.href="/login"})}catch(s){console.error("Password reset error:",s);let e="An error occurred while resetting your password";switch(s.code){case"auth/expired-action-code":e="This password reset link has expired. Please request a new one.";break;case"auth/invalid-action-code":e="This password reset link is invalid. Please request a new one.";break;case"auth/weak-password":e="Password is too weak. Please choose a stronger password.";break;default:e=s.message||"Failed to reset password"}u().fire({icon:"error",title:"Reset Failed",text:e})}finally{j(!1)}};return s||v?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:v?"Verifying reset link...":"Loading..."})]})}):g?(0,a.jsx)("main",{className:"min-h-screen flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(o(),{href:"/",className:"inline-block",children:(0,a.jsx)(n.default,{src:"/logo.png",alt:"MyTube",width:120,height:120,className:"mx-auto mb-4"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Set New Password"}),(0,a.jsxs)("p",{className:"text-white/80",children:["Enter your new password for ",(0,a.jsx)("span",{className:"font-semibold text-blue-400",children:h})]})]}),(0,a.jsxs)("form",{onSubmit:_,className:"glass-card p-8 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"newPassword",className:"block text-white font-medium mb-2",children:"New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,a.jsx)("input",{type:k?"text":"password",id:"newPassword",value:w,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Enter new password",disabled:b}),(0,a.jsx)("button",{type:"button",onClick:()=>P(!k),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,a.jsx)("i",{className:"fas ".concat(k?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,a.jsx)("input",{type:S?"text":"password",id:"confirmPassword",value:x,onChange:e=>p(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Confirm new password",disabled:b}),(0,a.jsx)("button",{type:"button",onClick:()=>T(!S),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,a.jsx)("i",{className:"fas ".concat(S?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:b,className:"w-full btn-primary flex items-center justify-center",children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Updating Password..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-check mr-2"}),"Update Password"]})})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)(o(),{href:"/login",className:"text-white/80 hover:text-white transition-colors flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Login"]})})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"fas fa-times text-red-400 text-2xl"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Invalid Reset Link"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"This password reset link is invalid or has expired."}),(0,a.jsx)(o(),{href:"/forgot-password",className:"btn-primary",children:"Request New Reset Link"})]})})}},6766:(e,s,t)=>{"use strict";t.d(s,{default:()=>r.a});var a=t(1469),r=t.n(a)}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3063,6681,8441,1684,7358],()=>s(3621)),_N_E=e.O()}]);