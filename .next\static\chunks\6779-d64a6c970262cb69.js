"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779],{6779:(t,a,e)=>{e.d(a,{CF:()=>c,Pn:()=>l,TK:()=>D,getWithdrawals:()=>w,hG:()=>h,lo:()=>s,nQ:()=>u,searchUsers:()=>d,updateWithdrawalStatus:()=>p});var o=e(5317),r=e(6104),n=e(3592);let i=new Map;async function l(){let t="dashboard-stats",a=function(t){let a=i.get(t);return a&&Date.now()-a.timestamp<3e5?a.data:null}(t);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let e=o.Dc.fromDate(a),l=await (0,o.getDocs)((0,o.collection)(r.db,n.COLLECTIONS.users)),s=l.size,d=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o._M)(n.Yr.joinedDate,">=",e)),c=(await (0,o.getDocs)(d)).size,u=0,w=0,D=0,h=0;l.forEach(t=>{var e;let o=t.data(),r=Number(o[n.Yr.totalTranslations])||0,i=Number(o[n.Yr.wallet])||0;u+=r,w+=i;let l=null==(e=o[n.Yr.lastTranslationDate])?void 0:e.toDate();if(l&&l.toDateString()===a.toDateString()){let t=Number(o[n.Yr.todayTranslations])||0;D+=t}});try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.transactions),(0,o._M)(n.Yr.type,"==","translation_earning"),(0,o.AB)(1e3));(await (0,o.getDocs)(t)).forEach(t=>{var e;let o=t.data(),r=null==(e=o[n.Yr.date])?void 0:e.toDate();if(r&&r>=a){let t=Number(o[n.Yr.amount])||0;h+=t}})}catch(t){console.warn("Could not fetch today's transactions:",t)}let p=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o._M)("status","==","pending")),g=(await (0,o.getDocs)(p)).size,y=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o._M)("date",">=",e)),v=(await (0,o.getDocs)(y)).size,E={totalUsers:Number(s)||0,totalTranslations:Number(u)||0,totalEarnings:Number(w)||0,pendingWithdrawals:Number(g)||0,todayUsers:Number(c)||0,todayTranslations:Number(D)||0,todayEarnings:Number(h)||0,todayWithdrawals:Number(v)||0};return i.set(t,{data:E,timestamp:Date.now()}),E}catch(t){throw console.error("Error getting admin dashboard stats:",t),t}}async function s(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.Yr.joinedDate,"desc"),(0,o.AB)(t));a&&(e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.Yr.joinedDate,"desc"),(0,o.HM)(a),(0,o.AB)(t)));let i=await (0,o.getDocs)(e);return{users:i.docs.map(t=>{var a,e,o,r,i,l,s;let d=t.data();return{id:t.id,...d,joinedDate:null==(a=d[n.Yr.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=d[n.Yr.planExpiry])?void 0:e.toDate(),quickTranslationAdvantageExpiry:null==(o=d.quickTranslationAdvantageExpiry)?void 0:o.toDate(),quickVideoAdvantageExpiry:null==(r=d.quickVideoAdvantageExpiry)?void 0:r.toDate(),lastTranslationDate:null==(i=d.lastTranslationDate)?void 0:i.toDate(),lastVideoDate:null==(l=d.lastVideoDate)?void 0:l.toDate(),lastCopyPasteReduction:null==(s=d.lastCopyPasteReduction)?void 0:s.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===t}}catch(t){throw console.error("Error getting users:",t),t}}async function d(t){try{if(!t||0===t.trim().length)return[];let a=t.toLowerCase().trim(),e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.Yr.joinedDate,"desc"));return(await (0,o.getDocs)(e)).docs.map(t=>{var a,e,o,r,i,l,s;let d=t.data();return{id:t.id,...d,joinedDate:null==(a=d[n.Yr.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=d[n.Yr.planExpiry])?void 0:e.toDate(),quickTranslationAdvantageExpiry:null==(o=d.quickTranslationAdvantageExpiry)?void 0:o.toDate(),quickVideoAdvantageExpiry:null==(r=d.quickVideoAdvantageExpiry)?void 0:r.toDate(),lastTranslationDate:null==(i=d.lastTranslationDate)?void 0:i.toDate(),lastVideoDate:null==(l=d.lastVideoDate)?void 0:l.toDate(),lastCopyPasteReduction:null==(s=d.lastCopyPasteReduction)?void 0:s.toDate()}}).filter(t=>{let e=String(t[n.Yr.name]||"").toLowerCase(),o=String(t[n.Yr.email]||"").toLowerCase(),r=String(t[n.Yr.mobile]||"").toLowerCase(),i=String(t[n.Yr.referralCode]||"").toLowerCase();return e.includes(a)||o.includes(a)||r.includes(a)||i.includes(a)})}catch(t){throw console.error("Error searching users:",t),t}}async function c(){try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users),(0,o.My)(n.Yr.joinedDate,"desc"));return(await (0,o.getDocs)(t)).docs.map(t=>{var a,e,o,r,i,l,s;let d=t.data();return{id:t.id,...d,joinedDate:null==(a=d[n.Yr.joinedDate])?void 0:a.toDate(),planExpiry:null==(e=d[n.Yr.planExpiry])?void 0:e.toDate(),quickTranslationAdvantageExpiry:null==(o=d.quickTranslationAdvantageExpiry)?void 0:o.toDate(),quickVideoAdvantageExpiry:null==(r=d.quickVideoAdvantageExpiry)?void 0:r.toDate(),lastTranslationDate:null==(i=d.lastTranslationDate)?void 0:i.toDate(),lastVideoDate:null==(l=d.lastVideoDate)?void 0:l.toDate(),lastCopyPasteReduction:null==(s=d.lastCopyPasteReduction)?void 0:s.toDate()}})}catch(t){throw console.error("Error getting all users:",t),t}}async function u(){try{let t=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.users));return(await (0,o.getDocs)(t)).size}catch(t){throw console.error("Error getting total user count:",t),t}}async function w(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o.My)("date","desc"),(0,o.AB)(t));a&&(e=(0,o.P)((0,o.collection)(r.db,n.COLLECTIONS.withdrawals),(0,o.My)("date","desc"),(0,o.HM)(a),(0,o.AB)(t)));let i=await (0,o.getDocs)(e);return{withdrawals:i.docs.map(t=>{var a;return{id:t.id,...t.data(),date:null==(a=t.data().date)?void 0:a.toDate()}}),lastDoc:i.docs[i.docs.length-1]||null,hasMore:i.docs.length===t}}catch(t){throw console.error("Error getting withdrawals:",t),t}}async function D(t,a){try{await (0,o.mZ)((0,o.H9)(r.db,n.COLLECTIONS.users,t),a),i.delete("dashboard-stats")}catch(t){throw console.error("Error updating user:",t),t}}async function h(t){try{await (0,o.kd)((0,o.H9)(r.db,n.COLLECTIONS.users,t)),i.delete("dashboard-stats")}catch(t){throw console.error("Error deleting user:",t),t}}async function p(t,a,l){try{let s=await (0,o.x7)((0,o.H9)(r.db,n.COLLECTIONS.withdrawals,t));if(!s.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=s.data(),w={status:a,updatedAt:o.Dc.now()};if(l&&(w.adminNotes=l),await (0,o.mZ)((0,o.H9)(r.db,n.COLLECTIONS.withdrawals,t),w),"approved"===a&&"approved"!==u){let{addTransaction:t}=await Promise.resolve().then(e.bind(e,3592));await t(d,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===a&&"rejected"!==u){let{updateWalletBalance:t,addTransaction:a}=await Promise.resolve().then(e.bind(e,3592));await t(d,c),await a(d,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}i.delete("dashboard-stats")}catch(t){throw console.error("Error updating withdrawal status:",t),t}}}}]);