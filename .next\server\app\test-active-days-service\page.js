(()=>{var e={};e.id=7410,e.ids=[7410],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},17537:(e,s,t)=>{Promise.resolve().then(t.bind(t,47538))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},45252:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(60687),i=t(43210),o=t(87979),l=t(51705);function n(){let{user:e,loading:s}=(0,o.Nu)(),[t,n]=(0,i.useState)(""),[a,c]=(0,i.useState)(null),[d,u]=(0,i.useState)(!1),p=async()=>{if(!t.trim())return void alert("Please enter a user ID");u(!0),c(null);try{console.log(`🧪 Testing active days service for user: ${t}`),console.log("1. Testing getUserActiveDays...");let e=await (0,l.nd)(t.trim());console.log("✅ getUserActiveDays result:",e),console.log("2. Testing calculateActiveDays...");let s=await (0,l.i7)(t.trim());console.log("✅ calculateActiveDays result:",s),console.log("3. Testing updateUserActiveDays...");let r=await (0,l.updateUserActiveDays)(t.trim());console.log("✅ updateUserActiveDays result:",r),c({success:!0,currentActiveDays:e,calculation:s,updatedActiveDays:r,message:"Single user test completed successfully"})}catch(e){console.error("❌ Error in single user test:",e),c({success:!1,error:e.message,stack:e.stack,message:"Single user test failed"})}finally{u(!1)}},x=async()=>{u(!0),c(null);try{console.log("\uD83E\uDDEA Testing processAllUsersActiveDays...");let e=await (0,l.mH)();console.log("✅ processAllUsersActiveDays result:",e),c({success:!0,allUsersResult:e,message:"All users test completed successfully"})}catch(e){console.error("❌ Error in all users test:",e),c({success:!1,error:e.message,stack:e.stack,message:"All users test failed"})}finally{u(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,r.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Active Days Service"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white mb-2",children:"User ID (for single user test):"}),(0,r.jsx)("input",{type:"text",value:t,onChange:e=>n(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:p,disabled:d,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Single User"}),(0,r.jsx)("button",{onClick:x,disabled:d,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test All Users"})]}),a&&(0,r.jsxs)("div",{className:`p-4 rounded-lg ${a.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,r.jsx)("h3",{className:`font-bold ${a.success?"text-green-400":"text-red-400"}`,children:a.success?"Success!":"Failed"}),(0,r.jsx)("p",{className:"text-white mt-2",children:a.message}),void 0!==a.currentActiveDays&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Current Active Days:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:a.currentActiveDays})]}),a.calculation&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Calculation Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(a.calculation,null,2)})]}),void 0!==a.updatedActiveDays&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"Updated Active Days:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:a.updatedActiveDays})]}),a.allUsersResult&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,r.jsx)("h4",{className:"text-white font-semibold",children:"All Users Result:"}),(0,r.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(a.allUsersResult,null,2)})]}),a.error&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,r.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mt-1",children:a.error}),a.stack&&(0,r.jsx)("pre",{className:"text-white/60 text-xs mt-2 overflow-auto",children:a.stack})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,r.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Enter a user ID to test single user functions"}),(0,r.jsx)("li",{children:'• Click "Test Single User" to test calculation and update functions'}),(0,r.jsx)("li",{children:'• Click "Test All Users" to test the batch processing function'}),(0,r.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}},47538:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-active-days-service\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-active-days-service\\page.tsx","default")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53985:(e,s,t)=>{Promise.resolve().then(t.bind(t,45252))},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80868:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=t(65239),i=t(48088),o=t(88170),l=t.n(o),n=t(30893),a={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>n[e]);t.d(s,a);let c={children:["",{children:["test-active-days-service",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,47538)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-active-days-service\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-active-days-service\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-active-days-service/page",pathname:"/test-active-days-service",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[2579,6803],()=>t(80868));module.exports=r})();