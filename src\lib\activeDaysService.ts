import { db } from './firebase'
import { 
  doc, 
  updateDoc, 
  getDoc, 
  collection, 
  getDocs, 
  query, 
  where,
  Timestamp 
} from 'firebase/firestore'

// Field names and collections
const FIELD_NAMES = {
  activeDays: 'activeDays',
  lastActiveDayUpdate: 'lastActiveDayUpdate',
  joinedDate: 'joinedDate',
  plan: 'plan',
  name: 'name',
  email: 'email'
}

const COLLECTIONS = {
  users: 'users'
}

// Centralized Active Days Calculation Service
export class ActiveDaysService {
  
  /**
   * Calculate active days for a user based on their registration and leave history
   */
  static async calculateActiveDays(userId: string): Promise<{
    activeDays: number
    shouldUpdate: boolean
    isNewDay: boolean
  }> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
      if (!userDoc.exists()) {
        console.error(`User ${userId} not found`)
        return { activeDays: 0, shouldUpdate: false, isNewDay: false }
      }

      const userData = userDoc.data()
      const joinedDate = userData.joinedDate?.toDate() || new Date()
      const lastUpdate = userData.lastActiveDayUpdate?.toDate()
      const currentActiveDays = userData.activeDays || 0
      const plan = userData.plan || 'Trial'
      
      const today = new Date()
      const todayString = today.toDateString()
      const lastUpdateString = lastUpdate ? lastUpdate.toDateString() : null

      console.log(`📅 Calculating active days for user ${userId}:`)
      console.log(`   - Joined: ${joinedDate.toDateString()}`)
      console.log(`   - Current active days: ${currentActiveDays}`)
      console.log(`   - Last update: ${lastUpdateString || 'Never'}`)
      console.log(`   - Today: ${todayString}`)
      console.log(`   - Plan: ${plan}`)
      console.log(`   - Is new day: ${lastUpdateString !== todayString}`)

      // Check if it's a new day
      const isNewDay = lastUpdateString !== todayString

      if (!isNewDay) {
        console.log(`✅ Already updated today for user ${userId}`)
        return { activeDays: currentActiveDays, shouldUpdate: false, isNewDay: false }
      }

      // Skip active days increment for admins
      if (plan === 'Admin') {
        console.log(`⏭️ Skipping active days increment for admin user ${userId}`)
        await ActiveDaysService.updateLastActiveDayUpdate(userId)
        return { activeDays: currentActiveDays, shouldUpdate: false, isNewDay: true }
      }

      // Check if user is on leave today
      const isOnLeave = await ActiveDaysService.isUserOnLeaveToday(userId)
      if (isOnLeave) {
        console.log(`🏖️ User ${userId} is on leave today, not incrementing active days`)
        await ActiveDaysService.updateLastActiveDayUpdate(userId)
        return { activeDays: currentActiveDays, shouldUpdate: false, isNewDay: true }
      }

      // Calculate new active days
      let newActiveDays: number

      if (plan === 'Trial') {
        // For trial users: calculate based on days since joining
        const daysSinceJoining = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24))
        newActiveDays = daysSinceJoining + 1 // Day 1 starts on registration day
      } else {
        // For paid plans: increment from current active days
        newActiveDays = currentActiveDays + 1
      }

      console.log(`📈 New active days calculated: ${currentActiveDays} → ${newActiveDays}`)

      return { 
        activeDays: newActiveDays, 
        shouldUpdate: newActiveDays !== currentActiveDays, 
        isNewDay: true 
      }

    } catch (error) {
      console.error(`Error calculating active days for user ${userId}:`, error)
      return { activeDays: 0, shouldUpdate: false, isNewDay: false }
    }
  }

  /**
   * Update user's active days
   */
  static async updateUserActiveDays(userId: string): Promise<number> {
    try {
      const calculation = await ActiveDaysService.calculateActiveDays(userId)

      if (calculation.shouldUpdate) {
        const userRef = doc(db, COLLECTIONS.users, userId)
        await updateDoc(userRef, {
          [FIELD_NAMES.activeDays]: calculation.activeDays,
          [FIELD_NAMES.lastActiveDayUpdate]: Timestamp.now()
        })

        console.log(`✅ Updated active days for user ${userId}: ${calculation.activeDays}`)
      } else if (calculation.isNewDay) {
        // Update timestamp even if active days didn't change
        await ActiveDaysService.updateLastActiveDayUpdate(userId)
      }

      return calculation.activeDays
    } catch (error) {
      console.error(`Error updating active days for user ${userId}:`, error)
      throw error
    }
  }

  /**
   * Update only the last active day update timestamp
   */
  private static async updateLastActiveDayUpdate(userId: string): Promise<void> {
    try {
      const userRef = doc(db, COLLECTIONS.users, userId)
      await updateDoc(userRef, {
        [FIELD_NAMES.lastActiveDayUpdate]: Timestamp.now()
      })
    } catch (error) {
      console.error(`Error updating last active day timestamp for user ${userId}:`, error)
    }
  }

  /**
   * Check if user is on leave today
   */
  private static async isUserOnLeaveToday(userId: string): Promise<boolean> {
    try {
      const { isUserOnLeave } = await import('./leaveService')
      return await isUserOnLeave(userId, new Date())
    } catch (error) {
      console.error(`Error checking leave status for user ${userId}:`, error)
      return false // Default to not on leave to avoid blocking work
    }
  }

  /**
   * Process all users' active days (daily scheduler)
   */
  static async processAllUsersActiveDays(): Promise<{
    processed: number
    updated: number
    errors: number
  }> {
    try {
      console.log('🔄 Starting daily active days processing for all users...')
      
      const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
      let processed = 0
      let updated = 0
      let errors = 0

      for (const userDoc of usersSnapshot.docs) {
        try {
          processed++
          const calculation = await ActiveDaysService.calculateActiveDays(userDoc.id)

          if (calculation.shouldUpdate || calculation.isNewDay) {
            await ActiveDaysService.updateUserActiveDays(userDoc.id)
            if (calculation.shouldUpdate) updated++
          }
        } catch (error) {
          errors++
          console.error(`Error processing active days for user ${userDoc.id}:`, error)
        }
      }

      console.log(`✅ Daily active days processing complete:`)
      console.log(`   - Processed: ${processed} users`)
      console.log(`   - Updated: ${updated} users`)
      console.log(`   - Errors: ${errors} users`)

      return { processed, updated, errors }
    } catch (error) {
      console.error('Error in daily active days processing:', error)
      throw error
    }
  }

  /**
   * Get active days for a user (read-only)
   */
  static async getUserActiveDays(userId: string): Promise<number> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
      if (!userDoc.exists()) {
        return 0
      }
      
      const userData = userDoc.data()
      return userData.activeDays || 0
    } catch (error) {
      console.error(`Error getting active days for user ${userId}:`, error)
      return 0
    }
  }

  /**
   * Initialize active days for new user (called during registration)
   */
  static async initializeActiveDaysForNewUser(userId: string): Promise<void> {
    try {
      const userRef = doc(db, COLLECTIONS.users, userId)
      await updateDoc(userRef, {
        [FIELD_NAMES.activeDays]: 1, // Start with day 1
        [FIELD_NAMES.lastActiveDayUpdate]: Timestamp.now()
      })
      
      console.log(`✅ Initialized active days for new user ${userId}: Day 1`)
    } catch (error) {
      console.error(`Error initializing active days for user ${userId}:`, error)
      throw error
    }
  }
}

// Export convenience functions
export const calculateActiveDays = ActiveDaysService.calculateActiveDays
export const updateUserActiveDays = ActiveDaysService.updateUserActiveDays
export const processAllUsersActiveDays = ActiveDaysService.processAllUsersActiveDays
export const getUserActiveDays = ActiveDaysService.getUserActiveDays
export const initializeActiveDaysForNewUser = ActiveDaysService.initializeActiveDaysForNewUser
