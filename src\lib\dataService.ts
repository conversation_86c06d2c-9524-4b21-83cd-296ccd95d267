import {
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  addDoc,
  Timestamp,
  increment,
  getCountFromServer
} from 'firebase/firestore'
import { db } from './firebase'

// Field names for Firestore collections
export const FIELD_NAMES = {
  // User fields
  name: 'name',
  email: 'email',
  mobile: 'mobile',
  referralCode: 'referralCode',
  referredBy: 'referredBy',
  referralBonusCredited: 'referralBonusCredited', // Track if referral bonus has been credited
  plan: 'plan',
  planExpiry: 'planExpiry',
  activeDays: 'activeDays',
  lastActiveDayUpdate: 'lastActiveDayUpdate', // Track when active days were last updated
  joinedDate: 'joinedDate',

  // Wallet fields
  wallet: 'wallet', // Single wallet instead of multiple

  // Bank details fields
  bankAccountHolderName: 'bankAccountHolderName',
  bankAccountNumber: 'bankAccountNumber',
  bankIfscCode: 'bankIfscCode',
  bankName: 'bankName',
  bankDetailsUpdated: 'bankDetailsUpdated',

  // Translation fields
  totalTranslations: 'totalTranslations',
  todayTranslations: 'todayTranslations',
  lastTranslationDate: 'lastTranslationDate',
  translationDuration: 'translationDuration', // Duration in seconds for translation tasks

  // Quick Translation Advantage fields
  quickTranslationAdvantage: 'quickTranslationAdvantage', // Boolean: whether user has quick translation advantage
  quickTranslationAdvantageExpiry: 'quickTranslationAdvantageExpiry', // Date: when the advantage expires
  quickTranslationAdvantageDays: 'quickTranslationAdvantageDays', // Number: total days granted
  quickTranslationAdvantageSeconds: 'quickTranslationAdvantageSeconds', // Number: translation duration in seconds during advantage
  quickTranslationAdvantageGrantedBy: 'quickTranslationAdvantageGrantedBy', // String: admin who granted it
  quickTranslationAdvantageGrantedAt: 'quickTranslationAdvantageGrantedAt', // Date: when it was granted

  // Transaction fields
  type: 'type',
  amount: 'amount',
  date: 'date',
  status: 'status',
  description: 'description',
  userId: 'userId'
}

// Collection names
export const COLLECTIONS = {
  users: 'users',
  transactions: 'transactions',
  withdrawals: 'withdrawals',
  plans: 'plans',
  settings: 'settings',
  notifications: 'notifications',
  adminLeaves: 'adminLeaves',
  userLeaves: 'userLeaves'
}

// Get user data
export async function getUserData(userId: string) {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getUserData:', userId)
      return null
    }

    const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
    if (userDoc.exists()) {
      const data = userDoc.data()

      // Ensure all values are properly typed
      const result = {
        name: String(data[FIELD_NAMES.name] || ''),
        email: String(data[FIELD_NAMES.email] || ''),
        mobile: String(data[FIELD_NAMES.mobile] || ''),
        referralCode: String(data[FIELD_NAMES.referralCode] || ''),
        referredBy: String(data[FIELD_NAMES.referredBy] || ''),
        referralBonusCredited: Boolean(data[FIELD_NAMES.referralBonusCredited] || false),
        plan: String(data[FIELD_NAMES.plan] || 'Trial'),
        planExpiry: data[FIELD_NAMES.planExpiry]?.toDate() || null,
        activeDays: Number(data[FIELD_NAMES.activeDays] || 0),
        lastActiveDayUpdate: data[FIELD_NAMES.lastActiveDayUpdate]?.toDate() || null,
        joinedDate: data[FIELD_NAMES.joinedDate]?.toDate() || new Date(),
        translationDuration: Number(data[FIELD_NAMES.translationDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)), // Default 30 seconds for trial, 5 minutes for others

        // Quick Translation Advantage fields
        quickTranslationAdvantage: Boolean(data[FIELD_NAMES.quickTranslationAdvantage] || false),
        quickTranslationAdvantageExpiry: data[FIELD_NAMES.quickTranslationAdvantageExpiry]?.toDate() || null,
        quickTranslationAdvantageDays: Number(data[FIELD_NAMES.quickTranslationAdvantageDays] || 0),
        quickTranslationAdvantageSeconds: Number(data[FIELD_NAMES.quickTranslationAdvantageSeconds] || 30),
        quickTranslationAdvantageGrantedBy: String(data[FIELD_NAMES.quickTranslationAdvantageGrantedBy] || ''),
        quickTranslationAdvantageGrantedAt: data[FIELD_NAMES.quickTranslationAdvantageGrantedAt]?.toDate() || null
      }

      console.log('getUserData result:', result)
      return result
    }
    return null
  } catch (error) {
    console.error('Error getting user data:', error)
    return null // Return null instead of throwing to prevent crashes
  }
}

// Get wallet data
export async function getWalletData(userId: string) {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getWalletData:', userId)
      return { wallet: 0 }
    }

    const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
    if (userDoc.exists()) {
      const data = userDoc.data()
      const result = {
        wallet: Number(data[FIELD_NAMES.wallet] || 0)
      }

      console.log('getWalletData result:', result)
      return result
    }
    return { wallet: 0 }
  } catch (error) {
    console.error('Error getting wallet data:', error)
    return { wallet: 0 } // Return default instead of throwing
  }
}

// Get translation count data
export async function getVideoCountData(userId: string) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)
    const userDoc = await getDoc(userRef)
    if (userDoc.exists()) {
      const data = userDoc.data()
      const totalTranslations = data[FIELD_NAMES.totalTranslations] || 0
      let todayTranslations = data[FIELD_NAMES.todayTranslations] || 0
      const lastTranslationDate = data[FIELD_NAMES.lastTranslationDate]?.toDate()

      // Check if it's a new day
      const today = new Date()
      const isNewDay = !lastTranslationDate ||
        lastTranslationDate.toDateString() !== today.toDateString()

      // If it's a new day, reset todayTranslations and update active days
      if (isNewDay && todayTranslations > 0) {
        console.log(`🔄 Resetting daily translation count for user ${userId} (was ${todayTranslations})`)
        await updateDoc(userRef, {
          [FIELD_NAMES.todayTranslations]: 0
        })
        todayTranslations = 0

        // Note: Active days are now handled by centralized scheduler
        // Individual calls removed to prevent duplicate increments
        console.log('📅 Active days will be updated by centralized scheduler')
      }

      return {
        totalTranslations: totalTranslations,
        todayTranslations: todayTranslations,
        remainingTranslations: Math.max(0, 50 - todayTranslations)
      }
    }
    return { totalTranslations: 0, todayTranslations: 0, remainingTranslations: 50 }
  } catch (error) {
    console.error('Error getting video count data:', error)
    throw error
  }
}

// Update user data
export async function updateUserData(userId: string, data: any) {
  try {
    await updateDoc(doc(db, COLLECTIONS.users, userId), data)
  } catch (error) {
    console.error('Error updating user data:', error)
    throw error
  }
}

// Add transaction
export async function addTransaction(userId: string, transactionData: {
  type: string
  amount: number
  description: string
  status?: string
}) {
  try {
    const transaction = {
      [FIELD_NAMES.userId]: userId,
      [FIELD_NAMES.type]: transactionData.type,
      [FIELD_NAMES.amount]: transactionData.amount,
      [FIELD_NAMES.description]: transactionData.description,
      [FIELD_NAMES.status]: transactionData.status || 'completed',
      [FIELD_NAMES.date]: Timestamp.now()
    }
    
    await addDoc(collection(db, COLLECTIONS.transactions), transaction)
  } catch (error) {
    console.error('Error adding transaction:', error)
    throw error
  }
}

// Get transactions
export async function getTransactions(userId: string, limitCount = 10) {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getTransactions:', userId)
      return []
    }

    // Temporary fix: Use only where clause without orderBy to avoid index requirement
    // TODO: Create composite index in Firebase console for better performance
    const q = query(
      collection(db, COLLECTIONS.transactions),
      where(FIELD_NAMES.userId, '==', userId),
      limit(limitCount)
    )

    const querySnapshot = await getDocs(q)
    const transactions = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data()[FIELD_NAMES.date]?.toDate()
    }))

    // Sort in memory since we can't use orderBy without index
    transactions.sort((a, b) => {
      const dateA = a.date || new Date(0)
      const dateB = b.date || new Date(0)
      return dateB.getTime() - dateA.getTime() // Descending order
    })

    return transactions
  } catch (error) {
    console.error('Error getting transactions:', error)
    return [] // Return empty array instead of throwing to prevent crashes
  }
}

// Get referrals
export async function getReferrals(referralCode: string) {
  try {
    const q = query(
      collection(db, COLLECTIONS.users),
      where(FIELD_NAMES.referredBy, '==', referralCode)
    )
    
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate()
    }))
  } catch (error) {
    console.error('Error getting referrals:', error)
    throw error
  }
}

// Update translation count
export async function updateVideoCount(userId: string) {
  try {
    const today = new Date()
    const userRef = doc(db, COLLECTIONS.users, userId)

    // Get current data to check if we need to reset daily count
    const userDoc = await getDoc(userRef)
    if (userDoc.exists()) {
      const data = userDoc.data()
      const lastTranslationDate = data[FIELD_NAMES.lastTranslationDate]?.toDate()
      const currentTodayTranslations = data[FIELD_NAMES.todayTranslations] || 0

      // Check if it's a new day
      const isNewDay = !lastTranslationDate ||
        lastTranslationDate.toDateString() !== today.toDateString()

      if (isNewDay && currentTodayTranslations > 0) {
        // Reset today's count and then increment
        console.log(`🔄 Resetting and updating daily translation count for user ${userId}`)
        await updateDoc(userRef, {
          [FIELD_NAMES.totalTranslations]: increment(1),
          [FIELD_NAMES.todayTranslations]: 1, // Reset to 1 (this translation)
          [FIELD_NAMES.lastTranslationDate]: Timestamp.fromDate(today)
        })
      } else {
        // Normal increment
        await updateDoc(userRef, {
          [FIELD_NAMES.totalTranslations]: increment(1),
          [FIELD_NAMES.todayTranslations]: increment(1),
          [FIELD_NAMES.lastTranslationDate]: Timestamp.fromDate(today)
        })
      }
    } else {
      // User doesn't exist, create with initial values
      await updateDoc(userRef, {
        [FIELD_NAMES.totalTranslations]: increment(1),
        [FIELD_NAMES.todayTranslations]: increment(1),
        [FIELD_NAMES.lastTranslationDate]: Timestamp.fromDate(today)
      })
    }
  } catch (error) {
    console.error('Error updating translation count:', error)
    throw error
  }
}

// Reset daily translation count for a user (admin function)
export async function resetDailyVideoCount(userId: string) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.todayTranslations]: 0
    })
    console.log(`✅ Reset daily translation count for user ${userId}`)
  } catch (error) {
    console.error('Error resetting daily translation count:', error)
    throw error
  }
}

// Update user's active days using centralized service
export async function updateUserActiveDays(userId: string) {
  try {
    const { updateUserActiveDays: centralizedUpdate } = await import('./activeDaysService')
    return await centralizedUpdate(userId)
  } catch (error) {
    console.error('Error updating user active days:', error)
    throw error
  }
}

// Fix all users' active days (admin function)
export async function fixAllUsersActiveDays() {
  try {
    console.log('🔧 Starting to fix all users active days...')
    const usersSnapshot = await getDocs(collection(db, COLLECTIONS.users))
    let fixedCount = 0
    let errorCount = 0

    for (const userDoc of usersSnapshot.docs) {
      try {
        await updateUserActiveDays(userDoc.id)
        fixedCount++
      } catch (error) {
        console.error(`Error fixing active days for user ${userDoc.id}:`, error)
        errorCount++
      }
    }

    console.log(`✅ Fixed active days for ${fixedCount} users, ${errorCount} errors`)
    return { fixedCount, errorCount }
  } catch (error) {
    console.error('Error fixing all users active days:', error)
    throw error
  }
}

// Update wallet balance
export async function updateWalletBalance(userId: string, amount: number) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.wallet]: increment(amount)
    })
  } catch (error) {
    console.error('Error updating wallet balance:', error)
    throw error
  }
}

// Bank details interface
export interface BankDetails {
  accountHolderName: string
  accountNumber: string
  ifscCode: string
  bankName: string
}

// Save bank details
export async function saveBankDetails(userId: string, bankDetails: BankDetails) {
  try {
    if (!userId || typeof userId !== 'string') {
      throw new Error('Invalid userId provided')
    }

    // Validate bank details
    validateBankDetails(bankDetails)

    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),
      [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),
      [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),
      [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),
      [FIELD_NAMES.bankDetailsUpdated]: Timestamp.now()
    })

    console.log('Bank details saved successfully for user:', userId)
  } catch (error) {
    console.error('Error saving bank details:', error)
    throw error
  }
}

// Get bank details
export async function getBankDetails(userId: string): Promise<BankDetails | null> {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getBankDetails:', userId)
      return null
    }

    const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
    if (userDoc.exists()) {
      const data = userDoc.data()

      // Check if bank details exist
      if (data[FIELD_NAMES.bankAccountNumber]) {
        const result: BankDetails = {
          accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),
          accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),
          ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),
          bankName: String(data[FIELD_NAMES.bankName] || '')
        }

        console.log('getBankDetails result found')
        return result
      }
    }

    console.log('No bank details found for user')
    return null
  } catch (error) {
    console.error('Error getting bank details:', error)
    return null
  }
}

// Get plan-based earning amount (per batch of 50 translations)
export function getPlanEarning(plan: string): number {
  const planEarnings: { [key: string]: number } = {
    'Trial': 25,     // Fixed: Trial should be ₹25 per 50 translations
    'Junior': 150,   // Updated to match new plan structure
    'Senior': 250,   // Updated to match new plan structure
    'Expert': 400,   // Updated to match new plan structure
    'Starter': 25,   // Legacy plan mapping
    'Basic': 75,     // Legacy plan mapping
    'Premium': 150,  // Legacy plan mapping
    'Gold': 200,     // Legacy plan mapping
    'Platinum': 250, // Legacy plan mapping
    'Diamond': 400   // Legacy plan mapping
  }

  return planEarnings[plan] || 25 // Default to trial earning (per batch of 50 translations)
}

// Check copy-paste permission using centralized service
export async function checkCopyPastePermission(userId: string): Promise<{
  hasPermission: boolean
  daysRemaining: number
}> {
  try {
    const { checkCopyPastePermission: centralizedCheck } = await import('./copyPasteService')
    const result = await centralizedCheck(userId)
    return {
      hasPermission: result.hasPermission,
      daysRemaining: result.daysRemaining
    }
  } catch (error) {
    console.error('Error checking copy-paste permission:', error)
    return { hasPermission: false, daysRemaining: 0 }
  }
}

// Get plan-based translation duration (in seconds)
export function getPlanVideoDuration(plan: string): number {
  const planDurations: { [key: string]: number } = {
    'Trial': 30,      // 30 seconds
    'Starter': 300,   // 5 minutes (Rs 499 plan)
    'Basic': 300,     // 5 minutes (Rs 1499 plan)
    'Premium': 300,   // 5 minutes (Rs 2999 plan)
    'Gold': 180,      // 3 minutes (Rs 3999 plan)
    'Platinum': 120,  // 2 minutes (Rs 5999 plan)
    'Diamond': 60     // 1 minute (Rs 9999 plan)
  }

  return planDurations[plan] || 30 // Default to trial duration (30 seconds)
}

// Get plan validity duration in days
export function getPlanValidityDays(plan: string): number {
  const planValidityDays: { [key: string]: number } = {
    'Trial': 2,       // 2 days
    'Junior': 30,     // 30 days (Rs 2999 plan)
    'Senior': 30,     // 30 days (Rs 5999 plan)
    'Expert': 30,     // 30 days (Rs 9999 plan)
    'Starter': 30,    // 30 days (Rs 499 plan) - Legacy
    'Basic': 30,      // 30 days (Rs 1499 plan) - Legacy
    'Premium': 30,    // 30 days (Rs 2999 plan) - Legacy
    'Gold': 30,       // 30 days (Rs 3999 plan) - Legacy
    'Platinum': 30,   // 30 days (Rs 5999 plan) - Legacy
    'Diamond': 30,    // 30 days (Rs 9999 plan) - Legacy
    '499': 30,        // Legacy plan mapping
    '1499': 30,       // Legacy plan mapping
    '2999': 30,       // Legacy plan mapping
    '3999': 30,       // Legacy plan mapping
    '5999': 30,       // Legacy plan mapping
    '9999': 30        // Legacy plan mapping
  }

  return planValidityDays[plan] || 2 // Default to trial duration (2 days)
}

// Check if user's plan is expired based on active days and plan validity
export async function isUserPlanExpired(userId: string): Promise<{ expired: boolean; reason?: string; daysLeft?: number; activeDays?: number }> {
  try {
    const userData = await getUserData(userId)
    if (!userData) {
      return { expired: true, reason: 'User data not found' }
    }

    // If user is on Trial plan, check based on joined date
    if (userData.plan === 'Trial') {
      const joinedDate = userData.joinedDate || new Date()
      const today = new Date()

      // Calculate days since joining (0 on join day, 1 after first day, etc.)
      const daysSinceJoining = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24))

      // Active days start from 1 on join day
      const activeDays = daysSinceJoining + 1

      // Trial lasts 2 days, so days left = 2 - days since joining
      const trialDaysLeft = Math.max(0, 2 - daysSinceJoining)

      console.log('📅 Trial plan calculation:', {
        joinedDate: joinedDate.toDateString(),
        today: today.toDateString(),
        daysSinceJoining,
        activeDays,
        trialDaysLeft
      })

      return {
        expired: trialDaysLeft <= 0,
        reason: trialDaysLeft <= 0 ? 'Trial period expired' : undefined,
        daysLeft: trialDaysLeft,
        activeDays: activeDays
      }
    }

    // For paid plans, check if planExpiry is set
    if (userData.planExpiry) {
      const today = new Date()
      const expired = today > userData.planExpiry
      const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

      return {
        expired,
        reason: expired ? 'Plan subscription expired' : undefined,
        daysLeft,
        activeDays: userData.activeDays || 0
      }
    }

    // If planExpiry is not set, calculate based on active days and plan validity
    const planValidityDays = getPlanValidityDays(userData.plan)
    const currentActiveDays = userData.activeDays || 0
    const daysLeft = Math.max(0, planValidityDays - currentActiveDays)
    const expired = daysLeft <= 0

    return {
      expired,
      reason: expired ? `Plan validity period (${planValidityDays} days) exceeded based on active days` : undefined,
      daysLeft,
      activeDays: currentActiveDays
    }
  } catch (error) {
    console.error('Error checking plan expiry:', error)
    return { expired: true, reason: 'Error checking plan status' }
  }
}

// Update user's plan expiry when admin changes plan
export async function updateUserPlanExpiry(userId: string, newPlan: string, customExpiryDate?: Date) {
  try {
    const userRef = doc(db, COLLECTIONS.users, userId)

    if (newPlan === 'Trial') {
      // Trial plan doesn't have expiry, it's based on joined date
      await updateDoc(userRef, {
        [FIELD_NAMES.planExpiry]: null
      })
    } else {
      // Set expiry date for paid plans
      let expiryDate: Date

      if (customExpiryDate) {
        expiryDate = customExpiryDate
      } else {
        // Calculate expiry based on plan validity
        const validityDays = getPlanValidityDays(newPlan)
        const today = new Date()
        expiryDate = new Date(today.getTime() + (validityDays * 24 * 60 * 60 * 1000))
      }

      await updateDoc(userRef, {
        [FIELD_NAMES.planExpiry]: Timestamp.fromDate(expiryDate)
      })

      console.log(`Updated plan expiry for user ${userId} to ${expiryDate.toDateString()}`)
    }
  } catch (error) {
    console.error('Error updating plan expiry:', error)
    throw error
  }
}

// Get referral bonus based on plan
export function getReferralBonus(plan: string): number {
  const referralBonuses: { [key: string]: number } = {
    'Trial': 0,     // Trial plan -> No bonus
    'Junior': 300,  // Junior plan -> Rs 300 bonus + 50 translations
    'Senior': 700,  // Senior plan -> Rs 700 bonus + 50 translations
    'Expert': 1200, // Expert plan -> Rs 1200 bonus + 50 translations
    '499': 50,      // Legacy Rs 499 plan -> Rs 50 bonus
    '1499': 150,    // Legacy Rs 1499 plan -> Rs 150 bonus
    '2999': 300,    // Legacy Rs 2999 plan -> Rs 300 bonus
    '3999': 400,    // Legacy Rs 3999 plan -> Rs 400 bonus
    '5999': 700,    // Legacy Rs 5999 plan -> Rs 700 bonus
    '9999': 1200,   // Legacy Rs 9999 plan -> Rs 1200 bonus
    'Starter': 50,  // Legacy plan mapping
    'Basic': 150,   // Legacy plan mapping
    'Premium': 300, // Legacy plan mapping
    'Gold': 400,    // Legacy plan mapping
    'Platinum': 700,// Legacy plan mapping
    'Diamond': 1200 // Legacy plan mapping
  }

  return referralBonuses[plan] || 0
}

// Process referral bonus when admin upgrades user from Trial to paid plan
export async function processReferralBonus(userId: string, oldPlan: string, newPlan: string) {
  try {
    console.log(`🎯 REFERRAL BONUS DEBUG: Starting process for user ${userId}`)
    console.log(`🎯 Plan change: ${oldPlan} → ${newPlan}`)

    // Only process bonus when upgrading FROM Trial TO a paid plan
    if (oldPlan !== 'Trial' || newPlan === 'Trial') {
      console.log('❌ Referral bonus only applies when upgrading from Trial to paid plan')
      console.log(`❌ Current conditions: oldPlan=${oldPlan}, newPlan=${newPlan}`)
      return
    }

    console.log(`✅ Processing referral bonus for user ${userId} upgrading from ${oldPlan} to ${newPlan}`)

    // Get the user's data to find their referral info
    const userDoc = await getDoc(doc(db, COLLECTIONS.users, userId))
    if (!userDoc.exists()) {
      console.log('❌ User not found in database')
      return
    }

    const userData = userDoc.data()
    const referredBy = userData[FIELD_NAMES.referredBy]
    const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited]

    console.log(`🔍 User referral data:`)
    console.log(`   - Referred by: ${referredBy || 'None'}`)
    console.log(`   - Already credited: ${alreadyCredited}`)
    console.log(`   - User name: ${userData[FIELD_NAMES.name]}`)

    if (!referredBy) {
      console.log('❌ User was not referred by anyone, skipping bonus processing')
      return
    }

    if (alreadyCredited) {
      console.log('❌ Referral bonus already credited for this user, skipping')
      return
    }

    console.log(`🔍 Finding referrer with code: ${referredBy}`)

    // Find the referrer by referral code
    const q = query(
      collection(db, COLLECTIONS.users),
      where(FIELD_NAMES.referralCode, '==', referredBy),
      limit(1)
    )

    const querySnapshot = await getDocs(q)

    if (querySnapshot.empty) {
      console.log(`❌ Referral code not found: ${referredBy}`)
      console.log('❌ No referrer found with this code in database')
      return
    }

    const referrerDoc = querySnapshot.docs[0]
    const referrerId = referrerDoc.id
    const referrerData = referrerDoc.data()
    const bonusAmount = getReferralBonus(newPlan)

    console.log(`✅ Found referrer:`)
    console.log(`   - Referrer ID: ${referrerId}`)
    console.log(`   - Referrer name: ${referrerData[FIELD_NAMES.name]}`)
    console.log(`   - Bonus amount: ₹${bonusAmount}`)
    console.log(`   - Plan: ${newPlan}`)

    if (bonusAmount > 0) {
      console.log(`💰 Processing bonus payment...`)

      // Add bonus to referrer's wallet
      await updateWalletBalance(referrerId, bonusAmount)
      console.log(`✅ Added ₹${bonusAmount} to referrer's wallet`)

      // Add 50 translations to referrer's total translation count
      const referrerRef = doc(db, COLLECTIONS.users, referrerId)
      await updateDoc(referrerRef, {
        [FIELD_NAMES.totalTranslations]: increment(50)
      })
      console.log(`✅ Added 50 bonus translations to referrer`)

      // Mark referral bonus as credited for this user
      const userRef = doc(db, COLLECTIONS.users, userId)
      await updateDoc(userRef, {
        [FIELD_NAMES.referralBonusCredited]: true
      })
      console.log(`✅ Marked referral bonus as credited for user ${userId}`)

      // Add transaction record for referral bonus
      await addTransaction(referrerId, {
        type: 'referral_bonus',
        amount: bonusAmount,
        description: `Referral bonus for ${newPlan} plan upgrade + 50 bonus translations (User: ${userData[FIELD_NAMES.name]})`
      })
      console.log(`✅ Added transaction record for referral bonus`)

      console.log(`🎉 REFERRAL BONUS COMPLETED: ₹${bonusAmount} + 50 translations for referrer ${referrerId}`)
    } else {
      console.log(`❌ No bonus amount calculated for plan: ${newPlan}`)
      console.log(`❌ Available plans:`, Object.keys({
        'Trial': 0, 'Junior': 300, 'Senior': 700, 'Expert': 1200
      }))
    }
  } catch (error) {
    console.error('❌ Error processing referral bonus:', error)
    console.error('❌ Error details:', error)
    // Don't throw error to avoid breaking plan update
  }
}

// Manual referral bonus processing (for testing/debugging)
export async function manualProcessReferralBonus(userId: string) {
  try {
    console.log(`🔧 MANUAL REFERRAL BONUS: Processing for user ${userId}`)

    const userData = await getUserData(userId)
    if (!userData) {
      console.log('❌ User not found')
      return { success: false, message: 'User not found' }
    }

    console.log(`🔍 User data:`)
    console.log(`   - Name: ${userData.name}`)
    console.log(`   - Plan: ${userData.plan}`)
    console.log(`   - Referred by: ${userData.referredBy || 'None'}`)
    console.log(`   - Bonus credited: ${userData.referralBonusCredited}`)

    if (userData.plan === 'Trial') {
      return { success: false, message: 'User is still on Trial plan. Upgrade to paid plan first.' }
    }

    if (!userData.referredBy) {
      return { success: false, message: 'User was not referred by anyone' }
    }

    if (userData.referralBonusCredited) {
      return { success: false, message: 'Referral bonus already credited' }
    }

    // Process the bonus manually
    await processReferralBonus(userId, 'Trial', userData.plan)

    return { success: true, message: `Referral bonus processed for ${userData.plan} plan` }
  } catch (error: any) {
    console.error('❌ Error in manual referral bonus processing:', error)
    return { success: false, message: `Error: ${error?.message || 'Unknown error'}` }
  }
}

// Get user translation settings (duration and earning per batch)
export async function getUserVideoSettings(userId: string) {
  try {
    const userData = await getUserData(userId)
    if (!userData) {
      return {
        translationDuration: 30, // Default 30 seconds for trial
        earningPerBatch: 25, // Default trial earning per batch of 50 translations
        plan: 'Trial',
        hasQuickAdvantage: false
      }
    }

    // Check if user has active quick translation advantage
    const hasActiveQuickAdvantage = checkQuickTranslationAdvantageActive(userData)

    let translationDuration = userData.translationDuration

    // If user has active quick translation advantage, use custom seconds or default to 30
    if (hasActiveQuickAdvantage) {
      translationDuration = userData.quickTranslationAdvantageSeconds || 30 // Use custom duration or default to 30 seconds
    } else {
      // Use plan-based translation duration, but allow admin overrides for non-trial users
      if (!translationDuration || userData.plan === 'Trial') {
        translationDuration = getPlanVideoDuration(userData.plan)
      }
    }

    return {
      translationDuration: translationDuration,
      earningPerBatch: getPlanEarning(userData.plan), // Earning per batch of 50 translations
      plan: userData.plan,
      hasQuickAdvantage: hasActiveQuickAdvantage,
      quickAdvantageExpiry: userData.quickTranslationAdvantageExpiry
    }
  } catch (error) {
    console.error('Error getting user translation settings:', error)
    return {
      translationDuration: 30, // Default 30 seconds for trial
      earningPerBatch: 25, // Default trial earning per batch of 50 translations
      plan: 'Trial',
      hasQuickAdvantage: false
    }
  }
}

// Check if user has active quick translation advantage
export function checkQuickTranslationAdvantageActive(userData: any): boolean {
  if (!userData.quickTranslationAdvantage || !userData.quickTranslationAdvantageExpiry) {
    return false
  }

  const now = new Date()
  const expiry = userData.quickTranslationAdvantageExpiry

  return now < expiry
}

// Grant quick translation advantage to user (admin function)
export async function grantQuickVideoAdvantage(userId: string, days: number, grantedBy: string, seconds: number = 30) {
  try {
    if (days <= 0 || days > 365) {
      throw new Error('Days must be between 1 and 365')
    }

    if (seconds < 1 || seconds > 420) {
      throw new Error('Seconds must be between 1 and 420 (7 minutes)')
    }

    // Use centralized copy-paste service
    const { grantCopyPastePermission } = await import('./copyPasteService')
    await grantCopyPastePermission(userId, days)

    console.log(`Granted copy-paste permission to user ${userId} for ${days} days by ${grantedBy}`)

    // Add transaction record
    await addTransaction(userId, {
      type: 'quick_advantage_granted',
      amount: 0,
      description: `Copy-paste permission granted for ${days} days by ${grantedBy}`
    })

    return { success: true }
  } catch (error) {
    console.error('Error granting quick video advantage:', error)
    throw error
  }
}

// Remove quick translation advantage from user (admin function)
export async function removeQuickVideoAdvantage(userId: string, removedBy: string) {
  try {
    // Use centralized copy-paste service
    const { removeCopyPastePermission } = await import('./copyPasteService')
    await removeCopyPastePermission(userId)

    console.log(`Removed copy-paste permission from user ${userId} by ${removedBy}`)

    // Add transaction record
    await addTransaction(userId, {
      type: 'quick_advantage_removed',
      amount: 0,
      description: `Copy-paste permission removed by ${removedBy}`
    })

    return { success: true }
  } catch (error) {
    console.error('Error removing quick video advantage:', error)
    throw error
  }
}

// Update user translation duration (admin function)
export async function updateUserVideoDuration(userId: string, durationInSeconds: number) {
  try {
    // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)
    const isQuickDuration = [1, 10, 30].includes(durationInSeconds)
    const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420

    if (!isQuickDuration && !isStandardDuration) {
      throw new Error('Translation duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration')
    }

    const userRef = doc(db, COLLECTIONS.users, userId)
    await updateDoc(userRef, {
      [FIELD_NAMES.translationDuration]: durationInSeconds
    })

    console.log(`Updated translation duration for user ${userId} to ${durationInSeconds} seconds`)
  } catch (error) {
    console.error('Error updating user translation duration:', error)
    throw error
  }
}

// Validate bank details
function validateBankDetails(bankDetails: BankDetails) {
  const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails

  if (!accountHolderName || accountHolderName.trim().length < 2) {
    throw new Error('Account holder name must be at least 2 characters long')
  }

  if (!accountNumber || !/^\d{9,18}$/.test(accountNumber.trim())) {
    throw new Error('Account number must be 9-18 digits')
  }

  if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {
    throw new Error('Invalid IFSC code format (e.g., SBIN0001234)')
  }

  if (!bankName || bankName.trim().length < 2) {
    throw new Error('Bank name must be at least 2 characters long')
  }
}

// Notification interface
export interface Notification {
  id?: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  targetUsers: 'all' | 'specific'
  userIds?: string[]
  createdAt: Date
  createdBy: string
  // All notifications are now blocking by default - no need for isBlocking field
}

// Add notification (admin function) - All notifications are now blocking
export async function addNotification(notification: Omit<Notification, 'id' | 'createdAt'>) {
  try {
    const notificationData = {
      title: notification.title,
      message: notification.message,
      type: notification.type,
      targetUsers: notification.targetUsers,
      userIds: notification.userIds || [],
      createdAt: Timestamp.now(),
      createdBy: notification.createdBy
    }

    console.log('Adding notification to Firestore:', notificationData)

    const docRef = await addDoc(collection(db, COLLECTIONS.notifications), notificationData)
    console.log('Notification added successfully with ID:', docRef.id)

    // Verify the notification was added
    const addedDoc = await getDoc(docRef)
    if (addedDoc.exists()) {
      console.log('Notification verified in database:', addedDoc.data())
    } else {
      console.warn('Notification not found after adding')
    }

    return docRef.id
  } catch (error) {
    console.error('Error adding notification:', error)
    throw error
  }
}

// Get notifications for a user
export async function getUserNotifications(userId: string, limitCount = 20) {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getUserNotifications:', userId)
      return []
    }

    console.log(`Loading notifications for user: ${userId}`)

    // Try to get notifications with fallback for indexing issues
    let allUsersSnapshot, specificUserSnapshot

    try {
      // Get notifications targeted to all users
      const allUsersQuery = query(
        collection(db, COLLECTIONS.notifications),
        where('targetUsers', '==', 'all'),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      )

      allUsersSnapshot = await getDocs(allUsersQuery)
      console.log(`Found ${allUsersSnapshot.docs.length} notifications for all users`)
    } catch (error) {
      console.warn('Error querying all users notifications, trying without orderBy:', error)
      // Fallback without orderBy if index is not ready
      const allUsersQuery = query(
        collection(db, COLLECTIONS.notifications),
        where('targetUsers', '==', 'all'),
        limit(limitCount)
      )
      allUsersSnapshot = await getDocs(allUsersQuery)
    }

    try {
      // Get notifications targeted to specific user
      const specificUserQuery = query(
        collection(db, COLLECTIONS.notifications),
        where('targetUsers', '==', 'specific'),
        where('userIds', 'array-contains', userId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      )

      specificUserSnapshot = await getDocs(specificUserQuery)
      console.log(`Found ${specificUserSnapshot.docs.length} notifications for specific user`)
    } catch (error) {
      console.warn('Error querying specific user notifications, trying without orderBy:', error)
      // Fallback without orderBy if index is not ready
      const specificUserQuery = query(
        collection(db, COLLECTIONS.notifications),
        where('targetUsers', '==', 'specific'),
        where('userIds', 'array-contains', userId),
        limit(limitCount)
      )
      specificUserSnapshot = await getDocs(specificUserQuery)
    }

    const notifications: Notification[] = []

    // Process all users notifications
    allUsersSnapshot.docs.forEach(doc => {
      notifications.push({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      } as Notification)
    })

    // Process specific user notifications
    specificUserSnapshot.docs.forEach(doc => {
      notifications.push({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      } as Notification)
    })

    // Sort by creation date (newest first)
    notifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())

    const finalNotifications = notifications.slice(0, limitCount)
    console.log(`Returning ${finalNotifications.length} total notifications for user`)

    return finalNotifications
  } catch (error) {
    console.error('Error getting user notifications:', error)
    return []
  }
}

// Get all notifications (admin function)
export async function getAllNotifications(limitCount = 50) {
  try {
    const q = query(
      collection(db, COLLECTIONS.notifications),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    )

    const querySnapshot = await getDocs(q)
    const notifications = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    })) as Notification[]

    return notifications
  } catch (error) {
    console.error('Error getting all notifications:', error)
    return []
  }
}

// Delete notification (admin function)
export async function deleteNotification(notificationId: string) {
  try {
    if (!notificationId || typeof notificationId !== 'string') {
      throw new Error('Invalid notification ID provided')
    }

    console.log('Deleting notification:', notificationId)
    await deleteDoc(doc(db, COLLECTIONS.notifications, notificationId))
    console.log('Notification deleted successfully')
  } catch (error) {
    console.error('Error deleting notification:', error)
    throw error
  }
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string, userId: string) {
  try {
    // For now, we'll store read status in localStorage since it's user-specific
    const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]')
    if (!readNotifications.includes(notificationId)) {
      readNotifications.push(notificationId)
      localStorage.setItem(`read_notifications_${userId}`, JSON.stringify(readNotifications))
    }
  } catch (error) {
    console.error('Error marking notification as read:', error)
  }
}

// Check if notification is read
export function isNotificationRead(notificationId: string, userId: string): boolean {
  try {
    const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]')
    return readNotifications.includes(notificationId)
  } catch (error) {
    console.error('Error checking notification read status:', error)
    return false
  }
}

// Get unread notification count
export function getUnreadNotificationCount(notifications: Notification[], userId: string): number {
  try {
    const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]')
    return notifications.filter(notification => !readNotifications.includes(notification.id)).length
  } catch (error) {
    console.error('Error getting unread notification count:', error)
    return 0
  }
}

// Get unread notifications - All notifications are now blocking
export async function getUnreadNotifications(userId: string): Promise<Notification[]> {
  try {
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid userId provided to getUnreadNotifications:', userId)
      return []
    }

    console.log(`Loading unread notifications for user: ${userId}`)

    // Get all notifications for the user
    const allNotifications = await getUserNotifications(userId, 50)

    // Filter for unread notifications
    const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]')
    const unreadNotifications = allNotifications.filter(notification =>
      notification.id &&
      !readNotifications.includes(notification.id)
    )

    console.log(`Found ${unreadNotifications.length} unread notifications`)
    return unreadNotifications
  } catch (error) {
    console.error('Error getting unread notifications:', error)
    return []
  }
}

// Check if user has unread notifications
export async function hasUnreadNotifications(userId: string): Promise<boolean> {
  try {
    const unreadNotifications = await getUnreadNotifications(userId)
    return unreadNotifications.length > 0
  } catch (error) {
    console.error('Error checking for unread notifications:', error)
    return false
  }
}

// Check if user has pending withdrawals
export async function hasPendingWithdrawals(userId: string): Promise<boolean> {
  try {
    const q = query(
      collection(db, COLLECTIONS.withdrawals),
      where('userId', '==', userId),
      where('status', '==', 'pending'),
      limit(1)
    )

    const snapshot = await getDocs(q)
    return !snapshot.empty
  } catch (error) {
    console.error('Error checking pending withdrawals:', error)
    return false
  }
}

// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)
export async function checkWithdrawalAllowed(userId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Check user plan first
    const userData = await getUserData(userId)
    if (!userData) {
      return {
        allowed: false,
        reason: 'Unable to verify user information. Please try again.'
      }
    }

    // Check if user is on trial plan
    if (userData.plan === 'Trial') {
      return {
        allowed: false,
        reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'
      }
    }

    // Check if user has pending withdrawals
    const hasPending = await hasPendingWithdrawals(userId)
    if (hasPending) {
      return {
        allowed: false,
        reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'
      }
    }

    const now = new Date()
    const currentHour = now.getHours()

    // Check time restrictions (10 AM to 6 PM)
    if (currentHour < 10 || currentHour >= 18) {
      return {
        allowed: false,
        reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'
      }
    }

    // Check admin leave day
    const { isAdminLeaveDay } = await import('./leaveService')
    const isAdminLeave = await isAdminLeaveDay(now)
    if (isAdminLeave) {
      return {
        allowed: false,
        reason: 'Withdrawals are not allowed on admin leave/holiday days'
      }
    }

    // Check user leave day
    const { isUserOnLeave } = await import('./leaveService')
    const isUserLeave = await isUserOnLeave(userId, now)
    if (isUserLeave) {
      return {
        allowed: false,
        reason: 'Withdrawals are not allowed on your leave days'
      }
    }

    return { allowed: true }
  } catch (error) {
    console.error('Error checking withdrawal allowed:', error)
    return { allowed: false, reason: 'Unable to verify withdrawal eligibility. Please try again.' }
  }
}

// Create withdrawal request
export async function createWithdrawalRequest(userId: string, amount: number, bankDetails: any) {
  try {
    // Check minimum withdrawal amount
    if (amount < 50) {
      throw new Error('Minimum withdrawal amount is ₹50')
    }

    // Check if withdrawal is allowed
    const withdrawalCheck = await checkWithdrawalAllowed(userId)
    if (!withdrawalCheck.allowed) {
      throw new Error(withdrawalCheck.reason)
    }

    // Check if user has sufficient balance
    const walletData = await getWalletData(userId)
    if (walletData.wallet < amount) {
      throw new Error('Insufficient wallet balance')
    }

    // Debit the amount from user's wallet immediately
    await updateWalletBalance(userId, -amount)

    // Add transaction record for withdrawal debit
    await addTransaction(userId, {
      type: 'withdrawal_request',
      amount: -amount,
      description: `Withdrawal request submitted - ₹${amount} debited from wallet`
    })

    const withdrawalData = {
      userId,
      amount,
      bankDetails,
      status: 'pending',
      date: Timestamp.now(),
      createdAt: Timestamp.now()
    }

    const docRef = await addDoc(collection(db, COLLECTIONS.withdrawals), withdrawalData)

    return docRef.id
  } catch (error) {
    console.error('Error creating withdrawal request:', error)
    throw error
  }
}

// Get user withdrawals
export async function getUserWithdrawals(userId: string, limitCount = 20) {
  try {
    const q = query(
      collection(db, COLLECTIONS.withdrawals),
      where('userId', '==', userId),
      orderBy('date', 'desc'),
      limit(limitCount)
    )

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      date: doc.data().date?.toDate()
    }))
  } catch (error) {
    console.error('Error getting user withdrawals:', error)
    return []
  }
}

// Generate unique referral code with TN prefix and sequential numbering
export async function generateUniqueReferralCode(): Promise<string> {
  try {
    // Try to get count from server for sequential numbering
    try {
      const usersCollection = collection(db, COLLECTIONS.users)
      const snapshot = await getCountFromServer(usersCollection)
      const count = snapshot.data().count
      const sequentialNumber = (count + 1).toString().padStart(4, '0')
      return `TN${sequentialNumber}`
    } catch (countError) {
      console.warn('Failed to get count from server, using fallback method:', countError)
      // Fallback to timestamp-based generation
      const timestamp = Date.now().toString().slice(-4)
      const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase()
      return `TN${timestamp}${randomPart}`
    }
  } catch (error) {
    console.error('Error generating unique referral code:', error)
    // Final fallback
    const timestamp = Date.now().toString().slice(-4)
    return `TN${timestamp}`
  }
}

// Generate sequential referral code (alias for generateUniqueReferralCode)
export async function generateSequentialReferralCode(): Promise<string> {
  return generateUniqueReferralCode()
}


