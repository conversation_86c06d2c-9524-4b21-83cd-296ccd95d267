"use strict";exports.id=6951,exports.ids=[6951,7087],exports.modules={744:(e,t,a)=>{a.d(t,{J:()=>o});var r=a(43210),s=a(3582);function o(e){let[t,a]=(0,r.useState)(!1),[o,n]=(0,r.useState)(!0);return{hasBlockingNotifications:t,isChecking:o,checkForBlockingNotifications:async()=>{try{n(!0);let t=await (0,s.iA)(e);a(t)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{n(!1)}},markAllAsRead:()=>{a(!1)}}}},55986:(e,t,a)=>{a.d(t,{l:()=>o});var r=a(43210),s=a(87087);function o({userId:e,checkInterval:t=3e4,enabled:a=!0}){let[o,n]=(0,r.useState)({blocked:!1,lastChecked:new Date}),[c,l]=(0,r.useState)(!1);return{leaveStatus:o,isChecking:c,checkLeaveStatus:(0,r.useCallback)(async()=>{if(e&&a)try{l(!0);let t=await (0,s.q8)(e);return n({blocked:t.blocked,reason:t.reason,lastChecked:new Date}),t}catch(e){return console.error("Error checking leave status:",e),n(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{l(!1)}},[e,a]),isBlocked:o.blocked}}},87087:(e,t,a)=>{a.d(t,{applyUserLeave:()=>d,cancelUserLeave:()=>m,createAdminLeave:()=>n,deleteAdminLeave:()=>l,getAdminLeaves:()=>c,getUserLeaves:()=>u,getUserMonthlyLeaveCount:()=>g,isAdminLeaveDay:()=>i,isUserOnLeave:()=>h,q8:()=>f});var r=a(33784),s=a(75535);let o={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function n(e){try{return(await (0,s.gS)((0,s.collection)(r.db,o.adminLeaves),{...e,date:s.Dc.fromDate(e.date),createdAt:s.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function c(){try{let e=(0,s.P)((0,s.collection)(r.db,o.adminLeaves),(0,s.My)("date","asc")),t=(await (0,s.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function l(e){try{await (0,s.kd)((0,s.H9)(r.db,o.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function i(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let n=(0,s.P)((0,s.collection)(r.db,o.adminLeaves),(0,s._M)("date",">=",s.Dc.fromDate(t)),(0,s._M)("date","<=",s.Dc.fromDate(a))),c=await (0,s.getDocs)(n),l=!c.empty;return l?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",c.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),l}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function d(e){try{let t,a,n,c=new Date,l=c.getFullYear(),i=c.getMonth()+1,d=await g(e.userId,l,i),u="pending";return d<4&&(u="approved",t="system",n=s.Dc.now(),a=`Auto-approved: ${d+1}/4 monthly leaves used`),{id:(await (0,s.gS)((0,s.collection)(r.db,o.userLeaves),{...e,date:s.Dc.fromDate(e.date),status:u,appliedAt:s.Dc.now(),...t&&{reviewedBy:t},...n&&{reviewedAt:n},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function u(e){try{let t=(0,s.P)((0,s.collection)(r.db,o.userLeaves),(0,s._M)("userId","==",e),(0,s.My)("date","desc"));return(await (0,s.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:e.data().reviewedAt?.toDate()}))}catch(e){throw console.error("Error getting user leaves:",e),e}}async function m(e){try{await (0,s.kd)((0,s.H9)(r.db,o.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function g(e,t,a){try{let n=new Date(t,a-1,1),c=new Date(t,a,0,23,59,59,999),l=(0,s.P)((0,s.collection)(r.db,o.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(n)),(0,s._M)("date","<=",s.Dc.fromDate(c)));return(await (0,s.getDocs)(l)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function h(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let n=new Date(t);n.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",n.toISOString());let c=(0,s.P)((0,s.collection)(r.db,o.userLeaves),(0,s._M)("userId","==",e),(0,s._M)("status","==","approved"),(0,s._M)("date",">=",s.Dc.fromDate(a)),(0,s._M)("date","<=",s.Dc.fromDate(n))),l=await (0,s.getDocs)(c),i=!l.empty;return i?console.log("\uD83D\uDC64 Found user leave(s) for today:",l.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),i}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function f(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await i(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await h(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}},98873:(e,t,a)=>{a.d(t,{A:()=>n});var r=a(60687),s=a(43210),o=a(3582);function n({userId:e,onAllRead:t}){let[a,n]=(0,s.useState)([]),[c,l]=(0,s.useState)(0),[i,d]=(0,s.useState)(!0),u=async()=>{let r=a[c];r?.id&&(await (0,o.bA)(r.id,e),c<a.length-1?l(c+1):t())};if(i)return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===a.length)return null;let m=a[c];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(m.type)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,r.jsxs)("p",{className:"text-blue-100 text-sm",children:[c+1," of ",a.length," notifications"]})]})]}),(0,r.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:m.title}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,r.jsx)("p",{className:"text-gray-800 leading-relaxed",children:m.message})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,r.jsxs)("span",{children:["From: ",m.createdBy]}),(0,r.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?`${Math.floor(t/60)} minutes ago`:t<86400?`${Math.floor(t/3600)} hours ago`:`${Math.floor(t/86400)} days ago`})(m.createdAt)})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[c+1,"/",a.length]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${(c+1)/a.length*100}%`}})})]}),(0,r.jsxs)("button",{onClick:u,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,r.jsx)("i",{className:"fas fa-check"}),(0,r.jsx)("span",{children:c<a.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("i",{className:"fas fa-info-circle"}),(0,r.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}};