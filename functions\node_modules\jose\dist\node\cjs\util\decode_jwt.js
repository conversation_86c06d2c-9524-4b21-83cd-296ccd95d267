"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.decodeJwt = void 0;
const base64url_js_1 = require("./base64url.js");
const buffer_utils_js_1 = require("../lib/buffer_utils.js");
const is_object_js_1 = require("../lib/is_object.js");
const errors_js_1 = require("./errors.js");
function decodeJwt(jwt) {
    if (typeof jwt !== 'string')
        throw new errors_js_1.JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');
    const { 1: payload, length } = jwt.split('.');
    if (length === 5)
        throw new errors_js_1.JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');
    if (length !== 3)
        throw new errors_js_1.JWTInvalid('Invalid JWT');
    if (!payload)
        throw new errors_js_1.JWTInvalid('JWTs must contain a payload');
    let decoded;
    try {
        decoded = (0, base64url_js_1.decode)(payload);
    }
    catch {
        throw new errors_js_1.JWTInvalid('Failed to base64url decode the payload');
    }
    let result;
    try {
        result = JSON.parse(buffer_utils_js_1.decoder.decode(decoded));
    }
    catch {
        throw new errors_js_1.JWTInvalid('Failed to parse the decoded payload as JSON');
    }
    if (!(0, is_object_js_1.default)(result))
        throw new errors_js_1.JWTInvalid('Invalid JWT Claims Set');
    return result;
}
exports.decodeJwt = decodeJwt;
