(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8733],{2899:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>g});var s=t(5155),n=t(2115),r=t(6874),i=t.n(r),l=t(6681),o=t(6779),c=t(3592),d=t(3737),u=t(4752),x=t.n(u);function g(){let{user:e,loading:a,isAdmin:r}=(0,l.wC)(),[u,g]=(0,n.useState)([]),[m,p]=(0,n.useState)(!0),[y,h]=(0,n.useState)(""),[f,b]=(0,n.useState)(!1),[v,D]=(0,n.useState)(0),[j,N]=(0,n.useState)(null),[w,k]=(0,n.useState)(!1),[T,A]=(0,n.useState)({name:"",email:"",mobile:"",referralCode:"",referredBy:"",plan:"",activeDays:0,totalTranslations:0,todayTranslations:0,wallet:0,status:"active",quickTranslationAdvantage:!1,quickTranslationAdvantageDays:1,quickTranslationAdvantageSeconds:30}),[C,S]=(0,n.useState)(!1),[q,E]=(0,n.useState)(1),[P,B]=(0,n.useState)(!0),[L,U]=(0,n.useState)(null);(0,n.useEffect)(()=>{r&&F()},[r]);let F=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];try{p(!0);let a=await (0,o.lo)(50,e?null:L);if(e){g(a.users),E(1);try{let e=await (0,o.nQ)();D(e)}catch(e){console.error("Error getting total user count:",e)}}else g(e=>[...e,...a.users]);U(a.lastDoc),B(a.hasMore)}catch(e){console.error("Error loading users:",e),x().fire({icon:"error",title:"Error",text:"Failed to load users. Please try again."})}finally{p(!1)}},I=async()=>{if(!y.trim())return void F();try{b(!0);let e=await (0,o.searchUsers)(y.trim());g(e),B(!1)}catch(e){console.error("Error searching users:",e),x().fire({icon:"error",title:"Search Failed",text:"Failed to search users. Please try again."})}finally{b(!1)}},R=async e=>{N(e);let a=!1;try{let{checkCopyPastePermission:s}=await t.e(7718).then(t.bind(t,7718));a=(await s(e.id)).hasPermission,console.log("\uD83D\uDCCB Current copy-paste status for ".concat(e.name,": ").concat(a))}catch(t){console.error("Error checking copy-paste status:",t),a=e.quickTranslationAdvantage||!1}A({name:e.name,email:e.email,mobile:e.mobile,referralCode:e.referralCode,referredBy:e.referredBy,plan:e.plan,activeDays:e.activeDays,totalTranslations:e.totalTranslations,todayTranslations:e.todayTranslations,wallet:e.wallet||0,status:e.status,quickTranslationAdvantage:a,quickTranslationAdvantageDays:e.quickTranslationAdvantageDays||1,quickTranslationAdvantageSeconds:e.quickTranslationAdvantageSeconds||30}),k(!0)},V=async()=>{if(j)try{S(!0);let a=j.plan,s=T.plan,n=a!==s,r={name:T.name,email:T.email,mobile:T.mobile,referralCode:T.referralCode,referredBy:T.referredBy,plan:T.plan,activeDays:T.activeDays,totalTranslations:T.totalTranslations,todayTranslations:T.todayTranslations,wallet:T.wallet,status:T.status};await (0,o.TK)(j.id,r),console.log("\uD83D\uDD27 Processing copy-paste permission changes..."),console.log("   - Checkbox checked: ".concat(T.quickTranslationAdvantage)),console.log("   - User ID: ".concat(j.id));let{checkCopyPastePermission:i}=await t.e(7718).then(t.bind(t,7718)),l=await i(j.id),d=l.hasPermission;if(console.log("   - Current has permission: ".concat(d)),console.log("   - Days remaining: ".concat(l.daysRemaining)),T.quickTranslationAdvantage&&!d?(console.log("✅ Granting copy-paste permission for ".concat(T.quickTranslationAdvantageDays," days")),await (0,c.w1)(j.id,T.quickTranslationAdvantageDays,(null==e?void 0:e.email)||"admin",T.quickTranslationAdvantageSeconds)):!T.quickTranslationAdvantage&&d?(console.log("❌ Removing copy-paste permission"),await (0,c.wT)(j.id,(null==e?void 0:e.email)||"admin")):T.quickTranslationAdvantage&&d?(console.log("\uD83D\uDD04 Updating copy-paste permission for ".concat(T.quickTranslationAdvantageDays," days")),await (0,c.wT)(j.id,(null==e?void 0:e.email)||"admin"),await (0,c.w1)(j.id,T.quickTranslationAdvantageDays,(null==e?void 0:e.email)||"admin",T.quickTranslationAdvantageSeconds)):console.log("⏭️ No copy-paste permission changes needed"),n)try{await (0,c.II)(j.id,s),console.log("Updated plan expiry for user ".concat(j.id,": ").concat(a," -> ").concat(s))}catch(e){console.error("Error updating plan expiry:",e)}if(n&&"Trial"===a&&"Trial"!==s)try{console.log("Processing referral bonus for user ".concat(j.id,": ").concat(a," -> ").concat(s)),await (0,c.IK)(j.id,a,s),x().fire({icon:"success",title:"User Updated & Referral Bonus Processed",html:'\n              <div class="text-left">\n                <p><strong>User plan updated:</strong> '.concat(a," → ").concat(s,"</p>\n                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>\n              </div>\n            "),timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error processing referral bonus:",e),x().fire({icon:"warning",title:"User Updated (Referral Bonus Issue)",html:'\n              <div class="text-left">\n                <p><strong>User plan updated successfully:</strong> '.concat(a," → ").concat(s,'</p>\n                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>\n                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>\n              </div>\n            '),timer:5e3,showConfirmButton:!1})}else{let e="User information has been updated successfully";T.quickTranslationAdvantage&&!d?e+=". Copy-paste permission granted for ".concat(T.quickTranslationAdvantageDays," days."):!T.quickTranslationAdvantage&&d?e+=". Copy-paste permission removed.":T.quickTranslationAdvantage&&d&&(e+=". Copy-paste permission updated for ".concat(T.quickTranslationAdvantageDays," days.")),x().fire({icon:"success",title:"User Updated",text:e,timer:3e3,showConfirmButton:!1})}let u=await i(j.id);console.log("\uD83D\uDD04 Refreshed copy-paste status: ".concat(u.hasPermission)),g(e=>e.map(e=>e.id===j.id?{...e,...r,quickTranslationAdvantage:u.hasPermission,quickTranslationAdvantageDays:T.quickTranslationAdvantageDays,quickTranslationAdvantageSeconds:T.quickTranslationAdvantageSeconds}:e)),A(e=>({...e,quickTranslationAdvantage:u.hasPermission}));try{let a="copyPasteUpdate_".concat(j.id),t={userId:j.id,hasPermission:u.hasPermission,timestamp:Date.now(),updatedBy:(null==e?void 0:e.email)||"admin"};localStorage.setItem(a,JSON.stringify(t)),window.dispatchEvent(new CustomEvent("copyPastePermissionChanged",{detail:t})),console.log("\uD83D\uDCE1 Broadcasted copy-paste permission change for user ".concat(j.id))}catch(e){console.error("Error broadcasting copy-paste permission change:",e)}setTimeout(()=>{k(!1)},500),N(null),await F()}catch(e){console.error("Error updating user:",e),x().fire({icon:"error",title:"Update Failed",text:"Failed to update user. Please try again."})}finally{S(!1)}},M=async e=>{if((await x().fire({icon:"warning",title:"Delete User",text:"Are you sure you want to delete ".concat(e.name,"? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,o.hG)(e.id),g(a=>a.filter(a=>a.id!==e.id)),x().fire({icon:"success",title:"User Deleted",text:"User has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting user:",e),x().fire({icon:"error",title:"Delete Failed",text:"Failed to delete user. Please try again."})}},G=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2)),O=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},_=e=>{switch(e){case"active":return"bg-green-500";case"inactive":return"bg-red-500";case"suspended":return"bg-yellow-500";default:return"bg-gray-500"}},J=async()=>{try{x().fire({title:"Exporting Users...",text:"Please wait while we prepare your export file.",allowOutsideClick:!1,didOpen:()=>{x().showLoading()}});let e=await (0,o.CF)();if(0===e.length)return void x().fire({icon:"warning",title:"No Data",text:"No users to export."});console.log("\uD83D\uDCCA Exporting ".concat(e.length," users with copy-paste data...")),e.slice(0,3).forEach(e=>{console.log("\uD83D\uDCCA Sample user ".concat(e.email," copy-paste data:"),{quickTranslationAdvantageExpiry:e.quickTranslationAdvantageExpiry,quickVideoAdvantageExpiry:e.quickVideoAdvantageExpiry,hasExpiry:!!(e.quickTranslationAdvantageExpiry||e.quickVideoAdvantageExpiry)})});let a=(0,d.Fz)(e);(0,d.Bf)(a,"users"),x().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(e.length," users to CSV file. Check console for copy-paste data details."),timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error exporting users:",e),x().fire({icon:"error",title:"Export Failed",text:"Failed to export users. Please try again."})}};return a?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)(i(),{href:"/admin",className:"text-gray-600 hover:text-gray-800",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),v>0&&(0,s.jsx)("p",{className:"text-sm text-gray-600",children:y?"Showing ".concat(u.length," of ").concat(v," users"):"Total: ".concat(v," users")})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(i(),{href:"/admin/upload-users",className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]}),(0,s.jsxs)("button",{onClick:J,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:()=>F(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("input",{type:"text",value:y,onChange:e=>h(e.target.value),placeholder:"Search by name, email, mobile, or referral code...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyDown:e=>"Enter"===e.key&&I()}),(0,s.jsx)("button",{onClick:I,disabled:f,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Searching..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-search mr-2"}),"Search"]})}),y&&(0,s.jsx)("button",{onClick:()=>{h(""),F()},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times"})})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Translations"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quick Copy-Paste"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m&&0===u.length?(0,s.jsx)("tr",{children:(0,s.jsxs)("td",{colSpan:8,className:"px-6 py-4 text-center",children:[(0,s.jsx)("div",{className:"spinner mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading users..."})]})}):0===u.length?(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:8,className:"px-6 py-4 text-center text-gray-500",children:"No users found"})}):u.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined: ",e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():new Date(e.joinedDate).toLocaleDateString()]})]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.mobile}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Code: ",e.referralCode]}),e.referredBy&&(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Ref: ",e.referredBy]})]}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(O(e.plan)),children:e.plan}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Days: ",e.activeDays]})]}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.totalTranslations]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Today: ",e.todayTranslations]})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.quickTranslationAdvantage&&e.quickTranslationAdvantageExpiry&&new Date<e.quickTranslationAdvantageExpiry?(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500",children:"Enabled"}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Until: ",e.quickTranslationAdvantageExpiry instanceof Date?e.quickTranslationAdvantageExpiry.toLocaleDateString():new Date(e.quickTranslationAdvantageExpiry).toLocaleDateString()]})]}):(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500",children:"Disabled"})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,s.jsx)("i",{className:"fas fa-wallet mr-1 text-green-500"}),G(e.wallet||0)]}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"Total Balance"})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(_(e.status)),children:e.status})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>R(e),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:(0,s.jsx)("i",{className:"fas fa-edit"})}),(0,s.jsx)("button",{onClick:()=>M(e),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,s.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),P&&!m&&u.length>0&&(0,s.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 text-center",children:(0,s.jsxs)("button",{onClick:()=>{P&&!m&&F(!1)},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:[(0,s.jsx)("i",{className:"fas fa-chevron-down mr-2"}),"Load More Users"]})})]})}),w&&j&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200 flex-shrink-0",children:(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Edit User"})}),(0,s.jsxs)("div",{className:"p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,s.jsx)("input",{type:"text",value:T.name,onChange:e=>A(a=>({...a,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,s.jsx)("input",{type:"email",value:T.email,onChange:e=>A(a=>({...a,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile"}),(0,s.jsx)("input",{type:"text",value:T.mobile,onChange:e=>A(a=>({...a,mobile:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Code"}),(0,s.jsx)("input",{type:"text",value:T.referralCode,onChange:e=>A(a=>({...a,referralCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referred By"}),(0,s.jsx)("input",{type:"text",value:T.referredBy,onChange:e=>A(a=>({...a,referredBy:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,s.jsxs)("select",{value:T.plan,onChange:e=>A(a=>({...a,plan:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"Trial",children:"Trial"}),(0,s.jsx)("option",{value:"Junior",children:"Junior"}),(0,s.jsx)("option",{value:"Senior",children:"Senior"}),(0,s.jsx)("option",{value:"Expert",children:"Expert"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Active Days"}),(0,s.jsx)("input",{type:"number",value:T.activeDays,onChange:e=>A(a=>({...a,activeDays:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Translations"}),(0,s.jsx)("input",{type:"number",value:T.totalTranslations,onChange:e=>A(a=>({...a,totalTranslations:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Today Translations"}),(0,s.jsx)("input",{type:"number",value:T.todayTranslations,onChange:e=>A(a=>({...a,todayTranslations:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wallet Balance (₹)"}),(0,s.jsx)("input",{type:"number",step:"0.01",value:T.wallet,onChange:e=>A(a=>({...a,wallet:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,s.jsxs)("select",{value:T.status,onChange:e=>A(a=>({...a,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"inactive",children:"Inactive"}),(0,s.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,s.jsxs)("h4",{className:"text-md font-semibold text-gray-900 mb-3",children:[(0,s.jsx)("i",{className:"fas fa-copy mr-2 text-yellow-500"}),"Quick Translation Copy-Paste Advantage"]}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mb-3",children:"When enabled, user can copy-paste English text instead of typing manually"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"quickTranslationAdvantage",checked:T.quickTranslationAdvantage,onChange:e=>A(a=>({...a,quickTranslationAdvantage:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"quickTranslationAdvantage",className:"ml-2 block text-sm text-gray-700",children:"Enable Copy-Paste for English Text"})]}),T.quickTranslationAdvantage&&(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 ml-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Days"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:T.quickTranslationAdvantageDays,onChange:e=>A(a=>({...a,quickTranslationAdvantageDays:parseInt(e.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Translation Duration"}),(0,s.jsxs)("select",{value:T.quickTranslationAdvantageSeconds,onChange:e=>A(a=>({...a,quickTranslationAdvantageSeconds:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:1,children:"1 second"}),(0,s.jsx)("option",{value:10,children:"10 seconds"}),(0,s.jsx)("option",{value:30,children:"30 seconds"})]})]})]}),j&&(0,s.jsx)("div",{className:"ml-6 p-3 bg-gray-50 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,s.jsx)("strong",{children:"Current Status:"})," ",j.quickTranslationAdvantage&&j.quickTranslationAdvantageExpiry&&new Date<j.quickTranslationAdvantageExpiry?(0,s.jsxs)("span",{className:"text-green-600",children:["Copy-paste enabled until ",j.quickTranslationAdvantageExpiry instanceof Date?j.quickTranslationAdvantageExpiry.toLocaleDateString():new Date(j.quickTranslationAdvantageExpiry).toLocaleDateString()]}):(0,s.jsx)("span",{className:"text-gray-500",children:"Copy-paste disabled - manual typing required"})]})})]})]})]}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-200 flex-shrink-0",children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("button",{onClick:V,disabled:C,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:C?"Saving...":"Save Changes"}),(0,s.jsx)("button",{onClick:()=>k(!1),className:"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700",children:"Cancel"})]})})]})})]})}},3737:(e,a,t)=>{"use strict";function s(e,a,t){if(!e||0===e.length)return void alert("No data to export");let s=t||Object.keys(e[0]),n=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],r=new Blob(["\uFEFF"+[s.join(","),...e.map(e=>s.map(a=>{let t=e[a];if(null==t)return"";let s=n.some(e=>a.toLowerCase().includes(e.toLowerCase()));if("string"==typeof t){let e=t.replace(/"/g,'""');return'"'.concat(e,'"')}return t instanceof Date?'"'.concat(t.toLocaleDateString(),'"'):"object"==typeof t&&null!==t&&t.toDate?'"'.concat(t.toDate().toLocaleDateString(),'"'):s&&("number"==typeof t||!isNaN(Number(t)))?'"'.concat(t,'"'):"number"==typeof t?t.toString():'"'.concat(String(t),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(r);i.setAttribute("href",e),i.setAttribute("download","".concat(a,"_").concat(new Date().toISOString().split("T")[0],".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function n(e){return e.map(a=>{let t=0,s=null,n="No",r=a.quickTranslationAdvantageExpiry||a.quickVideoAdvantageExpiry;if(r)try{r instanceof Date?s=r:r.toDate&&"function"==typeof r.toDate?s=r.toDate():s=new Date(r);let i=new Date,l=s.getTime()-i.getTime();n=(t=Math.max(0,Math.ceil(l/864e5)))>0?"Yes":"No",5>e.indexOf(a)&&console.log("\uD83D\uDCCA Export debug for user ".concat(a.email,":"),{expiryField:r,expiryFieldType:typeof r,copyPasteExpiryDate:s,copyPasteRemainingDays:t,copyPastePermission:n,hasQuickTranslationAdvantageExpiry:!!a.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!a.quickVideoAdvantageExpiry})}catch(e){console.error("❌ Error calculating copy-paste days for user ".concat(a.email,":"),e)}else 5>e.indexOf(a)&&console.log("\uD83D\uDCCA Export debug for user ".concat(a.email,": No copy-paste expiry field found"));return{"User ID":a.id||"",Name:a.name||"",Email:a.email||"",Mobile:String(a.mobile||""),"Referral Code":a.referralCode||"","Referred By":a.referredBy||"Direct",Plan:a.plan||"","Plan Expiry":a.planExpiry instanceof Date?a.planExpiry.toLocaleDateString():a.planExpiry?new Date(a.planExpiry).toLocaleDateString():"","Active Days":a.activeDays||0,"Total Translations":a.totalTranslations||a.totalVideos||0,"Today Translations":a.todayTranslations||a.todayVideos||0,"Last Translation Date":a.lastTranslationDate instanceof Date?a.lastTranslationDate.toLocaleDateString():a.lastTranslationDate?new Date(a.lastTranslationDate).toLocaleDateString():a.lastVideoDate instanceof Date?a.lastVideoDate.toLocaleDateString():a.lastVideoDate?new Date(a.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":n,"Copy-Paste Remaining Days":t,"Copy-Paste Expiry":s?s.toLocaleDateString():"","Copy-Paste Granted By":a.quickTranslationAdvantageGrantedBy||a.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":a.quickTranslationAdvantageGrantedAt?a.quickTranslationAdvantageGrantedAt instanceof Date?a.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(a.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":a.wallet||0,"Referral Bonus Credited":a.referralBonusCredited?"Yes":"No",Status:a.status||"","Joined Date":a.joinedDate instanceof Date?a.joinedDate.toLocaleDateString():a.joinedDate?new Date(a.joinedDate).toLocaleDateString():"","Joined Time":a.joinedDate instanceof Date?a.joinedDate.toLocaleTimeString():a.joinedDate?new Date(a.joinedDate).toLocaleTimeString():""}})}function r(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>{var a,t,s,n;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(a=e.bankDetails)?void 0:a.accountHolderName)||"","Bank Name":(null==(t=e.bankDetails)?void 0:t.bankName)||"","Account Number":String((null==(s=e.bankDetails)?void 0:s.accountNumber)||""),"IFSC Code":(null==(n=e.bankDetails)?void 0:n.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function l(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}t.d(a,{Bf:()=>s,Fz:()=>n,Pe:()=>l,dB:()=>i,sL:()=>r})},5125:(e,a,t)=>{Promise.resolve().then(t.bind(t,2899))}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,6681,3592,6779,8441,1684,7358],()=>a(5125)),_N_E=e.O()}]);