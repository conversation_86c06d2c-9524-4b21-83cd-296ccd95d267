"use strict";exports.id=3582,exports.ids=[3582],exports.modules={3582:(e,t,a)=>{a.d(t,{AX:()=>C,COLLECTIONS:()=>i,HY:()=>w,I0:()=>g,II:()=>A,IK:()=>E,QD:()=>H,Ss:()=>N,Yr:()=>o,_I:()=>k,_f:()=>I,addTransaction:()=>u,b6:()=>d,bA:()=>P,fP:()=>U,getPlanValidityDays:()=>v,getUserData:()=>s,getUserVideoSettings:()=>$,getVideoCountData:()=>c,getWalletData:()=>l,gj:()=>m,i8:()=>L,iA:()=>M,iF:()=>p,isUserPlanExpired:()=>T,mm:()=>b,mv:()=>q,pl:()=>f,ul:()=>_,updateWalletBalance:()=>h,w1:()=>S,wT:()=>x,x4:()=>Z,xj:()=>G,yx:()=>y,z8:()=>B,zb:()=>D});var r=a(75535),n=a(33784);let o={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalTranslations:"totalTranslations",todayTranslations:"todayTranslations",lastTranslationDate:"lastTranslationDate",translationDuration:"translationDuration",quickTranslationAdvantage:"quickTranslationAdvantage",quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",quickTranslationAdvantageDays:"quickTranslationAdvantageDays",quickTranslationAdvantageSeconds:"quickTranslationAdvantageSeconds",quickTranslationAdvantageGrantedBy:"quickTranslationAdvantageGrantedBy",quickTranslationAdvantageGrantedAt:"quickTranslationAdvantageGrantedAt",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},i={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserData:",e),null;let t=await (0,r.x7)((0,r.H9)(n.db,i.users,e));if(t.exists()){let e=t.data(),a={name:String(e[o.name]||""),email:String(e[o.email]||""),mobile:String(e[o.mobile]||""),referralCode:String(e[o.referralCode]||""),referredBy:String(e[o.referredBy]||""),referralBonusCredited:!!e[o.referralBonusCredited],plan:String(e[o.plan]||"Trial"),planExpiry:e[o.planExpiry]?.toDate()||null,activeDays:Number(e[o.activeDays]||0),lastActiveDayUpdate:e[o.lastActiveDayUpdate]?.toDate()||null,joinedDate:e[o.joinedDate]?.toDate()||new Date,translationDuration:Number(e[o.translationDuration]||("Trial"===e[o.plan]?30:300)),quickTranslationAdvantage:!!e[o.quickTranslationAdvantage],quickTranslationAdvantageExpiry:e[o.quickTranslationAdvantageExpiry]?.toDate()||null,quickTranslationAdvantageDays:Number(e[o.quickTranslationAdvantageDays]||0),quickTranslationAdvantageSeconds:Number(e[o.quickTranslationAdvantageSeconds]||30),quickTranslationAdvantageGrantedBy:String(e[o.quickTranslationAdvantageGrantedBy]||""),quickTranslationAdvantageGrantedAt:e[o.quickTranslationAdvantageGrantedAt]?.toDate()||null};return console.log("getUserData result:",a),a}return null}catch(e){return console.error("Error getting user data:",e),null}}async function l(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getWalletData:",e),{wallet:0};let t=await (0,r.x7)((0,r.H9)(n.db,i.users,e));if(t.exists()){let e=t.data(),a={wallet:Number(e[o.wallet]||0)};return console.log("getWalletData result:",a),a}return{wallet:0}}catch(e){return console.error("Error getting wallet data:",e),{wallet:0}}}async function c(e){try{let t=(0,r.H9)(n.db,i.users,e),a=await (0,r.x7)(t);if(a.exists()){let n=a.data(),i=n[o.totalTranslations]||0,s=n[o.todayTranslations]||0,l=n[o.lastTranslationDate]?.toDate(),c=new Date;if((!l||l.toDateString()!==c.toDateString())&&s>0){console.log(`🔄 Resetting daily translation count for user ${e} (was ${s})`),await (0,r.mZ)(t,{[o.todayTranslations]:0}),s=0;try{await p(e)}catch(e){console.error("Error updating active days during daily reset:",e)}}return{totalTranslations:i,todayTranslations:s,remainingTranslations:Math.max(0,50-s)}}return{totalTranslations:0,todayTranslations:0,remainingTranslations:50}}catch(e){throw console.error("Error getting video count data:",e),e}}async function d(e,t){try{await (0,r.mZ)((0,r.H9)(n.db,i.users,e),t)}catch(e){throw console.error("Error updating user data:",e),e}}async function u(e,t){try{let a={[o.userId]:e,[o.type]:t.type,[o.amount]:t.amount,[o.description]:t.description,[o.status]:t.status||"completed",[o.date]:r.Dc.now()};await (0,r.gS)((0,r.collection)(n.db,i.transactions),a)}catch(e){throw console.error("Error adding transaction:",e),e}}async function g(e,t=10){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getTransactions:",e),[];let a=(0,r.P)((0,r.collection)(n.db,i.transactions),(0,r._M)(o.userId,"==",e),(0,r.AB)(t)),s=(await (0,r.getDocs)(a)).docs.map(e=>({id:e.id,...e.data(),date:e.data()[o.date]?.toDate()}));return s.sort((e,t)=>{let a=e.date||new Date(0);return(t.date||new Date(0)).getTime()-a.getTime()}),s}catch(e){return console.error("Error getting transactions:",e),[]}}async function f(e){try{let t=(0,r.P)((0,r.collection)(n.db,i.users),(0,r._M)(o.referredBy,"==",e));return(await (0,r.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[o.joinedDate]?.toDate()}))}catch(e){throw console.error("Error getting referrals:",e),e}}async function y(e){try{let t=new Date,a=(0,r.H9)(n.db,i.users,e),s=await (0,r.x7)(a);if(s.exists()){let n=s.data(),i=n[o.lastTranslationDate]?.toDate(),l=n[o.todayTranslations]||0;(!i||i.toDateString()!==t.toDateString())&&l>0?(console.log(`🔄 Resetting and updating daily translation count for user ${e}`),await (0,r.mZ)(a,{[o.totalTranslations]:(0,r.GV)(1),[o.todayTranslations]:1,[o.lastTranslationDate]:r.Dc.fromDate(t)})):await (0,r.mZ)(a,{[o.totalTranslations]:(0,r.GV)(1),[o.todayTranslations]:(0,r.GV)(1),[o.lastTranslationDate]:r.Dc.fromDate(t)})}else await (0,r.mZ)(a,{[o.totalTranslations]:(0,r.GV)(1),[o.todayTranslations]:(0,r.GV)(1),[o.lastTranslationDate]:r.Dc.fromDate(t)})}catch(e){throw console.error("Error updating translation count:",e),e}}async function w(e){try{let t=(0,r.H9)(n.db,i.users,e);await (0,r.mZ)(t,{[o.todayTranslations]:0}),console.log(`✅ Reset daily translation count for user ${e}`)}catch(e){throw console.error("Error resetting daily translation count:",e),e}}async function p(e){try{let{updateUserActiveDays:t}=await Promise.resolve().then(a.bind(a,51705));return await t(e)}catch(e){throw console.error("Error updating user active days:",e),e}}async function m(){try{console.log("\uD83D\uDD27 Starting to fix all users active days...");let e=await (0,r.getDocs)((0,r.collection)(n.db,i.users)),t=0,a=0;for(let r of e.docs)try{await p(r.id),t++}catch(e){console.error(`Error fixing active days for user ${r.id}:`,e),a++}return console.log(`✅ Fixed active days for ${t} users, ${a} errors`),{fixedCount:t,errorCount:a}}catch(e){throw console.error("Error fixing all users active days:",e),e}}async function h(e,t){try{let a=(0,r.H9)(n.db,i.users,e);await (0,r.mZ)(a,{[o.wallet]:(0,r.GV)(t)})}catch(e){throw console.error("Error updating wallet balance:",e),e}}async function b(e,t){try{if(!e||"string"!=typeof e)throw Error("Invalid userId provided");var a=t;let{accountHolderName:s,accountNumber:l,ifscCode:c,bankName:d}=a;if(!s||s.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!l||!/^\d{9,18}$/.test(l.trim()))throw Error("Account number must be 9-18 digits");if(!c||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(c.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!d||d.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,r.H9)(n.db,i.users,e);await (0,r.mZ)(u,{[o.bankAccountHolderName]:t.accountHolderName.trim(),[o.bankAccountNumber]:t.accountNumber.trim(),[o.bankIfscCode]:t.ifscCode.trim().toUpperCase(),[o.bankName]:t.bankName.trim(),[o.bankDetailsUpdated]:r.Dc.now()}),console.log("Bank details saved successfully for user:",e)}catch(e){throw console.error("Error saving bank details:",e),e}}async function D(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getBankDetails:",e),null;let t=await (0,r.x7)((0,r.H9)(n.db,i.users,e));if(t.exists()){let e=t.data();if(e[o.bankAccountNumber]){let t={accountHolderName:String(e[o.bankAccountHolderName]||""),accountNumber:String(e[o.bankAccountNumber]||""),ifscCode:String(e[o.bankIfscCode]||""),bankName:String(e[o.bankName]||"")};return console.log("getBankDetails result found"),t}}return console.log("No bank details found for user"),null}catch(e){return console.error("Error getting bank details:",e),null}}function v(e){return({Trial:2,Junior:30,Senior:30,Expert:30,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[e]||2}async function T(e){try{let t=await s(e);if(!t)return{expired:!0,reason:"User data not found"};if("Trial"===t.plan){let e=t.joinedDate||new Date,a=new Date,r=Math.floor((a.getTime()-e.getTime())/864e5),n=r+1,o=Math.max(0,2-r);return console.log("\uD83D\uDCC5 Trial plan calculation:",{joinedDate:e.toDateString(),today:a.toDateString(),daysSinceJoining:r,activeDays:n,trialDaysLeft:o}),{expired:o<=0,reason:o<=0?"Trial period expired":void 0,daysLeft:o,activeDays:n}}if(t.planExpiry){let e=new Date,a=e>t.planExpiry,r=a?0:Math.ceil((t.planExpiry.getTime()-e.getTime())/864e5);return{expired:a,reason:a?"Plan subscription expired":void 0,daysLeft:r,activeDays:t.activeDays||0}}let a=v(t.plan),r=t.activeDays||0,n=Math.max(0,a-r),o=n<=0;return{expired:o,reason:o?`Plan validity period (${a} days) exceeded based on active days`:void 0,daysLeft:n,activeDays:r}}catch(e){return console.error("Error checking plan expiry:",e),{expired:!0,reason:"Error checking plan status"}}}async function A(e,t,a){try{let s=(0,r.H9)(n.db,i.users,e);if("Trial"===t)await (0,r.mZ)(s,{[o.planExpiry]:null});else{let n;if(a)n=a;else{let e=v(t),a=new Date;n=new Date(a.getTime()+24*e*36e5)}await (0,r.mZ)(s,{[o.planExpiry]:r.Dc.fromDate(n)}),console.log(`Updated plan expiry for user ${e} to ${n.toDateString()}`)}}catch(e){throw console.error("Error updating plan expiry:",e),e}}async function E(e,t,a){try{if(console.log(`🎯 REFERRAL BONUS DEBUG: Starting process for user ${e}`),console.log(`🎯 Plan change: ${t} → ${a}`),"Trial"!==t||"Trial"===a){console.log("❌ Referral bonus only applies when upgrading from Trial to paid plan"),console.log(`❌ Current conditions: oldPlan=${t}, newPlan=${a}`);return}console.log(`✅ Processing referral bonus for user ${e} upgrading from ${t} to ${a}`);let s=await (0,r.x7)((0,r.H9)(n.db,i.users,e));if(!s.exists())return void console.log("❌ User not found in database");let l=s.data(),c=l[o.referredBy],d=l[o.referralBonusCredited];if(console.log(`🔍 User referral data:`),console.log(`   - Referred by: ${c||"None"}`),console.log(`   - Already credited: ${d}`),console.log(`   - User name: ${l[o.name]}`),!c)return void console.log("❌ User was not referred by anyone, skipping bonus processing");if(d)return void console.log("❌ Referral bonus already credited for this user, skipping");console.log(`🔍 Finding referrer with code: ${c}`);let g=(0,r.P)((0,r.collection)(n.db,i.users),(0,r._M)(o.referralCode,"==",c),(0,r.AB)(1)),f=await (0,r.getDocs)(g);if(f.empty){console.log(`❌ Referral code not found: ${c}`),console.log("❌ No referrer found with this code in database");return}let y=f.docs[0],w=y.id,p=y.data(),m={Trial:0,Junior:300,Senior:700,Expert:1200,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[a]||0;if(console.log(`✅ Found referrer:`),console.log(`   - Referrer ID: ${w}`),console.log(`   - Referrer name: ${p[o.name]}`),console.log(`   - Bonus amount: ₹${m}`),console.log(`   - Plan: ${a}`),m>0){console.log(`💰 Processing bonus payment...`),await h(w,m),console.log(`✅ Added ₹${m} to referrer's wallet`);let t=(0,r.H9)(n.db,i.users,w);await (0,r.mZ)(t,{[o.totalTranslations]:(0,r.GV)(50)}),console.log(`✅ Added 50 bonus translations to referrer`);let s=(0,r.H9)(n.db,i.users,e);await (0,r.mZ)(s,{[o.referralBonusCredited]:!0}),console.log(`✅ Marked referral bonus as credited for user ${e}`),await u(w,{type:"referral_bonus",amount:m,description:`Referral bonus for ${a} plan upgrade + 50 bonus translations (User: ${l[o.name]})`}),console.log(`✅ Added transaction record for referral bonus`),console.log(`🎉 REFERRAL BONUS COMPLETED: ₹${m} + 50 translations for referrer ${w}`)}else console.log(`❌ No bonus amount calculated for plan: ${a}`),console.log(`❌ Available plans:`,["Trial","Junior","Senior","Expert"])}catch(e){console.error("❌ Error processing referral bonus:",e),console.error("❌ Error details:",e)}}async function k(e){try{console.log(`🔧 MANUAL REFERRAL BONUS: Processing for user ${e}`);let t=await s(e);if(!t)return console.log("❌ User not found"),{success:!1,message:"User not found"};if(console.log(`🔍 User data:`),console.log(`   - Name: ${t.name}`),console.log(`   - Plan: ${t.plan}`),console.log(`   - Referred by: ${t.referredBy||"None"}`),console.log(`   - Bonus credited: ${t.referralBonusCredited}`),"Trial"===t.plan)return{success:!1,message:"User is still on Trial plan. Upgrade to paid plan first."};if(!t.referredBy)return{success:!1,message:"User was not referred by anyone"};if(t.referralBonusCredited)return{success:!1,message:"Referral bonus already credited"};return await E(e,"Trial",t.plan),{success:!0,message:`Referral bonus processed for ${t.plan} plan`}}catch(e){return console.error("❌ Error in manual referral bonus processing:",e),{success:!1,message:`Error: ${e?.message||"Unknown error"}`}}}async function $(e){try{var t;let a=await s(e);if(!a)return{translationDuration:30,earningPerBatch:25,plan:"Trial",hasQuickAdvantage:!1};let r=!!(t=a).quickTranslationAdvantage&&!!t.quickTranslationAdvantageExpiry&&new Date<t.quickTranslationAdvantageExpiry,n=a.translationDuration;return r?n=a.quickTranslationAdvantageSeconds||30:n&&"Trial"!==a.plan||(n=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[a.plan]||30),{translationDuration:n,earningPerBatch:({Trial:25,Junior:150,Senior:250,Expert:400,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[a.plan]||25,plan:a.plan,hasQuickAdvantage:r,quickAdvantageExpiry:a.quickTranslationAdvantageExpiry}}catch(e){return console.error("Error getting user translation settings:",e),{translationDuration:30,earningPerBatch:25,plan:"Trial",hasQuickAdvantage:!1}}}async function S(e,t,r,n=30){try{if(t<=0||t>365)throw Error("Days must be between 1 and 365");if(n<1||n>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let{grantCopyPastePermission:o}=await Promise.resolve().then(a.bind(a,27878));return await o(e,t),console.log(`Granted copy-paste permission to user ${e} for ${t} days by ${r}`),await u(e,{type:"quick_advantage_granted",amount:0,description:`Copy-paste permission granted for ${t} days by ${r}`}),{success:!0}}catch(e){throw console.error("Error granting quick video advantage:",e),e}}async function x(e,t){try{let{removeCopyPastePermission:r}=await Promise.resolve().then(a.bind(a,27878));return await r(e),console.log(`Removed copy-paste permission from user ${e} by ${t}`),await u(e,{type:"quick_advantage_removed",amount:0,description:`Copy-paste permission removed by ${t}`}),{success:!0}}catch(e){throw console.error("Error removing quick video advantage:",e),e}}async function B(e){try{let t={title:e.title,message:e.message,type:e.type,targetUsers:e.targetUsers,userIds:e.userIds||[],createdAt:r.Dc.now(),createdBy:e.createdBy};console.log("Adding notification to Firestore:",t);let a=await (0,r.gS)((0,r.collection)(n.db,i.notifications),t);console.log("Notification added successfully with ID:",a.id);let o=await (0,r.x7)(a);return o.exists()?console.log("Notification verified in database:",o.data()):console.warn("Notification not found after adding"),a.id}catch(e){throw console.error("Error adding notification:",e),e}}async function N(e,t=20){try{let a,o;if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserNotifications:",e),[];console.log(`Loading notifications for user: ${e}`);try{let e=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.My)("createdAt","desc"),(0,r.AB)(t));a=await (0,r.getDocs)(e),console.log(`Found ${a.docs.length} notifications for all users`)}catch(o){console.warn("Error querying all users notifications, trying without orderBy:",o);let e=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.AB)(t));a=await (0,r.getDocs)(e)}try{let a=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.My)("createdAt","desc"),(0,r.AB)(t));o=await (0,r.getDocs)(a),console.log(`Found ${o.docs.length} notifications for specific user`)}catch(s){console.warn("Error querying specific user notifications, trying without orderBy:",s);let a=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.AB)(t));o=await (0,r.getDocs)(a)}let s=[];a.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),o.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),s.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime());let l=s.slice(0,t);return console.log(`Returning ${l.length} total notifications for user`),l}catch(e){return console.error("Error getting user notifications:",e),[]}}async function I(e=50){try{let t=(0,r.P)((0,r.collection)(n.db,i.notifications),(0,r.My)("createdAt","desc"),(0,r.AB)(e));return(await (0,r.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date}))}catch(e){return console.error("Error getting all notifications:",e),[]}}async function U(e){try{if(!e||"string"!=typeof e)throw Error("Invalid notification ID provided");console.log("Deleting notification:",e),await (0,r.kd)((0,r.H9)(n.db,i.notifications,e)),console.log("Notification deleted successfully")}catch(e){throw console.error("Error deleting notification:",e),e}}async function P(e,t){try{let a=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");a.includes(e)||(a.push(e),localStorage.setItem(`read_notifications_${t}`,JSON.stringify(a)))}catch(e){console.error("Error marking notification as read:",e)}}function q(e,t){try{return JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]").includes(e)}catch(e){return console.error("Error checking notification read status:",e),!1}}function _(e,t){try{let a=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");return e.filter(e=>!a.includes(e.id)).length}catch(e){return console.error("Error getting unread notification count:",e),0}}async function C(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUnreadNotifications:",e),[];console.log(`Loading unread notifications for user: ${e}`);let t=await N(e,50),a=JSON.parse(localStorage.getItem(`read_notifications_${e}`)||"[]"),r=t.filter(e=>e.id&&!a.includes(e.id));return console.log(`Found ${r.length} unread notifications`),r}catch(e){return console.error("Error getting unread notifications:",e),[]}}async function M(e){try{return(await C(e)).length>0}catch(e){return console.error("Error checking for unread notifications:",e),!1}}async function R(e){try{let t=(0,r.P)((0,r.collection)(n.db,i.withdrawals),(0,r._M)("userId","==",e),(0,r._M)("status","==","pending"),(0,r.AB)(1));return!(await (0,r.getDocs)(t)).empty}catch(e){return console.error("Error checking pending withdrawals:",e),!1}}async function H(e){try{let t=await s(e);if(!t)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===t.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};if(await R(e))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let r=new Date,n=r.getHours();if(n<10||n>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:o}=await a.e(7087).then(a.bind(a,87087));if(await o(r))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:i}=await a.e(7087).then(a.bind(a,87087));if(await i(e,r))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(e){return console.error("Error checking withdrawal allowed:",e),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function G(e,t,a){try{if(t<50)throw Error("Minimum withdrawal amount is ₹50");let o=await H(e);if(!o.allowed)throw Error(o.reason);if((await l(e)).wallet<t)throw Error("Insufficient wallet balance");await h(e,-t),await u(e,{type:"withdrawal_request",amount:-t,description:`Withdrawal request submitted - ₹${t} debited from wallet`});let s={userId:e,amount:t,bankDetails:a,status:"pending",date:r.Dc.now(),createdAt:r.Dc.now()};return(await (0,r.gS)((0,r.collection)(n.db,i.withdrawals),s)).id}catch(e){throw console.error("Error creating withdrawal request:",e),e}}async function L(e,t=20){try{let a=(0,r.P)((0,r.collection)(n.db,i.withdrawals),(0,r._M)("userId","==",e),(0,r.My)("date","desc"),(0,r.AB)(t));return(await (0,r.getDocs)(a)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}))}catch(e){return console.error("Error getting user withdrawals:",e),[]}}async function Z(){try{try{let e=(0,r.collection)(n.db,i.users),t=((await (0,r.d_)(e)).data().count+1).toString().padStart(4,"0");return`TN${t}`}catch(a){console.warn("Failed to get count from server, using fallback method:",a);let e=Date.now().toString().slice(-4),t=Math.random().toString(36).substring(2,4).toUpperCase();return`TN${e}${t}`}}catch(t){console.error("Error generating unique referral code:",t);let e=Date.now().toString().slice(-4);return`TN${e}`}}}};