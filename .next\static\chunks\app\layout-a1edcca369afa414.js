(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,7177,7718],{347:()=>{},1340:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var s=a(5155),r=a(2115);function o(){let[e,t]=(0,r.useState)(null),[a,o]=(0,r.useState)(!1);(0,r.useEffect)(()=>{"serviceWorker"in navigator&&navigator.serviceWorker.register("/sw.js").then(e=>{console.log("SW registered: ",e)}).catch(e=>{console.log("SW registration failed: ",e)});let e=e=>{e.preventDefault(),t(e),o(!0)};return window.addEventListener("beforeinstallprompt",e),window.matchMedia("(display-mode: standalone)").matches&&o(!1),()=>{window.removeEventListener("beforeinstallprompt",e)}},[]);let n=async()=>{if(!e)return;e.prompt();let{outcome:a}=await e.userChoice;"accepted"===a?console.log("User accepted the install prompt"):console.log("User dismissed the install prompt"),t(null),o(!1)};return a?(0,s.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,s.jsxs)("button",{onClick:n,className:"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Install App"]})}):null}},2439:(e,t,a)=>{"use strict";a.d(t,{S3:()=>p,i7:()=>c,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>l});var s=a(6104),r=a(5317);let o={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},n={users:"users"};class i{static async calculateActiveDays(e){try{var t,a;let o,c=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!c.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let l=c.data(),d=(null==(t=l.joinedDate)?void 0:t.toDate())||new Date,u=null==(a=l.lastActiveDayUpdate)?void 0:a.toDate(),p=l.activeDays||0,m=l.plan||"Trial",y=new Date,g=y.toDateString(),h=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(p)),console.log("   - Last update: ".concat(h||"Never")),console.log("   - Today: ".concat(g)),console.log("   - Plan: ".concat(m)),h===g)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:p,shouldUpdate:!1,isNewDay:!1};if("Admin"===m)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await i.updateLastActiveDayUpdate(e),{activeDays:p,shouldUpdate:!1,isNewDay:!0};if(await i.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await i.updateLastActiveDayUpdate(e),{activeDays:p,shouldUpdate:!1,isNewDay:!0};return o="Trial"===m?Math.floor((y.getTime()-d.getTime())/864e5)+1:p+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(p," → ").concat(o)),{activeDays:o,shouldUpdate:o!==p,isNewDay:!0}}catch(t){return console.error("Error calculating active days for user ".concat(e,":"),t),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let t=await i.calculateActiveDays(e);if(t.shouldUpdate){let a=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(a,{[o.activeDays]:t.activeDays,[o.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(t.activeDays))}else t.isNewDay&&await i.updateLastActiveDayUpdate(e);return t.activeDays}catch(t){throw console.error("Error updating active days for user ".concat(e,":"),t),t}}static async updateLastActiveDayUpdate(e){try{let t=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(t,{[o.lastActiveDayUpdate]:r.Dc.now()})}catch(t){console.error("Error updating last active day timestamp for user ".concat(e,":"),t)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:t}=await a.e(9567).then(a.bind(a,9567));return await t(e,new Date)}catch(t){return console.error("Error checking leave status for user ".concat(e,":"),t),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,r.getDocs)((0,r.collection)(s.db,n.users)),t=0,a=0,o=0;for(let s of e.docs)try{t++;let e=await i.calculateActiveDays(s.id);(e.shouldUpdate||e.isNewDay)&&(await i.updateUserActiveDays(s.id),e.shouldUpdate&&a++)}catch(e){o++,console.error("Error processing active days for user ".concat(s.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Updated: ".concat(a," users")),console.log("   - Errors: ".concat(o," users")),{processed:t,updated:a,errors:o}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!t.exists())return 0;return t.data().activeDays||0}catch(t){return console.error("Error getting active days for user ".concat(e,":"),t),0}}static async initializeActiveDaysForNewUser(e){try{let t=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(t,{[o.activeDays]:1,[o.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(t){throw console.error("Error initializing active days for user ".concat(e,":"),t),t}}}let c=i.calculateActiveDays,l=i.updateUserActiveDays,d=i.processAllUsersActiveDays,u=i.getUserActiveDays,p=i.initializeActiveDaysForNewUser},3711:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var s=a(2115),r=a(2439),o=a(7718);function n(){return(0,s.useEffect)(()=>{console.log("\uD83D\uDD04 DailyActiveDaysScheduler mounted - starting centralized scheduler...");let e=()=>{try{let e=localStorage.getItem("lastDailyTasksRun"),t=new Date().toDateString();if(!e||e!==t)return!0;return!1}catch(e){return console.error("Error checking daily tasks schedule:",e),!0}},t=()=>{try{let e=new Date().toDateString();localStorage.setItem("lastDailyTasksRun",e),console.log("✅ Daily tasks marked as completed for ".concat(e))}catch(e){console.error("Error marking daily tasks as completed:",e)}},a=async()=>{try{if(e()){console.log("\uD83C\uDF05 Running daily tasks..."),console.log("\uD83D\uDCC5 Processing active days increment...");let e=await (0,r.mH)();console.log("\uD83D\uDCCB Processing copy-paste reduction...");let a=await (0,o.Mk)();console.log("✅ Daily tasks completed successfully!"),console.log("\uD83D\uDCCA Active Days - Processed: ".concat(e.processed,", Updated: ").concat(e.updated)),console.log("\uD83D\uDCCA Copy-Paste - Processed: ".concat(a.processed,", Reduced: ").concat(a.reduced)),t()}else console.log("✅ Daily tasks already completed today")}catch(e){console.error("❌ Error running daily tasks:",e)}};a();let s=setInterval(()=>{console.log("⏰ Hourly check for daily tasks..."),a()},36e5);return()=>{console.log("Daily schedulers component unmounted"),clearInterval(s)}},[]),null}},5080:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var s=a(5155),r=a(2115);class o extends r.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"glass-card p-8 text-center max-w-md",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-red-400 text-4xl mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Something went wrong"}),(0,s.jsx)("p",{className:"text-white/80 mb-4",children:"An error occurred while loading this page. Please refresh and try again."}),(0,s.jsxs)("button",{onClick:()=>window.location.reload(),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-refresh mr-2"}),"Refresh Page"]})]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}let n=o},5170:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var s=a(5155),r=a(2115),o=a(6681);function n(e){let{children:t}=e,[a,n]=(0,r.useState)(!0),[i,c]=(0,r.useState)(!1),[l,d]=(0,r.useState)(null),{user:u}=(0,o.hD)();return((0,r.useEffect)(()=>{"undefined"!=typeof navigator&&n(navigator.onLine);let e=()=>{if(console.log("\uD83C\uDF10 Network: Back online"),n(!0),c(!1),d(null),l){let e=Math.round((Date.now()-l.getTime())/1e3);console.log("\uD83D\uDCF6 Reconnected after ".concat(e," seconds offline"))}},t=()=>{console.log("\uD83D\uDCF5 Network: Offline"),n(!1),d(new Date),setTimeout(()=>{navigator.onLine||c(!0)},2e3)};return window.addEventListener("online",e),window.addEventListener("offline",t),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t)}},[l]),i)?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full",children:(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center border border-white/20",children:[(0,s.jsx)("div",{className:"mx-auto w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)("i",{className:"fas fa-wifi-slash text-red-400 text-3xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"You're Offline"}),(0,s.jsx)("p",{className:"text-white/80 mb-6 leading-relaxed",children:"Your internet connection seems to be down. Don't worry - your work progress is safely saved locally and will be restored when you're back online."}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-white/60",children:"Connection Status:"}),(0,s.jsxs)("span",{className:"text-red-400 font-medium",children:[(0,s.jsx)("i",{className:"fas fa-circle text-xs mr-1"}),"Offline"]})]}),l&&(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,s.jsx)("span",{className:"text-white/60",children:"Offline Since:"}),(0,s.jsx)("span",{className:"text-white/80",children:l.toLocaleTimeString()})]}),u&&(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,s.jsx)("span",{className:"text-white/60",children:"User Session:"}),(0,s.jsxs)("span",{className:"text-green-400 font-medium",children:[(0,s.jsx)("i",{className:"fas fa-check text-xs mr-1"}),"Protected"]})]})]}),(0,s.jsxs)("div",{className:"text-left mb-6",children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-3 text-center",children:"Available Offline:"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80 text-sm",children:[(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"Continue current translation work"]}),(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"Auto-save progress locally"]}),(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"View completed translations"]}),(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-times text-red-400 mr-2"}),"Submit translations for earnings"]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Check Connection"]}),(0,s.jsx)("button",{onClick:()=>c(!1),className:"w-full bg-white/10 hover:bg-white/20 text-white font-semibold py-3 px-6 rounded-lg transition-colors border border-white/20",children:"Continue Offline"})]}),(0,s.jsx)("div",{className:"mt-6 p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20",children:(0,s.jsxs)("p",{className:"text-yellow-200 text-xs",children:[(0,s.jsx)("i",{className:"fas fa-lightbulb mr-1"}),(0,s.jsx)("strong",{children:"Tip:"})," Your work is automatically saved every 10 seconds. When you reconnect, everything will sync automatically."]})})]})})}):(0,s.jsxs)("div",{className:"relative",children:[!a&&(0,s.jsxs)("div",{className:"fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 text-sm z-50",children:[(0,s.jsx)("i",{className:"fas fa-wifi-slash mr-2"}),"You're offline - work is saved locally",(0,s.jsx)("button",{onClick:()=>c(!0),className:"ml-4 underline hover:no-underline",children:"View Details"})]}),(0,s.jsx)("div",{className:a?"":"pt-10",children:t})]})}},7320:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});var s=a(5155),r=a(2115),o=a(4752),n=a.n(o);function i(e){let{children:t}=e,[a,o]=(0,r.useState)(!1),[i,c]=(0,r.useState)(null),[l,d]=(0,r.useState)("");(0,r.useEffect)(()=>("serviceWorker"in navigator&&u(),window.addEventListener("beforeunload",h),()=>{window.removeEventListener("beforeunload",h)}),[]);let u=async()=>{try{let e=await navigator.serviceWorker.register("/sw.js",{scope:"/",updateViaCache:"none"});c(e),console.log("✅ Service Worker registered successfully"),e.update(),e.addEventListener("updatefound",()=>{let t=e.installing;t&&(console.log("\uD83D\uDD04 New service worker found, installing..."),t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&(console.log("✨ New service worker installed, update available"),o(!0),m())}))}),navigator.serviceWorker.addEventListener("message",e=>{e.data&&"SW_UPDATED"===e.data.type&&(console.log("\uD83D\uDCF1 Service worker updated to version:",e.data.version),d(e.data.version))}),e.waiting&&(o(!0),m()),p()}catch(e){console.error("❌ Service Worker registration failed:",e)}},p=async()=>{if("serviceWorker"in navigator&&navigator.serviceWorker.controller)try{let e=new MessageChannel;e.port1.onmessage=e=>{e.data&&e.data.version&&(d(e.data.version),console.log("\uD83D\uDCF1 Current app version:",e.data.version))},navigator.serviceWorker.controller.postMessage({type:"GET_VERSION"},[e.port2])}catch(e){console.error("Error getting version:",e)}},m=()=>{n().fire({icon:"info",title:"App Update Available!",text:"A new version of Instra Global is available. Update now for the latest features and improvements.",showCancelButton:!0,confirmButtonText:"Update Now",cancelButtonText:"Later",confirmButtonColor:"#3b82f6",cancelButtonColor:"#6b7280",allowOutsideClick:!1,allowEscapeKey:!1}).then(e=>{e.isConfirmed?g():setTimeout(()=>{a&&y()},18e5)})},y=()=>{n().fire({icon:"warning",title:"Update Reminder",text:"Please update the app to ensure optimal performance and security.",confirmButtonText:"Update Now",confirmButtonColor:"#3b82f6",timer:1e4,timerProgressBar:!0}).then(e=>{e.isConfirmed&&g()})},g=async()=>{if(!i||!i.waiting)return void console.log("No waiting service worker found");try{n().fire({title:"Updating App...",text:"Please wait while we update the app.",allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1,didOpen:()=>{n().showLoading()}}),i.waiting.postMessage({type:"SKIP_WAITING"}),navigator.serviceWorker.addEventListener("controllerchange",()=>{console.log("\uD83D\uDD04 New service worker took control, reloading..."),"caches"in window&&caches.keys().then(e=>{e.forEach(e=>{e.includes("instra-global")&&caches.delete(e)})}),window.location.reload()})}catch(e){console.error("Error applying update:",e),n().fire({icon:"error",title:"Update Failed",text:"Failed to update the app. Please refresh the page manually.",confirmButtonText:"Refresh Page"}).then(()=>{window.location.reload()})}},h=()=>{console.log("\uD83D\uDCBE Saving data before page unload")};return(0,r.useEffect)(()=>{let e=setInterval(()=>{i&&(console.log("\uD83D\uDD0D Checking for app updates..."),i.update())},18e5);return()=>clearInterval(e)},[i]),(0,s.jsxs)(s.Fragment,{children:[t,a&&(0,s.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,s.jsxs)("button",{onClick:g,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse",children:[(0,s.jsx)("i",{className:"fas fa-download"}),(0,s.jsx)("span",{children:"Update Available"})]})}),!1]})}},7718:(e,t,a)=>{"use strict";a.d(t,{Mk:()=>p,checkCopyPastePermission:()=>c,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var s=a(6104),r=a(5317);let o={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},n={users:"users"};class i{static async checkCopyPastePermission(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let a=t.data()[o.quickTranslationAdvantageExpiry];if(!a)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let i=a.toDate(),c=new Date,l=i>c,d=l?Math.ceil((i.getTime()-c.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:i}}catch(t){return console.error("Error checking copy-paste permission for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let a=new Date;a.setDate(a.getDate()+t);let i=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(i,{[o.quickTranslationAdvantageExpiry]:r.Dc.fromDate(a),[o.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(t," days (expires: ").concat(a.toDateString(),")"))}catch(t){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),t),t}}static async removeCopyPastePermission(e){try{let t=(0,r.H9)(s.db,n.users,e);await (0,r.mZ)(t,{[o.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(t){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,r.x7)((0,r.H9)(s.db,n.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let a=t.data(),i=a[o.quickTranslationAdvantageExpiry],c=a[o.lastCopyPasteReduction];if(!i)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((c?c.toDate().toDateString():null)===l){let e=i.toDate(),t=new Date,a=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:a,expired:0===a}}let d=i.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let p=(0,r.H9)(s.db,n.users,e);if(u<=new Date)return await (0,r.mZ)(p,{[o.quickTranslationAdvantageExpiry]:null,[o.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(p,{[o.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[o.lastCopyPasteReduction]:r.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(t," days remaining")),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error("Error reducing copy-paste days for user ".concat(e,":"),t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(s.db,n.users)),t=0,a=0,o=0,c=0;for(let s of e.docs)try{t++;let e=await i.reduceCopyPasteDays(s.id);e.reduced&&(a++,e.expired&&o++)}catch(e){c++,console.error("Error processing copy-paste reduction for user ".concat(s.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(t," users")),console.log("   - Reduced: ".concat(a," users")),console.log("   - Expired: ".concat(o," users")),console.log("   - Errors: ".concat(c," users")),{processed:t,reduced:a,expired:o,errors:c}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await i.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error("Error getting copy-paste status for user ".concat(e,":"),t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let c=i.checkCopyPastePermission,l=i.grantCopyPastePermission,d=i.removeCopyPastePermission,u=i.reduceCopyPasteDays,p=i.processAllUsersCopyPasteReduction;i.getCopyPasteStatus},8802:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,9398,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,3711)),Promise.resolve().then(a.bind(a,5080)),Promise.resolve().then(a.bind(a,5170)),Promise.resolve().then(a.bind(a,1340)),Promise.resolve().then(a.bind(a,7320))},9398:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_51684b",variable:"__variable_51684b"}}},e=>{var t=t=>e(e.s=t);e.O(0,[2756,2992,7416,8320,5181,6681,8441,1684,7358],()=>t(8802)),_N_E=e.O()}]);