(()=>{var e={};e.id=4559,e.ids=[4559],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3962:(e,s,r)=>{Promise.resolve().then(r.bind(r,80393))},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44130:(e,s,r)=>{Promise.resolve().then(r.bind(r,97955))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67028:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var t=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);r.d(s,c);let l={children:["",{children:["test-copy-paste-admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-admin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-copy-paste-admin/page",pathname:"/test-copy-paste-admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80393:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\test-copy-paste-admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\test-copy-paste-admin\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97955:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(60687),i=r(43210),n=r(87979),o=r(3582),a=r(27878);function c(){let{user:e,loading:s}=(0,n.Nu)(),[r,c]=(0,i.useState)(""),[l,d]=(0,i.useState)(1),[u,p]=(0,i.useState)(null),[x,m]=(0,i.useState)(!1),[h,g]=(0,i.useState)(null),b=async()=>{if(!r.trim())return void alert("Please enter a user ID");m(!0),p(null);try{console.log(`🔧 Granting copy-paste permission to user ${r} for ${l} days`),await (0,o.w1)(r.trim(),l,e?.email||"admin",30);let s=await (0,a.checkCopyPastePermission)(r.trim());g(s),p({success:!0,message:`Copy-paste permission granted for ${l} days`,action:"granted"})}catch(e){p({success:!1,message:`Error: ${e?.message||"Unknown error"}`,action:"grant_failed"})}finally{m(!1)}},v=async()=>{if(!r.trim())return void alert("Please enter a user ID");m(!0),p(null);try{console.log(`🔧 Removing copy-paste permission from user ${r}`),await (0,o.wT)(r.trim(),e?.email||"admin");let s=await (0,a.checkCopyPastePermission)(r.trim());g(s),p({success:!0,message:"Copy-paste permission removed",action:"removed"})}catch(e){p({success:!1,message:`Error: ${e?.message||"Unknown error"}`,action:"remove_failed"})}finally{m(!1)}},y=async()=>{if(!r.trim())return void alert("Please enter a user ID");m(!0);try{let e=await (0,a.checkCopyPastePermission)(r.trim());g(e),p({success:!0,message:"Status checked successfully",action:"status_checked"})}catch(e){p({success:!1,message:`Error: ${e?.message||"Unknown error"}`,action:"status_failed"})}finally{m(!1)}};return s?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,t.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,t.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Copy-Paste Admin Functions"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white mb-2",children:"User ID:"}),(0,t.jsx)("input",{type:"text",value:r,onChange:e=>c(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white mb-2",children:"Days to Grant:"}),(0,t.jsx)("input",{type:"number",value:l,onChange:e=>d(parseInt(e.target.value)||1),min:"1",max:"365",className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)("button",{onClick:b,disabled:x,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:x?"Processing...":"Grant Permission"}),(0,t.jsx)("button",{onClick:v,disabled:x,className:"flex-1 bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:x?"Processing...":"Remove Permission"}),(0,t.jsx)("button",{onClick:y,disabled:x,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:x?"Checking...":"Check Status"})]}),h&&(0,t.jsxs)("div",{className:"p-4 bg-white/10 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-white font-bold mb-2",children:"Current Permission Status:"}),(0,t.jsxs)("div",{className:"text-white space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Has Permission:"})," ",h.hasPermission?"Yes":"No"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Days Remaining:"})," ",h.daysRemaining]}),h.expiryDate&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Expires:"})," ",new Date(h.expiryDate).toLocaleDateString()]})]})]}),u&&(0,t.jsxs)("div",{className:`p-4 rounded-lg ${u.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"}`,children:[(0,t.jsx)("h3",{className:`font-bold ${u.success?"text-green-400":"text-red-400"}`,children:u.success?"Success!":"Failed"}),(0,t.jsx)("p",{className:"text-white mt-2",children:u.message}),(0,t.jsxs)("p",{className:"text-white/70 text-sm mt-1",children:["Action: ",u.action]})]})]}),(0,t.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,t.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Enter a valid user ID"}),(0,t.jsx)("li",{children:'• Use "Grant Permission" to enable copy-paste'}),(0,t.jsx)("li",{children:'• Use "Remove Permission" to disable copy-paste'}),(0,t.jsx)("li",{children:'• Use "Check Status" to see current permission state'}),(0,t.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[2579,6803,3582],()=>r(67028));module.exports=t})();