(()=>{var e={};e.id=4700,e.ids=[4700],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11451:(e,s,r)=>{Promise.resolve().then(r.bind(r,90322))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85316:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\reset-password\\page.tsx","default")},85348:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(65239),i=r(48088),o=r(88170),a=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(s,l);let d={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85316)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\reset-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\reset-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},90322:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(60687),i=r(43210),o=r(85814),a=r.n(o),n=r(30474),l=r(63385),d=r(33784),c=r(87979),u=r(77567);function p(){let{user:e,loading:s}=(0,c.hD)(),[r,o]=(0,i.useState)(""),[p,x]=(0,i.useState)(""),[h,m]=(0,i.useState)(""),[f,w]=(0,i.useState)(""),[b,v]=(0,i.useState)(!1),[j,g]=(0,i.useState)(!0),[N,q]=(0,i.useState)(!1),[P,y]=(0,i.useState)(!1),[k,S]=(0,i.useState)(!1),[C,_]=(0,i.useState)(!1),A=async e=>{if(e.preventDefault(),!h.trim())return void u.A.fire({icon:"error",title:"Password Required",text:"Please enter a new password"});if(h.length<6)return void u.A.fire({icon:"error",title:"Password Too Short",text:"Password must be at least 6 characters long"});if(h!==f)return void u.A.fire({icon:"error",title:"Passwords Don't Match",text:"Please make sure both passwords match"});v(!0);try{await (0,l.R4)(d.j2,r,h),_(!0),u.A.fire({icon:"success",title:"Password Reset Successful!",text:"Your password has been updated successfully. You can now login with your new password.",confirmButtonText:"Go to Login",confirmButtonColor:"#3b82f6"}).then(()=>{window.location.href="/login"})}catch(s){console.error("Password reset error:",s);let e="An error occurred while resetting your password";switch(s.code){case"auth/expired-action-code":e="This password reset link has expired. Please request a new one.";break;case"auth/invalid-action-code":e="This password reset link is invalid. Please request a new one.";break;case"auth/weak-password":e="Password is too weak. Please choose a stronger password.";break;default:e=s.message||"Failed to reset password"}u.A.fire({icon:"error",title:"Reset Failed",text:e})}finally{v(!1)}};return s||j?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:j?"Verifying reset link...":"Loading..."})]})}):N?(0,t.jsx)("main",{className:"min-h-screen flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)(a(),{href:"/",className:"inline-block",children:(0,t.jsx)(n.default,{src:"/logo.png",alt:"MyTube",width:120,height:120,className:"mx-auto mb-4"})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Set New Password"}),(0,t.jsxs)("p",{className:"text-white/80",children:["Enter your new password for ",(0,t.jsx)("span",{className:"font-semibold text-blue-400",children:p})]})]}),(0,t.jsxs)("form",{onSubmit:A,className:"glass-card p-8 space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"newPassword",className:"block text-white font-medium mb-2",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,t.jsx)("input",{type:P?"text":"password",id:"newPassword",value:h,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Enter new password",disabled:b}),(0,t.jsx)("button",{type:"button",onClick:()=>y(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,t.jsx)("i",{className:`fas ${P?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,t.jsx)("input",{type:k?"text":"password",id:"confirmPassword",value:f,onChange:e=>w(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Confirm new password",disabled:b}),(0,t.jsx)("button",{type:"button",onClick:()=>S(!k),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,t.jsx)("i",{className:`fas ${k?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:b,className:"w-full btn-primary flex items-center justify-center",children:b?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Updating Password..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-check mr-2"}),"Update Password"]})})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)(a(),{href:"/login",className:"text-white/80 hover:text-white transition-colors flex items-center justify-center",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Login"]})})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("i",{className:"fas fa-times text-red-400 text-2xl"})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Invalid Reset Link"}),(0,t.jsx)("p",{className:"text-white/80 mb-6",children:"This password reset link is invalid or has expired."}),(0,t.jsx)(a(),{href:"/forgot-password",className:"btn-primary",children:"Request New Reset Link"})]})})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97379:(e,s,r)=>{Promise.resolve().then(r.bind(r,85316))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[2579,6803],()=>r(85348));module.exports=t})();