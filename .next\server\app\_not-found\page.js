"use strict";(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={643:e=>{e.exports=require("node:perf_hooks")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{e.exports=require("node:zlib")},16698:e=>{e.exports=require("node:async_hooks")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{e.exports=require("node:http2")},33873:e=>{e.exports=require("path")},34589:e=>{e.exports=require("node:assert")},34631:e=>{e.exports=require("tls")},37067:e=>{e.exports=require("node:http")},37366:e=>{e.exports=require("dns")},37540:e=>{e.exports=require("node:console")},41204:e=>{e.exports=require("string_decoder")},41692:e=>{e.exports=require("node:tls")},41792:e=>{e.exports=require("node:querystring")},42840:(e,r,o)=>{o.r(r),o.d(r,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>a,routeModule:()=>l,tree:()=>u});var t=o(65239),s=o(48088),n=o(88170),i=o.n(n),p=o(30893),d={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>p[e]);o.d(r,d);let u={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(o.bind(o,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(o.bind(o,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(o.bind(o,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,a=[],x={require:o,loadChunk:()=>Promise.resolve()},l=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{e.exports=require("node:url")},73429:e=>{e.exports=require("node:util/types")},73496:e=>{e.exports=require("http2")},74075:e=>{e.exports=require("zlib")},75919:e=>{e.exports=require("node:worker_threads")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[2579,6803],()=>o(42840));module.exports=t})();