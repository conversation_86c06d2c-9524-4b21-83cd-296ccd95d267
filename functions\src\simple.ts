import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin (only if not already initialized)
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

/**
 * Simple test function to verify deployment works
 */
export const testFunction = onCall(async (request) => {
  try {
    // Verify authentication
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    console.log('Test function called by user:', request.auth.uid);

    return {
      success: true,
      message: 'Firebase Functions are working!',
      timestamp: new Date().toISOString(),
      userId: request.auth.uid
    };
  } catch (error) {
    console.error('Error in test function:', error);
    throw new HttpsError('internal', 'Test function failed');
  }
});

/**
 * Simple function to get user basic data with minimal reads
 */
export const getUserBasicData = onCall(async (request) => {
  try {
    // Verify authentication
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    const userId = request.auth.uid;
    console.log('Getting basic data for user:', userId);

    // Single read to get user data
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      throw new HttpsError('not-found', 'User not found');
    }

    const userData = userDoc.data()!;

    const result = {
      name: userData.name || 'Unknown',
      email: userData.email || 'Unknown',
      plan: userData.plan || 'Trial',
      wallet: userData.wallet || 0,
      totalTranslations: userData.totalTranslations || 0,
      todayTranslations: userData.todayTranslations || 0,
      timestamp: new Date().toISOString()
    };

    console.log('Basic data retrieved for user:', userId);
    return result;
  } catch (error) {
    console.error('Error getting user basic data:', error);
    if (error instanceof HttpsError) {
      throw error;
    }
    throw new HttpsError('internal', 'Failed to get user basic data');
  }
});
