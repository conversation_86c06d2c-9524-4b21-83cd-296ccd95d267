(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[773],{3489:(e,s,a)=>{Promise.resolve().then(a.bind(a,5067))},5067:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>m});var t=a(5155),l=a(2115),i=a(6874),r=a.n(i),n=a(6681),c=a(6779),o=a(4752),d=a.n(o);function m(){let{user:e,loading:s,isAdmin:i}=(0,n.wC)(),[o,m]=(0,l.useState)(!1),[x,h]=(0,l.useState)(null),[p,u]=(0,l.useState)(null),[f,j]=(0,l.useState)([]),[y,w]=(0,l.useState)(!1),b=async()=>{if(p)try{m(!0);let e=(await p.text()).split("\n").filter(e=>e.trim());if(e.length<2)throw Error("CSV file must have at least a header row and one data row");let s=e[0],a=s.includes("	")?"	":",",t=s.split(a).map(e=>e.trim().replace(/"/g,"").toLowerCase());if(["email","totaltranslations","walletbalance","activedays"].filter(e=>!t.some(s=>s.includes(e.replace("totaltranslations","translations").replace("walletbalance","wallet").replace("activedays","active")))).length>0)throw Error("Missing required columns. Expected: email, totalTranslations (or translations), walletBalance (or wallet), activeDays (or active)");let l=e.slice(1).map((e,s)=>{let l=e.split(a).map(e=>e.trim().replace(/"/g,"")),i={};t.forEach((e,s)=>{i[e]=l[s]||""});let r=i.email||"",n=parseInt(i.totaltranslations||i.translations||i.totalTranslations||"0")||0,c=parseFloat(i.walletbalance||i.wallet||i.walletBalance||"0")||0,o=parseInt(i.activedays||i.active||i.activeDays||"0")||0,d=parseInt(i.copypastedays||i.copypaste||i.copyPasteDays||"0")||0;if(!r)throw Error("Row ".concat(s+2,": Email is required"));if(!r.includes("@"))throw Error("Row ".concat(s+2,": Invalid email format"));return{email:r,totalTranslations:n,walletBalance:c,activeDays:o,copyPasteDays:d}});j(l.slice(0,10)),w(!0)}catch(e){console.error("Error previewing file:",e),d().fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{m(!1)}},v=async()=>{if(p&&(await d().fire({icon:"question",title:"Confirm Data Upload",html:'\n        <div class="text-left">\n          <p><strong>Are you sure you want to update user data from this file?</strong></p>\n          <br>\n          <p>This will:</p>\n          <ul>\n            <li>Find users by email address</li>\n            <li>Add to their existing total translations count</li>\n            <li>Add to their existing wallet balance</li>\n            <li>SET their active days to the specified value</li>\n            <li>Skip users not found in the system</li>\n          </ul>\n          <br>\n          <p class="text-yellow-600"><strong>Note:</strong> Translations and wallet will be ADDED, but active days will be SET (replaced)!</p>\n        </div>\n      ',showCancelButton:!0,confirmButtonText:"Yes, Update Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{m(!0),h(null),d().fire({title:"Updating Users",html:'\n          <div class="text-center">\n            <div class="spinner mx-auto mb-4"></div>\n            <p>Processing user updates...</p>\n            <p class="text-sm text-gray-600 mt-2">Please wait...</p>\n          </div>\n        ',allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1});let e=(await p.text()).split("\n").filter(e=>e.trim()),s=e[0],t=s.includes("	")?"	":",",l=s.split(t).map(e=>e.trim().replace(/"/g,"").toLowerCase()),i=e.slice(1).map(e=>{let s=e.split(t).map(e=>e.trim().replace(/"/g,"")),a={};return l.forEach((e,t)=>{a[e]=s[t]||""}),{email:a.email||"",totalTranslations:parseInt(a.totaltranslations||a.translations||a.totalTranslations||"0")||0,walletBalance:parseFloat(a.walletbalance||a.wallet||a.walletBalance||"0")||0,activeDays:parseInt(a.activedays||a.active||a.activeDays||"0")||0,copyPasteDays:parseInt(a.copypastedays||a.copypaste||a.copyPasteDays||"0")||0}}).filter(e=>e.email),r=0,n=0,o=0,x=[];for(let e of i)try{let s=(await (0,c.searchUsers)(e.email)).find(s=>{var a;return(null==(a=s.email)?void 0:a.toLowerCase())===e.email.toLowerCase()});if(!s){o++,x.push("User not found: ".concat(e.email));continue}let{getWalletData:t,getVideoCountData:l}=await Promise.resolve().then(a.bind(a,3592)),[i,n]=await Promise.all([t(s.id),l(s.id)]),d={totalTranslations:(n.totalTranslations||0)+e.totalTranslations,wallet:(i.wallet||0)+e.walletBalance,activeDays:e.activeDays};if(await (0,c.TK)(s.id,d),e.copyPasteDays>0){let{grantCopyPastePermission:t}=await a.e(7718).then(a.bind(a,7718));await t(s.id,e.copyPasteDays),console.log("✅ Granted ".concat(e.copyPasteDays," copy-paste days to ").concat(e.email))}r++}catch(s){n++,x.push("Failed to update ".concat(e.email,": ").concat(s.message))}d().close();let u={success:r,failed:n,errors:x,notFound:o};h(u),d().fire({icon:r>0?n>0||o>0?"warning":"success":"error",title:"Update Complete",html:'\n          <div class="text-left">\n            <p><strong>Update Summary:</strong></p>\n            <ul>\n              <li class="text-green-600">✓ Successfully updated: '.concat(r,' users</li>\n              <li class="text-yellow-600">⚠ Not found: ').concat(o,' users</li>\n              <li class="text-red-600">✗ Failed: ').concat(n," users</li>\n            </ul>\n            ").concat(x.length>0?"<br><p><strong>First 5 errors:</strong></p><ul>".concat(x.slice(0,5).map(e=>'<li class="text-red-600 text-sm">'.concat(e,"</li>")).join(""),"</ul>"):"","\n          </div>\n        "),timer:n>0?void 0:5e3,showConfirmButton:n>0})}catch(e){console.error("Error updating users:",e),d().fire({icon:"error",title:"Update Failed",text:e.message||"Failed to update users. Please try again."})}finally{m(!1)}};return s?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"spinner"})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Simple User Update"}),(0,t.jsx)("p",{className:"text-white/80",children:"Update user translations, wallet balance, and active days via CSV"})]}),(0,t.jsxs)(r(),{href:"/admin/users",className:"btn-secondary",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload CSV File"]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample File"}),(0,t.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalTranslations,walletBalance,activeDays,copyPasteDays\<EMAIL>,100,500,15,7\<EMAIL>,250,1200,25,14\<EMAIL>,75,300,5,0"],{type:"text/csv"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="simple-upload-sample.csv",a.click(),URL.revokeObjectURL(s)},className:"btn-secondary text-sm",children:[(0,t.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select CSV File"}),(0,t.jsx)("input",{type:"file",accept:".csv,.txt",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];a&&(u(a),j([]),w(!1),h(null))},className:"form-input"})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)("button",{onClick:b,disabled:!p||o,className:"btn-secondary",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,t.jsx)("button",{onClick:v,disabled:!p||o||!y,className:"btn-primary",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Updating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-upload mr-2"}),"Update Users"]})})]})})]}),y&&f.length>0&&(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 10 Records)"]}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full text-white",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-white/20",children:[(0,t.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Add Translations"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Add Wallet (₹)"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Set Active Days"}),(0,t.jsx)("th",{className:"text-left p-2",children:"Copy-Paste Days"})]})}),(0,t.jsx)("tbody",{children:f.map((e,s)=>(0,t.jsxs)("tr",{className:"border-b border-white/10",children:[(0,t.jsx)("td",{className:"p-2",children:e.email}),(0,t.jsxs)("td",{className:"p-2",children:["+",e.totalTranslations]}),(0,t.jsxs)("td",{className:"p-2",children:["+₹",e.walletBalance]}),(0,t.jsx)("td",{className:"p-2",children:e.activeDays}),(0,t.jsx)("td",{className:"p-2",children:e.copyPasteDays>0?"".concat(e.copyPasteDays," days"):"None"})]},s))})]})})]}),x&&(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Update Results"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:x.success}),(0,t.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Updated"})]}),(0,t.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:x.notFound}),(0,t.jsx)("div",{className:"text-yellow-300 text-sm",children:"Users Not Found"})]}),(0,t.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:x.failed}),(0,t.jsx)("div",{className:"text-red-300 text-sm",children:"Failed Updates"})]})]}),x.errors.length>0&&(0,t.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,t.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[x.errors.slice(0,10).map((e,s)=>(0,t.jsxs)("li",{children:["• ",e]},s)),x.errors.length>10&&(0,t.jsxs)("li",{className:"text-red-400",children:["... and ",x.errors.length-10," more errors"]})]})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Instructions"]}),(0,t.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"CSV Format:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"email:"})," User's email address (must exist in system)"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"totalTranslations:"})," Number of translations to ADD to current count"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"walletBalance:"})," Amount to ADD to current wallet balance"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"activeDays:"})," Active days to SET (replace current value)"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"copyPasteDays:"})," Copy-paste permission days to GRANT (0 = no permission)"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsx)("li",{children:"Translations and wallet values are ADDED to existing data"}),(0,t.jsx)("li",{children:"Active days value REPLACES the current active days"}),(0,t.jsx)("li",{children:"Users must already exist in the system"}),(0,t.jsx)("li",{children:"Email addresses are case-insensitive"}),(0,t.jsx)("li",{children:"Use comma or tab as delimiter"}),(0,t.jsx)("li",{children:"First row must be headers: email,totalTranslations,walletBalance,activeDays,copyPasteDays"})]})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,6681,3592,6779,8441,1684,7358],()=>s(3489)),_N_E=e.O()}]);