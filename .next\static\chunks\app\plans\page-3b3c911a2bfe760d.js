(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[481],{452:(e,a,t)=>{Promise.resolve().then(t.bind(t,4871))},4871:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>p});var s=t(5155),n=t(2115),r=t(6874),i=t.n(r),l=t(6681),o=t(4752),c=t.n(o);let d=[{id:"trial",name:"Trial",price:0,duration:2,earningPerTranslation:25,languages:1,description:"Explore and test your translation potential before handling real jobs",features:["2 days access","₹25 per 50 translations","Basic support","Learn the platform"]},{id:"junior",name:"Junior",price:2999,duration:30,earningPerTranslation:150,languages:1,description:"Entry-level role for new freelancers starting their translation journey",features:["30 days access","Certified for 1 Language","₹150 per 50 translations","Basic support","Translation training materials"]},{id:"senior",name:"Senior",price:5999,duration:30,earningPerTranslation:250,languages:3,description:"Senior role for experienced translators handling multiple Languages",features:["30 days access","Certified for 3 Languages","₹250 per 50 translations","Priority support","Advanced translation tools","Quality assurance training"],popular:!0},{id:"expert",name:"Expert",price:9999,duration:30,earningPerTranslation:400,languages:5,description:"Top-tier role for expert translators with broad multi-language proficiency",features:["30 days access","Certified for 5 Languages","₹400 per 50 translations","VIP support","Premium translation tools","Dedicated account manager","Exclusive high-value projects"]}];function p(){let{user:e,loading:a}=(0,l.hD)(),[t,r]=(0,n.useState)(null),[o,p]=(0,n.useState)(!1),x=async a=>{if(!e)return void c().fire({icon:"info",title:"Login Required",text:"Please login to purchase a plan",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&(window.location.href="/login")});if("trial"===a.id)return void c().fire({icon:"info",title:"Trial Plan",text:"You are already on the trial plan. Upgrade to a paid plan for better earnings!"});r(a.id),p(!0);try{await new Promise(e=>setTimeout(e,2e3)),c().fire({icon:"info",title:"Payment Integration Required",html:"\n          <p>To complete your purchase of the <strong>".concat(a.name,"</strong> plan (₹").concat(a.price,'), please contact our support team.</p>\n          <br>\n          <p><strong>Plan Details:</strong></p>\n          <ul style="text-align: left; margin: 10px 0;">\n            <li>Duration: ').concat(a.duration," days</li>\n            <li>Earning: ₹").concat(a.earningPerTranslation," per 50 translations</li>\n            <li>Languages: Certified for ").concat(a.languages," language").concat(a.languages>1?"s":"","</li>\n          </ul>\n          <br>\n          <p><strong>Contact Options:</strong></p>\n          <p>\uD83D\uDCE7 Email: <strong><EMAIL></strong></p>\n        "),confirmButtonText:"Contact Support",showCancelButton:!0,cancelButtonText:"Cancel"})}catch(e){console.error("Error processing plan selection:",e),c().fire({icon:"error",title:"Error",text:"Failed to process plan selection. Please try again."})}finally{p(!1),r(null)}};return a?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsxs)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(i(),{href:e?"/dashboard":"/",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Choose Your Plan"}),(0,s.jsx)("div",{className:"w-20"})," "]})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Start Earning with Instra Global"}),(0,s.jsx)("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Choose the perfect plan for your translation career. Translate text and earn money with our professional certification levels."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:d.map(e=>(0,s.jsxs)("div",{className:"glass-card p-6 relative ".concat(e.popular?"ring-2 ring-yellow-400":""),children:[e.popular&&(0,s.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:(0,s.jsx)("span",{className:"bg-yellow-400 text-black px-3 py-1 rounded-full text-xs font-bold",children:"Most Popular"})}),(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:e.name}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("span",{className:"text-3xl font-bold text-white",children:["₹",e.price]}),e.price>0&&(0,s.jsxs)("span",{className:"text-white/60 ml-2",children:["/ ",e.duration," days"]})]}),(0,s.jsxs)("p",{className:"text-green-400 font-semibold text-sm",children:["Earn ₹",e.earningPerTranslation," per 50 translations"]}),(0,s.jsx)("p",{className:"text-white/60 text-xs mt-2",children:e.description})]}),(0,s.jsx)("ul",{className:"space-y-2 mb-6 text-sm",children:e.features.map((e,a)=>(0,s.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,s.jsx)("i",{className:"fas fa-check text-green-400 mr-2 text-xs"}),e]},a))}),(0,s.jsx)("button",{onClick:()=>x(e),disabled:o&&t===e.id,className:"w-full py-2 rounded-lg font-semibold transition-all duration-300 text-sm ".concat(e.popular?"bg-yellow-400 text-black hover:bg-yellow-500":0===e.price?"bg-gray-600 text-white hover:bg-gray-700":"bg-purple-600 text-white hover:bg-purple-700"," disabled:opacity-50"),children:o&&t===e.id?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Processing..."]}):0===e.price?"Start Free Trial":"Choose ".concat(e.name)})]},e.id))}),(0,s.jsxs)("div",{className:"mt-12 glass-card p-8",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Translation Plan Benefits"]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Earning Structure"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,s.jsx)("li",{children:"• Complete 50 translations daily to earn the full amount"}),(0,s.jsx)("li",{children:"• Each translation must be accurate and complete"}),(0,s.jsx)("li",{children:"• Earnings are credited to your wallet"}),(0,s.jsx)("li",{children:"• Higher plans offer better earning rates"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Language Certification"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,s.jsx)("li",{children:"• Higher plans certify you for more languages"}),(0,s.jsx)("li",{children:"• Access to specialized translation projects"}),(0,s.jsx)("li",{children:"• Professional translator recognition"}),(0,s.jsx)("li",{children:"• Quality assurance and training included"})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 text-center",children:[(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"Need help choosing a plan? Contact us during business hours (9 AM - 6 PM, working days):"}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center text-white hover:text-blue-400 transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-envelope mr-2"}),"<EMAIL>"]})})]})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,6681,8441,1684,7358],()=>a(452)),_N_E=e.O()}]);