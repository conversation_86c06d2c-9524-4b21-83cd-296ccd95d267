"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./src/app/wallet/page.tsx":
/*!*********************************!*\
  !*** ./src/app/wallet/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WalletPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useBlockingNotifications */ \"(app-pages-browser)/./src/hooks/useBlockingNotifications.ts\");\n/* harmony import */ var _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useLeaveMonitor */ \"(app-pages-browser)/./src/hooks/useLeaveMonitor.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n/* harmony import */ var _components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/BlockingNotificationModal */ \"(app-pages-browser)/./src/components/BlockingNotificationModal.tsx\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_8__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth)();\n    const { hasBlockingNotifications, isChecking, markAllAsRead } = (0,_hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications)((user === null || user === void 0 ? void 0 : user.uid) || null);\n    const { isBlocked: isLeaveBlocked, leaveStatus } = (0,_hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor)({\n        userId: (user === null || user === void 0 ? void 0 : user.uid) || null,\n        checkInterval: 30000,\n        enabled: !!user\n    });\n    const [walletData, setWalletData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dataLoading, setDataLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWithdrawing, setIsWithdrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastWithdrawalAttempt, setLastWithdrawalAttempt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [bufferCountdown, setBufferCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Bank details state\n    const [bankDetails, setBankDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBankForm, setShowBankForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bankFormData, setBankFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        accountHolderName: '',\n        accountNumber: '',\n        ifscCode: '',\n        bankName: ''\n    });\n    const [isSavingBank, setIsSavingBank] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [withdrawalAllowed, setWithdrawalAllowed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        allowed: true\n    });\n    const [checkingWithdrawal, setCheckingWithdrawal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            if (user) {\n                loadWalletData();\n                loadBankDetails();\n                loadUserData();\n                checkWithdrawalEligibility();\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        user\n    ]);\n    // Buffer countdown timer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            let interval = null;\n            if (bufferCountdown > 0) {\n                interval = setInterval({\n                    \"WalletPage.useEffect\": ()=>{\n                        setBufferCountdown({\n                            \"WalletPage.useEffect\": (prev)=>{\n                                if (prev <= 1) {\n                                    return 0;\n                                }\n                                return prev - 1;\n                            }\n                        }[\"WalletPage.useEffect\"]);\n                    }\n                }[\"WalletPage.useEffect\"], 1000);\n            }\n            return ({\n                \"WalletPage.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"WalletPage.useEffect\"];\n        }\n    }[\"WalletPage.useEffect\"], [\n        bufferCountdown\n    ]);\n    // Monitor leave status changes and update withdrawal eligibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            if (isLeaveBlocked) {\n                setWithdrawalAllowed({\n                    allowed: false,\n                    reason: leaveStatus.reason || 'Withdrawals are not available due to leave.'\n                });\n            } else if (user) {\n                // Re-check withdrawal eligibility when leave status changes\n                checkWithdrawalEligibility();\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        isLeaveBlocked,\n        leaveStatus,\n        user\n    ]);\n    const loadUserData = async ()=>{\n        try {\n            const data = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserData)(user.uid);\n            setUserData(data);\n        } catch (error) {\n            console.error('Error loading user data:', error);\n        }\n    };\n    const checkWithdrawalEligibility = async ()=>{\n        if (!user) return;\n        try {\n            setCheckingWithdrawal(true);\n            const result = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.checkWithdrawalAllowed)(user.uid);\n            setWithdrawalAllowed(result);\n        } catch (error) {\n            console.error('Error checking withdrawal eligibility:', error);\n            setWithdrawalAllowed({\n                allowed: false,\n                reason: 'Unable to verify withdrawal eligibility. Please try again.'\n            });\n        } finally{\n            setCheckingWithdrawal(false);\n        }\n    };\n    const loadWalletData = async ()=>{\n        try {\n            setDataLoading(true);\n            const [walletResult, withdrawalsResult] = await Promise.all([\n                (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getWalletData)(user.uid),\n                (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserWithdrawals)(user.uid, 20)\n            ]);\n            setWalletData(walletResult);\n            // Convert withdrawals to transaction format for display\n            const withdrawalTransactions = withdrawalsResult.map((withdrawal)=>({\n                    id: withdrawal.id,\n                    type: 'withdrawal',\n                    amount: -withdrawal.amount,\n                    description: \"Withdrawal request - ₹\".concat(withdrawal.amount),\n                    date: withdrawal.date,\n                    status: withdrawal.status\n                }));\n            setTransactions(withdrawalTransactions);\n        } catch (error) {\n            console.error('Error loading wallet data:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Error',\n                text: 'Failed to load wallet data. Please try again.'\n            });\n        } finally{\n            setDataLoading(false);\n        }\n    };\n    const loadBankDetails = async ()=>{\n        try {\n            const bankData = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getBankDetails)(user.uid);\n            setBankDetails(bankData);\n            if (bankData) {\n                setBankFormData(bankData);\n            }\n        } catch (error) {\n            console.error('Error loading bank details:', error);\n        }\n    };\n    const handleBankFormSubmit = async (e)=>{\n        e.preventDefault();\n        // Prevent multiple submissions\n        if (isSavingBank) return;\n        try {\n            setIsSavingBank(true);\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.saveBankDetails)(user.uid, bankFormData);\n            setBankDetails(bankFormData);\n            setShowBankForm(false);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'success',\n                title: 'Bank Details Saved',\n                text: 'Your bank details have been saved successfully',\n                timer: 2000,\n                showConfirmButton: false\n            });\n        } catch (error) {\n            console.error('Error saving bank details:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Error',\n                text: error.message || 'Failed to save bank details. Please try again.'\n            });\n        } finally{\n            setIsSavingBank(false);\n        }\n    };\n    const handleBankFormChange = (e)=>{\n        const { name, value } = e.target;\n        setBankFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleWithdraw = async ()=>{\n        const now = Date.now();\n        const WITHDRAWAL_BUFFER_TIME = 3000 // 3 seconds buffer between attempts\n        ;\n        // Prevent multiple clicks with buffer time\n        if (isWithdrawing) {\n            console.log('⚠️ Withdrawal already in progress, ignoring click');\n            return;\n        }\n        // Check buffer time to prevent rapid successive clicks\n        if (now - lastWithdrawalAttempt < WITHDRAWAL_BUFFER_TIME) {\n            const remainingTime = Math.ceil((WITHDRAWAL_BUFFER_TIME - (now - lastWithdrawalAttempt)) / 1000);\n            setBufferCountdown(remainingTime);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'warning',\n                title: 'Please Wait',\n                text: \"Please wait \".concat(remainingTime, \" more second(s) before attempting another withdrawal.\"),\n                timer: 2000,\n                showConfirmButton: false\n            });\n            return;\n        }\n        // Update last attempt timestamp and start buffer countdown\n        setLastWithdrawalAttempt(now);\n        setBufferCountdown(3) // Start 3-second countdown\n        ;\n        // Check if withdrawals are blocked due to leave\n        if (isLeaveBlocked) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'warning',\n                title: 'Withdrawal Not Available',\n                text: leaveStatus.reason || 'Withdrawals are not available due to leave.',\n                confirmButtonText: 'OK'\n            });\n            return;\n        }\n        const amount = parseFloat(withdrawAmount);\n        if (!amount || amount <= 0) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Invalid Amount',\n                text: 'Please enter a valid amount to withdraw'\n            });\n            return;\n        }\n        if (amount < 50) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Minimum Withdrawal',\n                text: 'Minimum withdrawal amount is ₹50'\n            });\n            return;\n        }\n        if (amount > ((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0)) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Insufficient Balance',\n                text: 'You do not have enough balance in your wallet'\n            });\n            return;\n        }\n        if (!bankDetails) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'warning',\n                title: 'Bank Details Required',\n                text: 'Please add your bank details before making a withdrawal'\n            });\n            return;\n        }\n        try {\n            setIsWithdrawing(true);\n            // Create withdrawal request in Firestore\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.createWithdrawalRequest)(user.uid, amount, bankDetails);\n            // Reload wallet data to show updated transactions\n            await loadWalletData();\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'success',\n                title: 'Withdrawal Request Submitted',\n                text: \"Your withdrawal request for ₹\".concat(amount, \" has been submitted and will be processed within 24-48 hours.\")\n            });\n            setWithdrawAmount('');\n        } catch (error) {\n            console.error('Error processing withdrawal:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Withdrawal Failed',\n                text: error.message || 'Failed to process withdrawal request. Please try again.'\n            });\n        } finally{\n            setIsWithdrawing(false);\n        }\n    };\n    const formatCurrency = (amount)=>{\n        if (amount === undefined || amount === null || isNaN(amount)) {\n            return '₹0.00';\n        }\n        return \"₹\".concat(amount.toFixed(2));\n    };\n    if (loading || dataLoading || isChecking) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading wallet...'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n            lineNumber: 326,\n            columnNumber: 7\n        }, this);\n    }\n    // Show blocking notifications if any exist\n    if (hasBlockingNotifications && user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            userId: user.uid,\n            onAllRead: markAllAsRead\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n            lineNumber: 340,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"glass-card p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/dashboard\",\n                            className: \"glass-button px-4 py-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-arrow-left mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"My Wallet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadWalletData,\n                            className: \"glass-button px-4 py-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-sync-alt mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-semibold text-white\",\n                                children: \"My Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-wallet text-green-400 text-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-4xl font-bold text-green-400 mb-2\",\n                        children: formatCurrency((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60\",\n                        children: \"Total available balance\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-university mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this),\n                            bankDetails && !showBankForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-edit mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this),\n                    !bankDetails && !showBankForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-university text-white/30 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-4\",\n                                children: \"No bank details added yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-plus mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this) : showBankForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleBankFormSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Account Holder Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"accountHolderName\",\n                                                value: bankFormData.accountHolderName,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter account holder name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Bank Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"bankName\",\n                                                value: bankFormData.bankName,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter bank name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Account Number *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"accountNumber\",\n                                                value: bankFormData.accountNumber,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter account number\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"IFSC Code *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"ifscCode\",\n                                                value: bankFormData.ifscCode,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter IFSC code (e.g., SBIN0001234)\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSavingBank,\n                                        className: \"\".concat(isSavingBank ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-primary hover:bg-blue-600'),\n                                        children: isSavingBank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Saving Bank Details...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-save mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Save Bank Details\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowBankForm(false),\n                                        className: \"btn-secondary\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/10 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Account Holder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.accountHolderName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Bank Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.bankName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Account Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: [\n                                                \"****\",\n                                                bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.accountNumber.slice(-4)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"IFSC Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.ifscCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-money-bill-wave mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this),\n                            \"Withdraw Funds\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, this),\n                    (userData === null || userData === void 0 ? void 0 : userData.plan) === 'Trial' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-lock text-red-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-400 font-medium\",\n                                        children: \"Trial Plan Restriction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm mb-3\",\n                                children: \"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/plans\",\n                                    className: \"btn-primary inline-block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-arrow-up mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upgrade Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-clock text-blue-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 font-medium\",\n                                        children: \"Withdrawal Timings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm mb-2\",\n                                children: [\n                                    \"Withdrawals are only allowed between \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"10:00 AM to 6:00 PM\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 50\n                                    }, this),\n                                    \" on non-leave days.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-xs\",\n                                        children: [\n                                            \"Current time: \",\n                                            new Date().toLocaleTimeString(),\n                                            \" | Status: \",\n                                            withdrawalAllowed.allowed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-medium\",\n                                                children: \"✓ Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400 font-medium\",\n                                                children: \"✗ Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: checkWithdrawalEligibility,\n                                        disabled: checkingWithdrawal,\n                                        className: \"text-blue-400 hover:text-blue-300 text-xs\",\n                                        children: checkingWithdrawal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"spinner w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-sync-alt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 11\n                            }, this),\n                            !withdrawalAllowed.allowed && withdrawalAllowed.reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-400 text-sm mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-exclamation-triangle mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    withdrawalAllowed.reason\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 9\n                    }, this),\n                    !bankDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-4\",\n                                children: \"Please add your bank details before making a withdrawal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-university mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: withdrawAmount,\n                                        onChange: (e)=>setWithdrawAmount(e.target.value),\n                                        placeholder: \"Enter amount to withdraw (Min: ₹50)\",\n                                        className: \"form-input flex-1\",\n                                        min: \"50\",\n                                        max: (walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleWithdraw,\n                                        disabled: isWithdrawing || bufferCountdown > 0 || !withdrawAmount || !withdrawalAllowed.allowed,\n                                        onKeyDown: (e)=>{\n                                            // Prevent keyboard interactions when disabled or processing\n                                            if (isWithdrawing || bufferCountdown > 0 || !withdrawAmount || !withdrawalAllowed.allowed) {\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            }\n                                        },\n                                        className: \"whitespace-nowrap transition-all duration-200 \".concat(isWithdrawing || bufferCountdown > 0 ? 'btn-disabled cursor-not-allowed opacity-50 pointer-events-none' : withdrawalAllowed.allowed && withdrawAmount ? 'btn-primary hover:bg-blue-600 active:scale-95' : 'btn-disabled cursor-not-allowed opacity-50'),\n                                        style: {\n                                            pointerEvents: isWithdrawing || bufferCountdown > 0 ? 'none' : 'auto'\n                                        },\n                                        children: isWithdrawing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Processing Withdrawal...\"\n                                            ]\n                                        }, void 0, true) : bufferCountdown > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-clock mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Please wait \",\n                                                bufferCountdown,\n                                                \"s...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-download mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Withdraw ₹\",\n                                                withdrawAmount || '0'\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 text-sm mt-2\",\n                                children: [\n                                    \"Available: \",\n                                    formatCurrency((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0),\n                                    \" | Minimum: ₹50\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 523,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-money-bill-wave mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Withdrawal History\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadWalletData,\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-sync-alt mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, this),\n                    transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-money-bill-wave text-white/30 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-2\",\n                                children: \"No withdrawal requests yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/40 text-sm\",\n                                children: \"Your withdrawal requests will appear here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: transactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white/10 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-money-bill-wave text-red-400 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: transaction.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: [\n                                                            transaction.date.toLocaleDateString(),\n                                                            \" at \",\n                                                            transaction.date.toLocaleTimeString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-bold text-red-400\",\n                                                children: formatCurrency(Math.abs(transaction.amount))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs px-2 py-1 rounded-full \".concat(transaction.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' : transaction.status === 'approved' ? 'bg-green-500/20 text-green-400' : transaction.status === 'rejected' ? 'bg-red-500/20 text-red-400' : transaction.status === 'completed' ? 'bg-blue-500/20 text-blue-400' : 'bg-gray-500/20 text-gray-400'),\n                                                children: transaction.status === 'pending' ? '⏳ Pending' : transaction.status === 'approved' ? '✅ Approved' : transaction.status === 'rejected' ? '❌ Rejected' : transaction.status === 'completed' ? '✅ Completed' : transaction.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, transaction.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 658,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"BfOyfpeXcxF6CwmzPphFYRFo5Qk=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth,\n        _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications,\n        _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wallet/page.tsx\n"));

/***/ })

});