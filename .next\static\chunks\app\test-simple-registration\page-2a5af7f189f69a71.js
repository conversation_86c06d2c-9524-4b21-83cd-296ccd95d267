(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5094,6104],{323:(e,t,s)=>{Promise.resolve().then(s.bind(s,6424))},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>o,j2:()=>c});var a=s(3915),i=s(3004),l=s(5317),n=s(858);let r=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),c=(0,i.xI)(r),o=(0,l.aU)(r);(0,n.c7)(r)},6424:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(5155),i=s(2115),l=s(3004),n=s(5317),r=s(6104);function c(){let[e,t]=(0,i.useState)(""),[s,c]=(0,i.useState)(!1),o=e=>{t(t=>t+e+"\n"),console.log(e)},m=async()=>{c(!0),t("");try{o("\uD83E\uDDEA Testing Simple Registration...");let e="simple-test-".concat(Date.now(),"@example.com");o("Step 1: Creating Firebase Auth user...");let t=(await (0,l.eJ)(r.j2,e,"test123456")).user;o("✅ User created: ".concat(t.uid)),o("Step 2: Creating simple Firestore document...");let s={name:"Simple Test User",email:e,plan:"Trial",wallet:0,created:new Date().toISOString()},a=(0,n.H9)(r.db,"users",t.uid);await (0,n.BN)(a,s),o("✅ Document created successfully"),o("Step 3: Verifying document...");let i=await (0,n.x7)(a);i.exists()?(o("✅ Document verified successfully"),o("Data: ".concat(JSON.stringify(i.data(),null,2)))):o("❌ Document not found"),o("Step 4: Cleaning up..."),await t.delete(),o("✅ Test user deleted"),o("\n\uD83C\uDF89 Simple registration test completed successfully!")}catch(e){o("❌ Error: ".concat(e.message)),o("❌ Code: ".concat(e.code)),o("❌ Full error: ".concat(JSON.stringify(e,null,2)))}finally{c(!1)}};return(0,a.jsx)("div",{className:"min-h-screen p-4",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"\uD83E\uDDEA Simple Registration Test"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"This test uses simple field names to check if the issue is with complex field mapping."}),(0,a.jsx)("button",{onClick:m,disabled:s,className:"btn-primary mb-6",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Testing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-test-tube mr-2"}),"Test Simple Registration"]})}),e&&(0,a.jsxs)("div",{className:"bg-black/50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Test Results:"}),(0,a.jsx)("pre",{className:"text-green-400 text-sm whitespace-pre-wrap font-mono overflow-x-auto",children:e})]})]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,5181,8441,1684,7358],()=>t(323)),_N_E=e.O()}]);