'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useRequireAuth } from '@/hooks/useAuth'
import { useBlockingNotifications } from '@/hooks/useBlockingNotifications'
import { useLeaveMonitor } from '@/hooks/useLeaveMonitor'
import { getVideoCountData as getTranslationCountData, updateVideoCount as updateTranslationCount, addTransaction, updateWalletBalance, getUserVideoSettings as getUserTranslationSettings, getUserData } from '@/lib/dataService'
import { ActiveDaysService } from '@/lib/activeDaysService'
import { isWorkBlocked } from '@/lib/leaveService'
import {
  initializeWorkPageData,
  getRandomTargetLanguage,
  AVAILABLE_LANGUAGES
} from '@/lib/translationManager'
import { checkCopyPastePermission } from '@/lib/copyPasteService'
import { sessionManager, WorkProgress } from '@/lib/sessionManager'
import BlockingNotificationModal from '@/components/BlockingNotificationModal'
import SessionRecovery from '@/components/SessionRecovery'
import Swal from 'sweetalert2'

interface TranslationItem {
  english: string
  hindi?: string
  spanish?: string
  french?: string
  german?: string
  italian?: string
  portuguese?: string
  russian?: string
  arabic?: string
  chinese?: string
  japanese?: string
  korean?: string
  turkish?: string
  dutch?: string
  swedish?: string
  polish?: string
  ukrainian?: string
  greek?: string
  hebrew?: string
  vietnamese?: string
  thai?: string
}

interface TranslationStep {
  id: string
  englishText: string
  targetLanguage: string
  targetLanguageName: string
  targetTranslation: string
  userTypedText: string
  selectedLanguage: string
  isTypingComplete: boolean
  isLanguageSelected: boolean
  isConverted: boolean
  isSubmitted: boolean
}



export default function WorkPage() {
  const { user, loading } = useRequireAuth()
  const { hasBlockingNotifications, isChecking, markAllAsRead } = useBlockingNotifications(user?.uid || null)
  const { isBlocked: isLeaveBlocked, leaveStatus } = useLeaveMonitor({
    userId: user?.uid || null,
    checkInterval: 30000,
    enabled: !!user
  })

  // Debug logging
  console.log('WorkPage render:', {
    user: user?.uid,
    loading,
    hasBlockingNotifications,
    isChecking,
    isLeaveBlocked
  })
  
  // New Translation Workflow State
  const [currentStep, setCurrentStep] = useState<TranslationStep | null>(null)
  const [todayTranslations, setTodayTranslations] = useState(0)
  const [totalTranslationsCompleted, setTotalTranslationsCompleted] = useState(0)
  const [localTranslationCount, setLocalTranslationCount] = useState(0)
  const [canSubmitBatch, setCanSubmitBatch] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [translationItems, setTranslationItems] = useState<TranslationItem[]>([])
  const [isLoadingTranslations, setIsLoadingTranslations] = useState(true)

  // Typing validation state
  const [userTypedText, setUserTypedText] = useState('')
  const [previousValidText, setPreviousValidText] = useState('') // Track previous valid state for paste reversion
  const [typingErrors, setTypingErrors] = useState<number[]>([])
  const [isTypingComplete, setIsTypingComplete] = useState(false)
  const [hasQuickAdvantage, setHasQuickAdvantage] = useState(false)
  const [copyPasteDaysRemaining, setCopyPasteDaysRemaining] = useState(0)

  // Language selection state
  const [selectedLanguage, setSelectedLanguage] = useState('')
  const [isLanguageCorrect, setIsLanguageCorrect] = useState(false)
  const [showTranslation, setShowTranslation] = useState(false)

  // Paste detection state
  const [lastInputTime, setLastInputTime] = useState(0)
  const [pasteDetected, setPasteDetected] = useState(false)
  const [lastInputLength, setLastInputLength] = useState(0)
  const [typingBlocked, setTypingBlocked] = useState(false)
  const [blockReason, setBlockReason] = useState('')

  // Auto-save state
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [isAutoSaving, setIsAutoSaving] = useState(false)

  // Session recovery state
  const [showSessionRecovery, setShowSessionRecovery] = useState(false)
  const [sessionRecoveryComplete, setSessionRecoveryComplete] = useState(false)

  // Daily completion state
  const [isDailyCompleted, setIsDailyCompleted] = useState(false)
  const [showFinalSubmit, setShowFinalSubmit] = useState(false)

  const [translationSettings, setTranslationSettings] = useState({
    earningPerBatch: 25, // Fixed: Trial should be ₹25 per 50 translations
    plan: 'Trial'
  })
  const [userData, setUserData] = useState<any>(null)
  const [daysLeft, setDaysLeft] = useState(0)
  const [activeDays, setActiveDays] = useState(0)

  useEffect(() => {
    if (user && !sessionRecoveryComplete) {
      // Only show session recovery if we haven't completed it yet
      setShowSessionRecovery(true)

      // Add fallback timeout to prevent indefinite blocking
      const fallbackTimeout = setTimeout(() => {
        console.log('Session recovery timeout - proceeding without recovery')
        setSessionRecoveryComplete(true)
        setShowSessionRecovery(false)
      }, 15000) // 15 second timeout

      return () => clearTimeout(fallbackTimeout)
    } else if (user && sessionRecoveryComplete) {
      // If user is logged in and recovery is complete, proceed with work access
      checkWorkAccess()
    }
  }, [user, sessionRecoveryComplete])

  const handleSessionRecoveryComplete = () => {
    setSessionRecoveryComplete(true)
    setShowSessionRecovery(false)
    // Don't call checkWorkAccess here - it will be called by the useEffect above
  }

  const handleProgressRestored = (progress: WorkProgress) => {
    // Restore the work state from recovered progress
    setCurrentStep(progress.currentStep)
    setUserTypedText(progress.userTypedText)
    setPreviousValidText(progress.userTypedText) // Set previous valid text to current restored text
    setSelectedLanguage(progress.selectedLanguage)
    setIsTypingComplete(progress.isTypingComplete)
    setLocalTranslationCount(progress.completedTranslations)
    setLastSaved(new Date(progress.lastSaved))
  }

  useEffect(() => {
    setCanSubmitBatch(localTranslationCount >= 50)
    setShowFinalSubmit(localTranslationCount >= 50 && !isDailyCompleted)
  }, [localTranslationCount, isDailyCompleted])

  // Auto-save work progress every 10 seconds
  useEffect(() => {
    if (!user || !currentStep) return

    const autoSaveInterval = setInterval(() => {
      saveWorkProgress()
    }, 10000) // Save every 10 seconds

    return () => clearInterval(autoSaveInterval)
  }, [user, currentStep, userTypedText, selectedLanguage, isTypingComplete, localTranslationCount])

  // Save progress when user types
  useEffect(() => {
    if (user && currentStep && userTypedText) {
      const debounceTimer = setTimeout(() => {
        saveWorkProgress()
      }, 2000) // Save 2 seconds after user stops typing

      return () => clearTimeout(debounceTimer)
    }
  }, [userTypedText])

  // Save progress before page unload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (user && currentStep && (userTypedText || localTranslationCount > 0)) {
        saveWorkProgress()
        e.preventDefault()
        e.returnValue = 'You have unsaved work progress. Are you sure you want to leave?'
        return e.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [user, currentStep, userTypedText, localTranslationCount])

  // Listen for real-time copy-paste permission changes
  useEffect(() => {
    if (!user?.uid) return

    const handleCopyPastePermissionChange = (event: CustomEvent) => {
      const { userId, hasPermission, timestamp, updatedBy } = event.detail

      if (userId === user.uid) {
        console.log(`📡 Received copy-paste permission update: ${hasPermission} (by ${updatedBy})`)

        setHasQuickAdvantage(hasPermission)

        // Show notification to user
        Swal.fire({
          icon: hasPermission ? 'success' : 'warning',
          title: hasPermission ? 'Copy-Paste Enabled!' : 'Copy-Paste Disabled!',
          text: hasPermission
            ? 'Copy-paste permission has been enabled by admin. You can now copy and paste text.'
            : 'Copy-paste permission has been disabled by admin. Please type text manually.',
          timer: 4000,
          showConfirmButton: true,
          confirmButtonText: 'OK'
        })
      }
    }

    // Check localStorage for updates (in case event was missed)
    const checkForUpdates = () => {
      try {
        const updateKey = `copyPasteUpdate_${user.uid}`
        const updateData = localStorage.getItem(updateKey)

        if (updateData) {
          const parsed = JSON.parse(updateData)
          const timeDiff = Date.now() - parsed.timestamp

          // If update is less than 30 seconds old, apply it
          if (timeDiff < 30000) {
            console.log(`📡 Found recent copy-paste update in localStorage: ${parsed.hasPermission}`)

            setHasQuickAdvantage(parsed.hasPermission)

            // Show notification
            Swal.fire({
              icon: parsed.hasPermission ? 'success' : 'warning',
              title: parsed.hasPermission ? 'Copy-Paste Enabled!' : 'Copy-Paste Disabled!',
              text: parsed.hasPermission
                ? 'Copy-paste permission has been enabled by admin.'
                : 'Copy-paste permission has been disabled by admin.',
              timer: 3000,
              showConfirmButton: false
            })

            // Clear the update after applying
            localStorage.removeItem(updateKey)
          }
        }
      } catch (error) {
        console.error('Error checking for copy-paste updates:', error)
      }
    }

    // Listen for custom events
    window.addEventListener('copyPastePermissionChanged', handleCopyPastePermissionChange as EventListener)

    // Check for updates immediately and every 5 seconds
    checkForUpdates()
    const interval = setInterval(checkForUpdates, 5000)

    return () => {
      window.removeEventListener('copyPastePermissionChanged', handleCopyPastePermissionChange as EventListener)
      clearInterval(interval)
    }
  }, [user?.uid])

  const checkWorkAccess = async () => {
    try {
      console.log('🔍 Checking work access for user:', user!.uid)

      // Check plan expiry first using centralized service
      const planStatus = await ActiveDaysService.isUserPlanExpired(user!.uid)
      console.log('📅 Plan status result:', planStatus)

      if (planStatus.expired) {
        console.log('🚫 Work access blocked - Plan expired:', planStatus.reason)
        Swal.fire({
          icon: 'error',
          title: 'Plan Expired',
          html: `
            <div class="text-center">
              <p class="mb-3">${planStatus.reason}</p>
              <p class="text-sm text-gray-600">
                Active Days: ${planStatus.activeDays || 0} | Days Left: ${planStatus.daysLeft || 0}
              </p>
            </div>
          `,
          confirmButtonText: 'Upgrade Plan',
          showCancelButton: true,
          cancelButtonText: 'Go to Dashboard'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = '/plans'
          } else {
            window.location.href = '/dashboard'
          }
        })
        return
      }

      // Check if user has already completed their daily session (50 translations)
      const translationData = await getTranslationCountData(user!.uid)
      console.log('📊 Translation data check:', translationData)

      if (translationData.todayTranslations >= 50) {
        console.log('🚫 Work access blocked - Daily session completed')
        setIsDailyCompleted(true)
        Swal.fire({
          icon: 'info',
          title: 'Daily Session Completed',
          html: `
            <div class="text-center">
              <p class="mb-3">You have already completed your daily session of 50 translations!</p>
              <p class="text-sm text-gray-600">
                Translations completed today: ${translationData.todayTranslations}/50
              </p>
              <p class="text-sm text-green-600 mt-2">
                Come back tomorrow for your next session.
              </p>
            </div>
          `,
          confirmButtonText: 'Go to Dashboard',
          allowOutsideClick: false,
          allowEscapeKey: false
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }

      const workStatus = await isWorkBlocked(user!.uid)
      console.log('📊 Work status result:', workStatus)

      if (workStatus.blocked) {
        console.log('🚫 Work access blocked:', workStatus.reason)
        Swal.fire({
          icon: 'warning',
          title: 'Work Not Available',
          text: workStatus.reason || 'Work is currently blocked.',
          confirmButtonText: 'Go to Dashboard'
        }).then(() => {
          window.location.href = '/dashboard'
        })
        return
      }

      console.log('✅ Work access allowed, proceeding with fast loading')
      await initializeWorkPageFast()
    } catch (error) {
      console.error('❌ Error checking work access (allowing work to proceed):', error)
      await initializeWorkPageFast()
    }
  }

  const loadTranslationData = async () => {
    try {
      console.log('📊 Loading translation data for user:', user!.uid)
      const data = await getTranslationCountData(user!.uid)
      console.log('📊 Translation data loaded:', data)
      setTodayTranslations(data.todayTranslations)
      setTotalTranslationsCompleted(data.totalTranslations)
    } catch (error) {
      console.error('Error loading translation data:', error)
    }
  }

  const loadTranslationSettings = async () => {
    try {
      const settings = await getUserTranslationSettings(user!.uid)
      setTranslationSettings({
        earningPerBatch: settings.earningPerBatch,
        plan: settings.plan
      })

      // Check copy-paste permission using new service
      const copyPastePermission = await checkCopyPastePermission(user!.uid)
      setHasQuickAdvantage(copyPastePermission.hasPermission)
      setCopyPasteDaysRemaining(copyPastePermission.daysRemaining)

      console.log('Copy-paste permission status:', {
        hasPermission: copyPastePermission.hasPermission,
        daysRemaining: copyPastePermission.daysRemaining,
        expiryDate: copyPastePermission.expiryDate
      })
    } catch (error) {
      console.error('Error loading translation settings:', error)
    }
  }

  // Fast initialization - loads all data in one go
  const initializeWorkPageFast = async () => {
    try {
      setIsLoadingTranslations(true)

      console.log('🚀 Fast loading work page data...')
      const workData = await initializeWorkPageData(user!.uid)

      if (!workData.canWork) {
        console.log('🚫 User cannot work today')
        setIsDailyCompleted(true)
        return
      }

      // Set all data from cache
      setTranslationSettings({
        earningPerBatch: workData.userSettings.earningPerBatch,
        plan: workData.userSettings.plan
      })

      setHasQuickAdvantage(workData.userSettings.hasQuickAdvantage)
      setCopyPasteDaysRemaining(workData.userSettings.copyPasteDaysRemaining)

      setTodayTranslations(workData.todayProgress.todayTranslations)
      setTotalTranslationsCompleted(workData.todayProgress.totalTranslations)
      setActiveDays(workData.todayProgress.activeDays || 0)

      // Convert translations to work format
      const translationItems: TranslationItem[] = workData.translations.map((item: any) => ({
        english: item.english,
        hindi: item.hindi,
        spanish: item.spanish,
        french: item.french,
        german: item.german,
        italian: item.italian,
        portuguese: item.portuguese,
        russian: item.russian,
        arabic: item.arabic,
        chinese: item.chinese,
        japanese: item.japanese,
        korean: item.korean,
        turkish: item.turkish,
        dutch: item.dutch,
        swedish: item.swedish,
        polish: item.polish,
        ukrainian: item.ukrainian,
        greek: item.greek,
        hebrew: item.hebrew,
        vietnamese: item.vietnamese,
        thai: item.thai
      }))

      setTranslationItems(translationItems)

      // Initialize session
      initializeSession()

      // Generate first translation step if no restored progress
      if (!currentStep) {
        generateNewTranslationStep(translationItems)
      }

      console.log('✅ Fast initialization complete!')
    } catch (error) {
      console.error('❌ Error in fast initialization:', error)
      // Fallback to old method
      loadTranslationData()
      loadTranslationSettings()
      loadUserData()
      initializeTranslations()
    } finally {
      setIsLoadingTranslations(false)
    }
  }

  const loadUserData = async () => {
    try {
      const data = await getUserData(user!.uid)
      setUserData(data)

      if (data) {
        // Note: Active days are now handled by centralized scheduler
        // Individual calls removed to prevent duplicate increments

        const planStatus = await ActiveDaysService.isUserPlanExpired(user!.uid)
        setDaysLeft(planStatus.daysLeft || 0)
        setActiveDays(planStatus.activeDays || 0)

        console.log('📊 Plan status loaded:', {
          plan: data.plan,
          expired: planStatus.expired,
          daysLeft: planStatus.daysLeft,
          activeDays: planStatus.activeDays,
          reason: planStatus.reason
        })
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    }
  }

  const saveWorkProgress = async () => {
    if (!user || !currentStep) return

    try {
      setIsAutoSaving(true)

      const progress: WorkProgress = {
        currentStep,
        userTypedText,
        selectedLanguage,
        isTypingComplete,
        completedTranslations: localTranslationCount,
        batchProgress: (localTranslationCount / 50) * 100,
        lastSaved: new Date(),
        userId: user.uid
      }

      // Save to session manager
      sessionManager.saveWorkProgress(progress)

      // Also save to localStorage as backup
      const progressKey = `work_progress_backup_${user.uid}`
      localStorage.setItem(progressKey, JSON.stringify(progress))

      setLastSaved(new Date())
      console.log('✅ Work progress saved successfully')
    } catch (error) {
      console.error('❌ Error saving work progress:', error)
    } finally {
      setIsAutoSaving(false)
    }
  }

  const restoreWorkProgress = () => { // Legacy function - now handled in fast initialization
    if (!user) return

    try {
      // Try to restore from session manager first
      let savedProgress = sessionManager.getWorkProgress()

      // If not found, try localStorage backup
      if (!savedProgress) {
        const progressKey = `work_progress_backup_${user.uid}`
        const backupData = localStorage.getItem(progressKey)
        if (backupData) {
          savedProgress = JSON.parse(backupData)
        }
      }

      if (savedProgress && savedProgress.userId === user.uid) {
        // Check if the saved progress is from today
        const today = new Date().toDateString()
        const savedDate = new Date(savedProgress.lastSaved).toDateString()

        if (savedDate === today) {
          console.log('🔄 Restoring work progress from:', savedProgress.lastSaved)

          // Restore state
          setCurrentStep(savedProgress.currentStep)
          setUserTypedText(savedProgress.userTypedText)
          setSelectedLanguage(savedProgress.selectedLanguage)
          setIsTypingComplete(savedProgress.isTypingComplete)
          setLocalTranslationCount(savedProgress.completedTranslations)
          setLastSaved(new Date(savedProgress.lastSaved))

          // Show restoration notification
          Swal.fire({
            icon: 'info',
            title: 'Work Progress Restored',
            text: `Your previous work session has been restored. Progress: ${savedProgress.completedTranslations}/50 translations.`,
            timer: 3000,
            showConfirmButton: false
          })

          return true // Progress was restored
        } else {
          console.log('🗑️ Clearing old work progress from:', savedDate)
          sessionManager.clearWorkProgress()
          const progressKey = `work_progress_backup_${user.uid}`
          localStorage.removeItem(progressKey)
        }
      }
    } catch (error) {
      console.error('❌ Error restoring work progress:', error)
    }

    return false // No progress was restored
  }

  const initializeSession = () => {
    const today = new Date().toDateString()
    const sessionKey = `translation_session_${user!.uid}_${today}`

    const savedCount = localStorage.getItem(sessionKey)

    if (savedCount) {
      const count = parseInt(savedCount)
      setLocalTranslationCount(count)
    }
  }

  const initializeTranslations = async () => {
    try {
      setIsLoadingTranslations(true)

      // Check if we have restored progress
      const hasRestoredProgress = currentStep !== null

      // Initialize translation system with batching
      const { initializeTranslationSystem } = await import('@/lib/translationManager')
      const currentBatchTranslations = await initializeTranslationSystem()

      // Convert TranslationData to TranslationItem format
      const translationItems: TranslationItem[] = currentBatchTranslations.map((item: any) => ({
        english: item.english,
        hindi: item.hindi,
        spanish: item.spanish,
        french: item.french,
        german: item.german,
        italian: item.italian,
        portuguese: item.portuguese,
        russian: item.russian,
        arabic: item.arabic,
        chinese: item.chinese,
        japanese: item.japanese,
        korean: item.korean,
        turkish: item.turkish,
        dutch: item.dutch,
        swedish: item.swedish,
        polish: item.polish,
        ukrainian: item.ukrainian,
        greek: item.greek,
        hebrew: item.hebrew,
        vietnamese: item.vietnamese,
        thai: item.thai
      }))

      setTranslationItems(translationItems)

      // Only generate new step if we don't have restored progress
      if (!hasRestoredProgress) {
        generateNewTranslationStep(translationItems)
      } else {
        console.log('🔄 Using restored translation step')
      }

    } catch (error) {
      console.error('Error loading translations:', error)
      Swal.fire({
        icon: 'error',
        title: 'Loading Error',
        text: 'Failed to load translation data. Please refresh the page.',
      })
    } finally {
      setIsLoadingTranslations(false)
    }
  }

  // Reset current step (for blocked users)
  const resetCurrentStep = () => {
    setUserTypedText('')
    setPreviousValidText('') // Clear previous valid text
    setTypingErrors([])
    setIsTypingComplete(false)
    setSelectedLanguage('')
    setIsLanguageCorrect(false)
    setShowTranslation(false)
    setPasteDetected(false)
    setTypingBlocked(false)
    setBlockReason('')
    setLastInputTime(0)
    setLastInputLength(0)

    Swal.fire({
      icon: 'info',
      title: 'Reset Complete!',
      text: 'You can now start typing again. Please type carefully.',
      timer: 2000,
      showConfirmButton: false
    })
  }

  // Clear only typing errors (for correction)
  const clearTypingErrors = () => {
    setTypingErrors([])
    setTypingBlocked(false)
    setBlockReason('')
  }

  // Generate a new translation step
  const generateNewTranslationStep = (items: TranslationItem[] = translationItems) => {
    if (items.length === 0) return

    const randomIndex = Math.floor(Math.random() * items.length)
    const selectedItem = items[randomIndex]
    const randomTargetLang = getRandomTargetLanguage()
    const targetLangData = AVAILABLE_LANGUAGES.find(lang => lang.code === randomTargetLang)

    const newStep: TranslationStep = {
      id: `step_${Date.now()}_${Math.random()}`,
      englishText: selectedItem.english,
      targetLanguage: randomTargetLang,
      targetLanguageName: targetLangData?.name || 'Unknown',
      targetTranslation: selectedItem[randomTargetLang as keyof TranslationItem] || 'Translation not available',
      userTypedText: '',
      selectedLanguage: '',
      isTypingComplete: false,
      isLanguageSelected: false,
      isConverted: false,
      isSubmitted: false
    }

    setCurrentStep(newStep)
    setUserTypedText('')
    setPreviousValidText('') // Reset previous valid text for new step
    setTypingErrors([])
    setIsTypingComplete(false)
    setSelectedLanguage('')
    setIsLanguageCorrect(false)
    setShowTranslation(false)
    setPasteDetected(false)
    setTypingBlocked(false)
    setBlockReason('')
  }

  // Typing validation with paste detection and smart error handling
  const handleTextInput = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!currentStep || isTypingComplete) return

    const newValue = e.target.value
    const currentTime = Date.now()

    // Initialize timing for first input
    if (lastInputTime === 0) {
      setLastInputTime(currentTime)
      setLastInputLength(0)
    }

    // First, validate the input to check for errors
    const errors = validateTyping(newValue, currentStep.englishText)

    // For non-advantage users: strict error handling - prevent typing beyond errors
    if (errors.length > 0 && !hasQuickAdvantage) {
      const firstErrorIndex = errors[0]

      // If user is trying to type beyond the first error, block it
      if (newValue.length > firstErrorIndex + 1) {
        // Trim to the error position + 1 character (allow the error character to be visible)
        const trimmedValue = newValue.substring(0, firstErrorIndex + 1)
        e.target.value = trimmedValue
        setUserTypedText(trimmedValue)
        setTypingErrors(validateTyping(trimmedValue, currentStep.englishText))

        // Set error state silently (no popup dialogue)
        if (!typingBlocked) {
          setTypingBlocked(true)
          setBlockReason(`Typing error at position ${firstErrorIndex + 1}`)
        }
        return
      }
    }

    // Only run paste detection if there are no typing errors AND user is not currently in error correction mode
    // Also skip if user was previously in error state (to avoid false positives during corrections)
    if (errors.length === 0 && !hasQuickAdvantage && !typingBlocked && detectPasteAttempt(newValue, currentTime)) {
      // Revert to the last known valid text state (not current state)
      const revertText = previousValidText

      // Revert to previous valid text state
      setUserTypedText(revertText)
      e.target.value = revertText

      // Show brief warning without blocking
      setPasteDetected(true)

      // Provide more specific feedback based on the detection reason
      const title = blockReason.includes('speed') ? 'Fast Typing Detected!' : 'Paste Not Allowed!'
      const text = blockReason.includes('speed')
        ? `${blockReason}. Please type at a moderate pace and continue.`
        : `${blockReason}. Please continue typing manually.`

      Swal.fire({
        icon: 'warning',
        title,
        text,
        timer: 2000,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
      })

      // Clear the paste detection flag after a short delay to allow continued typing
      setTimeout(() => {
        setPasteDetected(false)
      }, 1000)

      return
    }

    // Update previous valid text only when there are no errors
    if (errors.length === 0) {
      setPreviousValidText(newValue)
    }

    // Update the text and error state
    setUserTypedText(newValue)
    setTypingErrors(errors)

    // Handle error state management
    if (errors.length > 0) {
      // User has typing errors
      if (!typingBlocked) {
        setTypingBlocked(true)
        setBlockReason(`Typing error detected`)
      }
    } else {
      // No errors - clear blocked state
      if (typingBlocked) {
        setTypingBlocked(false)
        setBlockReason('')
      }
    }

    // Check if typing is complete and correct
    if (newValue === currentStep.englishText && errors.length === 0) {
      setIsTypingComplete(true)
      Swal.fire({
        icon: 'success',
        title: 'Perfect!',
        text: 'Text typed correctly. Now select the target language.',
        timer: 2000,
        showConfirmButton: false
      })
    }

    // Update timing tracking
    setLastInputTime(currentTime)
    setLastInputLength(newValue.length)
  }, [currentStep, isTypingComplete, pasteDetected, typingBlocked, hasQuickAdvantage, userTypedText, previousValidText, blockReason, lastInputTime])

  // Enhanced paste detection with multiple methods (optimized for fast typists)
  const detectPasteAttempt = (newValue: string, currentTime: number): boolean => {
    const timeDiff = currentTime - lastInputTime
    const lengthDiff = newValue.length - userTypedText.length

    // Skip detection if this is the first input or very short text
    if (lastInputTime === 0 || newValue.length < 4) {
      return false
    }

    // Skip detection if user was recently in error state (likely correcting)
    if (typingBlocked || typingErrors.length > 0) {
      return false
    }

    // Skip detection for small changes (likely normal typing or corrections)
    if (Math.abs(lengthDiff) <= 1) {
      return false
    }

    // Method 1: More than 5 characters at once (STRICT RULE - increased from 3 to 5)
    if (lengthDiff > 5) {
      console.log('🚫 Paste detected: More than 5 characters at once')
      setBlockReason('More than 5 characters added at once')
      return true
    }

    // Method 2: Very fast typing (unrealistic speed - more than 3 chars per 50ms)
    // Further increased threshold to allow fast typists and avoid false positives during corrections
    if (lengthDiff > 3 && timeDiff < 50) {
      console.log('🚫 Paste detected: Unrealistic typing speed (>3 chars in <50ms)')
      setBlockReason('Typing speed too fast (possible paste)')
      return true
    }

    // Method 3: Large text block inserted instantly (increased threshold)
    if (lengthDiff > 15) {
      console.log('🚫 Paste detected: Large text block')
      setBlockReason('Large text block added instantly')
      return true
    }

    // Method 4: Perfect text match with suspicious speed (improved calculation)
    if (newValue.length > 30 && newValue === currentStep?.englishText.substring(0, newValue.length)) {
      // Calculate characters per second over the entire typing session
      const totalTime = currentTime - (lastInputTime === 0 ? currentTime : lastInputTime)
      const charsPerSecond = newValue.length / (totalTime / 1000)

      // Flag if typing more than 10 characters per second with perfect accuracy
      if (charsPerSecond > 10 && totalTime > 1000) {
        console.log('🚫 Paste detected: Perfect match with high speed', { charsPerSecond, totalTime })
        setBlockReason('Perfect text match with unrealistic speed')
        return true
      }
    }

    // Method 5: Detect clipboard-like patterns (multiple words at once)
    if (lengthDiff > 3) {
      const addedText = newValue.substring(userTypedText.length)
      const wordCount = addedText.trim().split(/\s+/).length

      // If adding 2+ complete words at once, likely paste
      if (wordCount >= 2 && addedText.includes(' ')) {
        console.log('🚫 Paste detected: Multiple words added at once')
        setBlockReason('Multiple words added simultaneously')
        return true
      }
    }

    return false
  }

  // Additional event handlers for paste detection and error correction
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!hasQuickAdvantage) {
      // Detect Ctrl+V, Cmd+V
      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        e.preventDefault()
        Swal.fire({
          icon: 'warning',
          title: 'Paste Not Allowed!',
          text: 'Keyboard paste shortcuts are disabled. Please continue typing manually.',
          timer: 2000,
          toast: true,
          position: 'top-end',
          showConfirmButton: false
        })
      }

      // Detect long press (holding a key)
      if (e.repeat) {
        console.log('🚫 Long press detected')
        // Allow backspace and delete for corrections
        if (e.key !== 'Backspace' && e.key !== 'Delete') {
          e.preventDefault()
        }
      }
    }

    // Handle any key input for error correction (not just backspace)
    if (typingErrors.length > 0) {
      // Allow any editing - the handleTextInput will manage error state
      setTimeout(() => {
        // Check if errors are fixed after any key input
        const newValue = (e.target as HTMLTextAreaElement).value
        const newErrors = validateTyping(newValue, currentStep?.englishText || '')
        if (newErrors.length === 0) {
          clearTypingErrors()
        }
      }, 10)
    }
  }, [hasQuickAdvantage, typingErrors, currentStep])

  // Detect drag and drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    if (!hasQuickAdvantage) {
      e.preventDefault()
      Swal.fire({
        icon: 'warning',
        title: 'Drag & Drop Not Allowed!',
        text: 'Please continue typing the text manually.',
        timer: 2000,
        toast: true,
        position: 'top-end',
        showConfirmButton: false
      })
    }
  }, [hasQuickAdvantage])

  // Detect right-click context menu
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    if (!hasQuickAdvantage) {
      e.preventDefault()
      Swal.fire({
        icon: 'warning',
        title: 'Context Menu Disabled',
        text: 'Right-click menu is disabled to prevent paste operations.',
        timer: 1500
      })
    }
  }, [hasQuickAdvantage])

  // Validate typing character by character
  const validateTyping = (userText: string, targetText: string): number[] => {
    const errors: number[] = []

    for (let i = 0; i < userText.length; i++) {
      if (i >= targetText.length || userText[i] !== targetText[i]) {
        errors.push(i)
      }
    }

    return errors
  }

  // Handle language selection
  const handleLanguageSelect = (languageCode: string) => {
    if (!currentStep || !isTypingComplete) return

    setSelectedLanguage(languageCode)

    if (languageCode === currentStep.targetLanguage) {
      setIsLanguageCorrect(true)
      Swal.fire({
        icon: 'success',
        title: 'Correct Language!',
        text: 'You selected the correct language. Click Convert to see the translation.',
        timer: 2000,
        showConfirmButton: false
      })
    } else {
      setIsLanguageCorrect(false)
      Swal.fire({
        icon: 'error',
        title: 'Wrong Language!',
        text: `Please select ${currentStep.targetLanguageName} language.`,
        timer: 2000,
        showConfirmButton: false
      })
    }
  }

  // Handle convert button click
  const handleConvert = () => {
    if (!currentStep || !isLanguageCorrect) return

    setShowTranslation(true)
    setCurrentStep(prev => prev ? { ...prev, isConverted: true } : null)
  }

  // Handle submit translation
  const handleSubmitTranslation = () => {
    if (!currentStep || !showTranslation) return

    // Prevent submitting more than 50 translations
    if (localTranslationCount >= 50) {
      Swal.fire({
        icon: 'warning',
        title: 'Daily Limit Reached!',
        text: 'You have already completed 50 translations for today. Please submit your batch to earn rewards.',
        timer: 3000,
        showConfirmButton: false
      })
      return
    }

    // Mark current step as submitted
    setCurrentStep(prev => prev ? { ...prev, isSubmitted: true } : null)

    // Increment local count
    const newLocalCount = localTranslationCount + 1
    setLocalTranslationCount(newLocalCount)

    // Save to localStorage
    const today = new Date().toDateString()
    const sessionKey = `translation_session_${user!.uid}_${today}`
    localStorage.setItem(sessionKey, newLocalCount.toString())

    // Show progress
    if (newLocalCount < 50) {
      Swal.fire({
        icon: 'success',
        title: 'Translation Submitted!',
        text: `Progress: ${newLocalCount}/50 translations completed.`,
        timer: 2000,
        showConfirmButton: false
      }).then(() => {
        // Generate next translation step
        generateNewTranslationStep()
      })
    } else {
      // Exactly 50 translations completed - show final submit option
      setCanSubmitBatch(true)
      setShowFinalSubmit(true)

      Swal.fire({
        icon: 'success',
        title: '🎉 All Translations Completed!',
        text: 'You have completed all 50 translations! Click "Submit & Earn" to get your rewards.',
        timer: 3000,
        showConfirmButton: false
      })

      // Clear current step to prevent further translations
      setCurrentStep(null)
      setShowTranslation(false)
      setIsTypingComplete(false)
      setUserTypedText('')
      setSelectedLanguage('')
      setIsLanguageCorrect(false)
    }
  }

  const submitTranslations = async () => {
    if (!canSubmitBatch || isSubmitting || localTranslationCount < 50) return

    // Check if work is blocked due to leave
    if (isLeaveBlocked) {
      Swal.fire({
        icon: 'warning',
        title: 'Submission Not Available',
        text: leaveStatus.reason || 'Translation submission is not available due to leave.',
        confirmButtonText: 'Go to Dashboard'
      }).then(() => {
        window.location.href = '/dashboard'
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Calculate earning for the batch of 50 translations
      const batchEarningAmount = translationSettings.earningPerBatch

      // Update translation count in database (add 50 translations)
      for (let i = 0; i < 50; i++) {
        await updateTranslationCount(user!.uid)
      }

      // Add batch earning to wallet
      await updateWalletBalance(user!.uid, batchEarningAmount)

      // Add transaction record for the batch
      await addTransaction(user!.uid, {
        type: 'translation_earning',
        amount: batchEarningAmount,
        description: `Batch completion reward - 50 translations completed`
      })

      // Update local state
      const newTodayTranslations = Math.min(todayTranslations + 50, 50)
      setTodayTranslations(newTodayTranslations)
      setTotalTranslationsCompleted(totalTranslationsCompleted + 50)

      // Clear local session data
      const today = new Date().toDateString()
      const sessionKey = `translation_session_${user!.uid}_${today}`
      localStorage.removeItem(sessionKey)

      setLocalTranslationCount(0)
      setCanSubmitBatch(false)
      setShowFinalSubmit(false)
      setIsDailyCompleted(true) // Mark daily session as completed

      Swal.fire({
        icon: 'success',
        title: '🎉 Daily Session Completed!',
        html: `
          <div class="text-center">
            <p class="text-lg font-bold text-green-600 mb-2">₹${batchEarningAmount} Earned!</p>
            <p class="mb-2">50 translations completed and submitted</p>
            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>
            <p class="text-sm text-blue-600 font-semibold">
              🎉 Your daily session is complete! Come back tomorrow for your next session.
            </p>
          </div>
        `,
        confirmButtonText: 'Go to Dashboard',
        timer: 6000,
        showConfirmButton: true
      }).then(() => {
        window.location.href = '/dashboard'
      })

    } catch (error) {
      console.error('Error submitting translations:', error)
      Swal.fire({
        icon: 'error',
        title: 'Submission Failed',
        text: 'There was an error submitting your translations. Please try again.',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading || isLoadingTranslations || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading translations...'}
          </p>
        </div>
      </div>
    )
  }

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Loading work page...</p>
        </div>
      </div>
    )
  }

  // If user is not authenticated, show loading (useRequireAuth will handle redirect)
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Authenticating...</p>
        </div>
      </div>
    )
  }

  // Show blocking notifications if any exist
  if (hasBlockingNotifications && user) {
    return (
      <BlockingNotificationModal
        userId={user.uid}
        onAllRead={markAllAsRead}
      />
    )
  }

  // Show daily completion lockout
  if (isDailyCompleted && user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center shadow-2xl">
          <div className="mb-6">
            <i className="fas fa-check-circle text-6xl text-green-400 mb-4"></i>
            <h2 className="text-3xl font-bold text-white mb-2">
              Daily Work Completed! 🎉
            </h2>
            <p className="text-white/80 text-lg">
              You've successfully completed your 50 translations for today.
            </p>
          </div>

          <div className="bg-white/5 rounded-xl p-6 mb-6">
            <div className="flex items-center justify-between mb-3">
              <span className="text-white/70">Today's Earnings:</span>
              <span className="text-green-400 font-bold text-xl">₹{translationSettings.earningPerBatch}</span>
            </div>
            <div className="flex items-center justify-between mb-3">
              <span className="text-white/70">Translations Completed:</span>
              <span className="text-white font-semibold">50/50</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">Next Session:</span>
              <span className="text-yellow-400 font-semibold">Tomorrow</span>
            </div>
          </div>

          <div className="text-white/60 text-sm mb-6">
            <p className="mb-2">
              <i className="fas fa-clock mr-2"></i>
              Your work session is locked until tomorrow
            </p>
            <p>
              <i className="fas fa-calendar-alt mr-2"></i>
              Come back tomorrow for your next 50 translations
            </p>
          </div>

          <button
            onClick={() => window.location.href = '/dashboard'}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105"
          >
            <i className="fas fa-home mr-2"></i>
            Go to Dashboard
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700">
      {/* Session Recovery Modal */}
      {showSessionRecovery && (
        <SessionRecovery
          onProgressRestored={handleProgressRestored}
          onRecoveryComplete={handleSessionRecoveryComplete}
        />
      )}

      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <Link href="/dashboard" className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </Link>
          <h1 className="text-xl font-bold text-white">Translate Text & Earn</h1>
          <div className="text-white text-right">
            <p className="text-sm">Plan: {translationSettings.plan}</p>
            <p className="text-sm">₹{translationSettings.earningPerBatch}/batch (50 translations)</p>
          </div>
        </div>

        {/* Translation Statistics Header */}
        <div className="grid grid-cols-5 gap-2 text-center">
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-blue-400">{todayTranslations}</p>
            <p className="text-white/80 text-xs">Today TL</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-green-400">{totalTranslationsCompleted}</p>
            <p className="text-white/80 text-xs">Total TL</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-purple-400">{Math.max(0, 50 - localTranslationCount)}</p>
            <p className="text-white/80 text-xs">TL Left</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className="text-lg font-bold text-orange-400">
              {activeDays}/{translationSettings.plan === 'Trial' ? '2' : '30'}
            </p>
            <p className="text-white/80 text-xs">Active Days</p>
          </div>
          <div className="bg-white/10 rounded-lg p-3">
            <p className={`text-lg font-bold ${hasQuickAdvantage ? 'text-green-400' : 'text-gray-400'}`}>
              {hasQuickAdvantage ? copyPasteDaysRemaining : '0'}
            </p>
            <p className="text-white/80 text-xs">Copy Days</p>
          </div>
        </div>

        {/* Auto-save status */}
        <div className="flex items-center justify-center mt-3">
          {isAutoSaving ? (
            <span className="text-yellow-400 text-sm">
              <i className="fas fa-spinner fa-spin mr-1"></i>
              Saving progress...
            </span>
          ) : lastSaved ? (
            <span className="text-green-400 text-sm">
              <i className="fas fa-check mr-1"></i>
              Last saved: {lastSaved.toLocaleTimeString()}
            </span>
          ) : (
            <span className="text-blue-400 text-sm">
              <i className="fas fa-shield-alt mr-1"></i>
              Auto-save protection enabled
            </span>
          )}
        </div>
      </header>

      {/* Translation Section */}
      <div className="glass-card p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white">
            <i className="fas fa-language mr-2"></i>
            Translate Text & Earn
          </h2>
          <button
            onClick={resetCurrentStep}
            className="glass-button px-3 py-1 text-white text-sm"
            title="Clear typed text and reset"
          >
            <i className="fas fa-eraser mr-1"></i>
            Reset
          </button>
        </div>

        {!currentStep && !isLoadingTranslations && (
          <div className="text-center py-12">
            <div className="bg-white/10 rounded-lg p-8">
              <i className="fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"></i>
              <h3 className="text-xl font-bold text-white mb-4">No Translation Available</h3>
              <p className="text-white/80 mb-6">
                Unable to load translation data. This could be due to:
              </p>
              <ul className="text-white/70 text-left max-w-md mx-auto mb-6">
                <li className="mb-2">• Translation data file not found</li>
                <li className="mb-2">• Network connectivity issues</li>
                <li className="mb-2">• Server maintenance</li>
              </ul>
              <button
                onClick={() => window.location.reload()}
                className="btn-primary px-6 py-3 rounded-lg font-semibold"
              >
                <i className="fas fa-sync-alt mr-2"></i>
                Retry Loading
              </button>
            </div>
          </div>
        )}

        {currentStep && (
          <div className="space-y-6">
            {/* Step 1: English Text Typing */}
            <div className="bg-white/10 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white font-semibold">
                  <i className="fas fa-keyboard mr-2"></i>
                  Step 1: Type the English text below
                </h3>
                {/* Copy Button - Only show when copy-paste is enabled */}
                {hasQuickAdvantage ? (
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(currentStep.englishText)
                      Swal.fire({
                        icon: 'success',
                        title: 'Copied!',
                        text: 'English text copied to clipboard',
                        timer: 1500,
                        showConfirmButton: false
                      })
                    }}
                    className="group relative bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 animate-pulse"
                    title="Copy English text"
                  >
                    <i className="fas fa-copy mr-2"></i>
                    <span className="text-sm">Copy</span>
                    <div className="absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                  </button>
                ) : (
                  <div className="text-white/60 text-xs bg-white/10 px-3 py-2 rounded-lg">
                    <i className="fas fa-lock mr-1"></i>
                    Copy disabled - Type manually
                  </div>
                )}
              </div>
              <div className="bg-white/5 p-3 rounded border-l-4 border-blue-400 mb-3">
                <div className="max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
                  <p className="text-white text-base md:text-lg font-mono leading-relaxed">
                    {typingErrors.length > 0 && !hasQuickAdvantage ? (
                      <>
                        {/* Show text with error highlighting */}
                        <span className="text-green-400">
                          {currentStep.englishText.substring(0, typingErrors[0])}
                        </span>
                        <span className="bg-red-500 text-white px-1 rounded animate-pulse">
                          {currentStep.englishText[typingErrors[0]]}
                        </span>
                        <span className="text-white/60">
                          {currentStep.englishText.substring(typingErrors[0] + 1)}
                        </span>
                      </>
                    ) : (
                      currentStep.englishText
                    )}
                  </p>
                </div>
                <div className="text-xs text-white/60 mt-2">
                  <i className="fas fa-info-circle mr-1"></i>
                  {typingErrors.length > 0 && !hasQuickAdvantage
                    ? `Error at highlighted character (position ${typingErrors[0] + 1})`
                    : "Scroll to see full text if needed"
                  }
                </div>
              </div>

              <div className="relative">
                <textarea
                value={userTypedText}
                onChange={handleTextInput}
                onKeyDown={handleKeyDown}
                onDrop={handleDrop}
                onContextMenu={handleContextMenu}
                disabled={isTypingComplete}
                placeholder={
                  typingErrors.length > 0
                    ? "Fix the highlighted error and continue typing..."
                    : hasQuickAdvantage
                      ? "Type or paste the English text here..."
                      : "Type the English text here (copy-paste not allowed). Fast typists: please type at moderate speed to avoid triggering anti-paste protection."
                }
                className={`w-full h-24 md:h-32 p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200 placeholder-white/60 resize-none font-mono text-sm md:text-base leading-relaxed ${
                  typingErrors.length > 0 ? 'border-red-500' : ''
                } ${isTypingComplete ? 'border-green-500 bg-green-500/10' : ''}`}
                onPaste={(e) => {
                  if (!hasQuickAdvantage) {
                    e.preventDefault()
                    Swal.fire({
                      icon: 'warning',
                      title: 'Paste Not Allowed!',
                      text: 'Please continue typing the text manually.',
                      timer: 2000,
                      toast: true,
                      position: 'top-end',
                      showConfirmButton: false
                    })
                  }
                }}
                onDragOver={(e) => {
                  if (!hasQuickAdvantage) {
                    e.preventDefault()
                  }
                }}
                spellCheck={false}
                autoComplete="off"
                autoCorrect="off"
                autoCapitalize="off"
              />

              {/* Paste Button - Only show when copy-paste is enabled */}
              {hasQuickAdvantage && (
                <button
                  onClick={async () => {
                    try {
                      const text = await navigator.clipboard.readText()
                      setUserTypedText(text)
                      handleTextInput({ target: { value: text } } as any)
                      Swal.fire({
                        icon: 'success',
                        title: 'Pasted!',
                        text: 'Text pasted from clipboard',
                        timer: 1500,
                        showConfirmButton: false
                      })
                    } catch (error) {
                      Swal.fire({
                        icon: 'error',
                        title: 'Paste Failed',
                        text: 'Could not access clipboard',
                        timer: 1500,
                        showConfirmButton: false
                      })
                    }
                  }}
                  className="group absolute top-3 right-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-2 px-3 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200 animate-bounce disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:animate-none"
                  title="Paste from clipboard"
                  disabled={isTypingComplete}
                >
                  <i className="fas fa-paste mr-1"></i>
                  <span className="text-xs">Paste</span>
                  <div className="absolute inset-0 bg-white/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                </button>
              )}
              </div>

              {typingErrors.length > 0 && (
                <div className="mt-2 text-red-400 text-sm bg-red-500/10 border border-red-500/30 rounded-lg p-3">
                  <div className="flex items-center mb-2">
                    <i className="fas fa-exclamation-triangle mr-2"></i>
                    <strong>Typing Error Detected</strong>
                  </div>
                  <div className="text-red-300 text-xs mb-2">
                    Error at position {typingErrors[0] + 1}: Expected "{currentStep.englishText[typingErrors[0]]}" but got "{userTypedText[typingErrors[0]] || 'nothing'}"
                  </div>
                  <div className="text-red-200 text-xs">
                    <i className="fas fa-edit mr-1"></i>
                    Edit the text box to correct the mistake, then continue typing.
                  </div>
                </div>
              )}

              {isTypingComplete && (
                <div className="mt-2 text-green-400 text-sm">
                  <i className="fas fa-check-circle mr-1"></i>
                  Perfect! Text typed correctly.
                </div>
              )}

              {pasteDetected && !hasQuickAdvantage && (
                <div className="mt-2 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg">
                  <div className="text-yellow-400 text-sm">
                    <i className="fas fa-exclamation-triangle mr-1"></i>
                    <strong>Paste Attempt Detected</strong>
                  </div>
                  <div className="text-yellow-300 text-xs mt-1">
                    {blockReason.includes('speed') ? (
                      <>
                        <i className="fas fa-info-circle mr-1"></i>
                        Fast typing detected. Please type at a moderate pace. You can continue typing normally.
                      </>
                    ) : (
                      <>
                        <i className="fas fa-clipboard mr-1"></i>
                        Paste operation blocked. Please continue typing manually from where you left off.
                      </>
                    )}
                  </div>
                  <div className="text-yellow-200 text-xs mt-2">
                    <i className="fas fa-arrow-right mr-1"></i>
                    This message will disappear automatically. Continue typing normally.
                  </div>
                </div>
              )}



              {/* Helper Text for Error Correction */}
              {typingErrors.length > 0 && !hasQuickAdvantage && (
                <div className="mt-3 text-center">
                  <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
                    <div className="text-blue-400 text-sm font-semibold mb-2">
                      <i className="fas fa-lightbulb mr-2"></i>
                      How to Fix the Error:
                    </div>
                    <div className="text-blue-300 text-xs space-y-1">
                      <div>1. Click in the text box and edit the incorrect character</div>
                      <div>2. Change it to the correct character: "{currentStep.englishText[typingErrors[0]]}"</div>
                      <div>3. Continue typing the rest of the text</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Physical Check Button */}
              {userTypedText && !isTypingComplete && typingErrors.length === 0 && (
                <div className="mt-3 text-center">
                  <button
                    onClick={() => {
                      // Manually trigger text validation
                      handleTextInput({ target: { value: userTypedText } } as any)
                    }}
                    className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                    title="Check if typed text is correct"
                  >
                    <i className="fas fa-check-circle mr-2"></i>
                    Check Text
                  </button>
                </div>
              )}
            </div>

            {/* Step 2: Language Selection */}
            {isTypingComplete && (
              <div className="bg-white/10 rounded-lg p-4">
                <h3 className="text-white font-semibold mb-2">
                  <i className="fas fa-globe mr-2"></i>
                  Step 2: Select the target language - {currentStep.targetLanguageName}
                </h3>
                <select
                  value={selectedLanguage}
                  onChange={(e) => handleLanguageSelect(e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-purple-600 focus:ring-2 focus:ring-purple-200"
                >
                  <option value="" className="bg-gray-800 text-white">Select target language...</option>
                  {AVAILABLE_LANGUAGES.map((lang) => (
                    <option key={lang.code} value={lang.code} className="bg-gray-800 text-white">
                      {lang.flag} {lang.name}
                    </option>
                  ))}
                </select>

                {selectedLanguage && !isLanguageCorrect && (
                  <div className="mt-2 text-red-400 text-sm">
                    <i className="fas fa-times-circle mr-1"></i>
                    Wrong language! Please select {currentStep.targetLanguageName}.
                  </div>
                )}

                {isLanguageCorrect && (
                  <div className="mt-2 text-green-400 text-sm">
                    <i className="fas fa-check-circle mr-1"></i>
                    Correct language selected!
                  </div>
                )}
              </div>
            )}

            {/* Step 3: Convert Button */}
            {isLanguageCorrect && (
              <div className="text-center">
                <button
                  onClick={handleConvert}
                  disabled={showTranslation}
                  className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    showTranslation
                      ? 'btn-disabled cursor-not-allowed opacity-50'
                      : 'btn-primary hover:scale-105'
                  }`}
                >
                  <i className="fas fa-exchange-alt mr-2"></i>
                  Convert to {currentStep.targetLanguageName}
                </button>
              </div>
            )}

            {/* Step 4: Show Translation */}
            {showTranslation && (
              <div className="bg-white/10 rounded-lg p-4">
                <h3 className="text-white font-semibold mb-2">
                  <i className="fas fa-language mr-2"></i>
                  {currentStep.targetLanguageName} Translation:
                </h3>
                <div className="bg-white/5 p-3 rounded border-l-4 border-green-400">
                  <p className="text-white text-lg">
                    {currentStep.targetTranslation}
                  </p>
                </div>

                <div className="text-center mt-4">
                  <button
                    onClick={handleSubmitTranslation}
                    disabled={localTranslationCount >= 50}
                    className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                      localTranslationCount >= 50
                        ? 'btn-disabled cursor-not-allowed opacity-50'
                        : 'btn-success hover:scale-105'
                    }`}
                  >
                    <i className="fas fa-check mr-2"></i>
                    {localTranslationCount >= 50 ? 'Daily Limit Reached' : 'Submit Translation'}
                  </button>
                </div>
              </div>
            )}

            {/* Final Submit Button */}
            {showFinalSubmit && !isDailyCompleted && (
              <div className="text-center mb-6">
                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 mb-4 shadow-lg">
                  <h3 className="text-white font-bold text-xl mb-2">
                    🎉 Congratulations! You've completed 50 translations!
                  </h3>
                  <p className="text-white/90 mb-4">
                    Click the button below to submit your daily batch and receive your earnings.
                  </p>
                  <p className="text-white/80 text-sm">
                    ⚠️ After submission, you won't be able to work until tomorrow.
                  </p>
                </div>

                <button
                  onClick={submitTranslations}
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 px-12 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none animate-pulse"
                >
                  {isSubmitting ? (
                    <>
                      <i className="fas fa-spinner fa-spin mr-2"></i>
                      Submitting Final Batch...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-trophy mr-2"></i>
                      Submit Final Batch & Earn ₹{translationSettings.earningPerBatch}
                    </>
                  )}
                </button>
              </div>
            )}

            {/* Regular Submit Button (for testing/admin) */}
            {canSubmitBatch && !showFinalSubmit && (
              <div className="text-center">
                <button
                  onClick={submitTranslations}
                  disabled={isSubmitting}
                  className="btn-success px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 hover:scale-105"
                >
                  <i className="fas fa-money-bill-wave mr-2"></i>
                  Submit All 50 Translations & Earn ₹{translationSettings.earningPerBatch}
                </button>
              </div>
            )}

            {/* Progress */}
            <div className="text-center">
              <p className="text-white/80">
                Progress: {localTranslationCount}/50 translations completed
              </p>
              <div className="w-full bg-white/20 rounded-full h-2 mt-2">
                <div
                  className="bg-gradient-to-r from-purple-600 to-purple-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(localTranslationCount / 50) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
