(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,2922,7718],{2439:(e,s,t)=>{"use strict";t.d(s,{S3:()=>p,i7:()=>n,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>l});var a=t(6104),r=t(5317);let i={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},c={users:"users"};class o{static async calculateActiveDays(e){try{var s,t;let i,n=await (0,r.x7)((0,r.H9)(a.db,c.users,e));if(!n.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let l=n.data(),d=(null==(s=l.joinedDate)?void 0:s.toDate())||new Date,u=null==(t=l.lastActiveDayUpdate)?void 0:t.toDate(),p=l.activeDays||0,y=l.plan||"Trial",m=new Date,h=m.toDateString(),g=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(p)),console.log("   - Last update: ".concat(g||"Never")),console.log("   - Today: ".concat(h)),console.log("   - Plan: ".concat(y)),g===h)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:p,shouldUpdate:!1,isNewDay:!1};if("Admin"===y)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await o.updateLastActiveDayUpdate(e),{activeDays:p,shouldUpdate:!1,isNewDay:!0};if(await o.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await o.updateLastActiveDayUpdate(e),{activeDays:p,shouldUpdate:!1,isNewDay:!0};return i="Trial"===y?Math.floor((m.getTime()-d.getTime())/864e5)+1:p+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(p," → ").concat(i)),{activeDays:i,shouldUpdate:i!==p,isNewDay:!0}}catch(s){return console.error("Error calculating active days for user ".concat(e,":"),s),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let s=await o.calculateActiveDays(e);if(s.shouldUpdate){let t=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(t,{[i.activeDays]:s.activeDays,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(s.activeDays))}else s.isNewDay&&await o.updateLastActiveDayUpdate(e);return s.activeDays}catch(s){throw console.error("Error updating active days for user ".concat(e,":"),s),s}}static async updateLastActiveDayUpdate(e){try{let s=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(s,{[i.lastActiveDayUpdate]:r.Dc.now()})}catch(s){console.error("Error updating last active day timestamp for user ".concat(e,":"),s)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:s}=await t.e(9567).then(t.bind(t,9567));return await s(e,new Date)}catch(s){return console.error("Error checking leave status for user ".concat(e,":"),s),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,c.users)),s=0,t=0,i=0;for(let a of e.docs)try{s++;let e=await o.calculateActiveDays(a.id);(e.shouldUpdate||e.isNewDay)&&(await o.updateUserActiveDays(a.id),e.shouldUpdate&&t++)}catch(e){i++,console.error("Error processing active days for user ".concat(a.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(s," users")),console.log("   - Updated: ".concat(t," users")),console.log("   - Errors: ".concat(i," users")),{processed:s,updated:t,errors:i}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,c.users,e));if(!s.exists())return 0;return s.data().activeDays||0}catch(s){return console.error("Error getting active days for user ".concat(e,":"),s),0}}static async initializeActiveDaysForNewUser(e){try{let s=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(s,{[i.activeDays]:1,[i.lastActiveDayUpdate]:r.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(s){throw console.error("Error initializing active days for user ".concat(e,":"),s),s}}}let n=o.calculateActiveDays,l=o.updateUserActiveDays,d=o.processAllUsersActiveDays,u=o.getUserActiveDays,p=o.initializeActiveDaysForNewUser},4172:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(5155),r=t(2115),i=t(6681),c=t(7718),o=t(2439);function n(){let{user:e,loading:s}=(0,i.Nu)(),[n,l]=(0,r.useState)(null),[d,u]=(0,r.useState)(!1),p=async()=>{u(!0),l(null);try{console.log("\uD83E\uDDEA Testing daily reduction processes..."),console.log("1. Testing active days increment...");let e=await (0,o.mH)();console.log("2. Testing copy-paste reduction...");let s=await (0,c.Mk)();l({success:!0,activeDaysResult:e,copyPasteResult:s,message:"Daily reduction test completed successfully"})}catch(e){l({success:!1,error:e.message,message:"Daily reduction test failed"})}finally{u(!1)}},y=async()=>{let e=prompt("Enter user email to check copy-paste status:");if(e){u(!0);try{let{searchUsers:s}=await Promise.all([t.e(3592),t.e(6779)]).then(t.bind(t,6779)),a=(await s(e)).find(s=>{var t;return(null==(t=s.email)?void 0:t.toLowerCase())===e.toLowerCase()});if(!a)return void l({success:!1,message:"User not found: ".concat(e)});let r=await (0,c.checkCopyPastePermission)(a.id);l({success:!0,userEmail:e,userId:a.id,copyPasteStatus:r,message:"Copy-paste status retrieved successfully"})}catch(e){l({success:!1,error:e.message,message:"Failed to check copy-paste status"})}finally{u(!1)}}};return s?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,a.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Sample Upload & Daily Reduction"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"1. Download Test CSV"}),(0,a.jsx)("p",{className:"text-white/80 mb-3",children:"Download a test CSV file with sample data including copy-paste days."}),(0,a.jsxs)("button",{onClick:()=>{let e=new Blob(["email,totalTranslations,walletBalance,activeDays,copyPasteDays\<EMAIL>,50,250,10,7\<EMAIL>,100,500,15,14\<EMAIL>,25,125,5,3\<EMAIL>,75,375,12,0"],{type:"text/csv"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="test-sample-upload.csv",t.click(),URL.revokeObjectURL(s)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Download Test CSV"]})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"2. Upload Test Data"}),(0,a.jsxs)("p",{className:"text-white/80 mb-3",children:["Go to ",(0,a.jsx)("strong",{children:"Admin → Simple Upload"})," and upload the test CSV file."]}),(0,a.jsxs)("a",{href:"/admin/simple-upload",className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg inline-block",children:[(0,a.jsx)("i",{className:"fas fa-upload mr-2"}),"Go to Simple Upload"]})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"3. Test Daily Reduction"}),(0,a.jsx)("p",{className:"text-white/80 mb-3",children:"Test the daily reduction process for active days and copy-paste permissions."}),(0,a.jsx)("button",{onClick:p,disabled:d,className:"bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Testing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-clock mr-2"}),"Test Daily Reduction"]})})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"4. Check User Copy-Paste Status"}),(0,a.jsx)("p",{className:"text-white/80 mb-3",children:"Check the copy-paste permission status for a specific user."}),(0,a.jsx)("button",{onClick:y,disabled:d,className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4 inline-block"}),"Checking..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-search mr-2"}),"Check User Status"]})})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-3",children:"5. Test Export with Copy-Paste Days"}),(0,a.jsx)("p",{className:"text-white/80 mb-3",children:"Export users to verify copy-paste remaining days are included."}),(0,a.jsxs)("a",{href:"/admin/users",className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg inline-block",children:[(0,a.jsx)("i",{className:"fas fa-users mr-2"}),"Go to Users & Export"]})]}),n&&(0,a.jsxs)("div",{className:"rounded-lg p-4 ".concat(n.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"),children:[(0,a.jsx)("h3",{className:"font-bold ".concat(n.success?"text-green-400":"text-red-400"),children:n.success?"Test Results - Success!":"Test Results - Failed"}),(0,a.jsx)("p",{className:"text-white mt-2",children:n.message}),n.activeDaysResult&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Active Days Result:"}),(0,a.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(n.activeDaysResult,null,2)})]}),n.copyPasteResult&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Copy-Paste Reduction Result:"}),(0,a.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(n.copyPasteResult,null,2)})]}),n.copyPasteStatus&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"User Copy-Paste Status:"}),(0,a.jsxs)("div",{className:"text-white/80 text-sm mt-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",n.userEmail]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"User ID:"})," ",n.userId]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Has Permission:"})," ",n.copyPasteStatus.hasPermission?"Yes":"No"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Days Remaining:"})," ",n.copyPasteStatus.daysRemaining]}),n.copyPasteStatus.expiryDate&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Expiry Date:"})," ",n.copyPasteStatus.expiryDate.toDateString()]})]})]}),n.error&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,a.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mt-1",children:n.error})]})]}),(0,a.jsxs)("div",{className:"bg-blue-500/10 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-blue-400 font-bold mb-3",children:"Testing Instructions:"}),(0,a.jsxs)("ol",{className:"text-blue-300 text-sm space-y-2 list-decimal list-inside",children:[(0,a.jsx)("li",{children:"Download the test CSV file"}),(0,a.jsx)("li",{children:"Go to Simple Upload and upload the CSV"}),(0,a.jsx)("li",{children:"Verify users are updated with copy-paste permissions"}),(0,a.jsx)("li",{children:"Test daily reduction to see copy-paste days decrease"}),(0,a.jsx)("li",{children:"Check individual user status"}),(0,a.jsx)("li",{children:"Export users to verify copy-paste remaining days are included"}),(0,a.jsx)("li",{children:"Check browser console for detailed logs"})]})]})]})]})})})}},6147:(e,s,t)=>{Promise.resolve().then(t.bind(t,4172))},7718:(e,s,t)=>{"use strict";t.d(s,{Mk:()=>p,checkCopyPastePermission:()=>n,grantCopyPastePermission:()=>l,i7:()=>u,removeCopyPastePermission:()=>d});var a=t(6104),r=t(5317);let i={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},c={users:"users"};class o{static async checkCopyPastePermission(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,c.users,e));if(!s.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let t=s.data()[i.quickTranslationAdvantageExpiry];if(!t)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let o=t.toDate(),n=new Date,l=o>n,d=l?Math.ceil((o.getTime()-n.getTime())/864e5):0;return{hasPermission:l,daysRemaining:d,expiryDate:o}}catch(s){return console.error("Error checking copy-paste permission for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,s){try{let t=new Date;t.setDate(t.getDate()+s);let o=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(o,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(t),[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("✅ Granted copy-paste permission to user ".concat(e," for ").concat(s," days (expires: ").concat(t.toDateString(),")"))}catch(s){throw console.error("Error granting copy-paste permission to user ".concat(e,":"),s),s}}static async removeCopyPastePermission(e){try{let s=(0,r.H9)(a.db,c.users,e);await (0,r.mZ)(s,{[i.quickTranslationAdvantageExpiry]:null}),console.log("✅ Removed copy-paste permission from user ".concat(e))}catch(s){throw console.error("Error removing copy-paste permission from user ".concat(e,":"),s),s}}static async reduceCopyPasteDays(e){try{let s=await (0,r.x7)((0,r.H9)(a.db,c.users,e));if(!s.exists())return{reduced:!1,daysRemaining:0,expired:!1};let t=s.data(),o=t[i.quickTranslationAdvantageExpiry],n=t[i.lastCopyPasteReduction];if(!o)return{reduced:!1,daysRemaining:0,expired:!1};let l=new Date().toDateString();if((n?n.toDate().toDateString():null)===l){let e=o.toDate(),s=new Date,t=Math.max(0,Math.ceil((e.getTime()-s.getTime())/864e5));return{reduced:!1,daysRemaining:t,expired:0===t}}let d=o.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let p=(0,r.H9)(a.db,c.users,e);if(u<=new Date)return await (0,r.mZ)(p,{[i.quickTranslationAdvantageExpiry]:null,[i.lastCopyPasteReduction]:r.Dc.now()}),console.log("\uD83D\uDCC5 Copy-paste permission expired for user ".concat(e)),{reduced:!0,daysRemaining:0,expired:!0};{await (0,r.mZ)(p,{[i.quickTranslationAdvantageExpiry]:r.Dc.fromDate(u),[i.lastCopyPasteReduction]:r.Dc.now()});let s=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log("\uD83D\uDCC5 Reduced copy-paste days for user ".concat(e,": ").concat(s," days remaining")),{reduced:!0,daysRemaining:s,expired:!1}}}catch(s){return console.error("Error reducing copy-paste days for user ".concat(e,":"),s),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,r.getDocs)((0,r.collection)(a.db,c.users)),s=0,t=0,i=0,n=0;for(let a of e.docs)try{s++;let e=await o.reduceCopyPasteDays(a.id);e.reduced&&(t++,e.expired&&i++)}catch(e){n++,console.error("Error processing copy-paste reduction for user ".concat(a.id,":"),e)}return console.log("✅ Daily copy-paste reduction complete:"),console.log("   - Processed: ".concat(s," users")),console.log("   - Reduced: ".concat(t," users")),console.log("   - Expired: ".concat(i," users")),console.log("   - Errors: ".concat(n," users")),{processed:s,reduced:t,expired:i,errors:n}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let s=await o.checkCopyPastePermission(e);return{hasPermission:s.hasPermission,daysRemaining:s.daysRemaining,expiryDate:s.expiryDate?s.expiryDate.toDateString():null}}catch(s){return console.error("Error getting copy-paste status for user ".concat(e,":"),s),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let n=o.checkCopyPastePermission,l=o.grantCopyPastePermission,d=o.removeCopyPastePermission,u=o.reduceCopyPasteDays,p=o.processAllUsersCopyPasteReduction;o.getCopyPasteStatus}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6681,8441,1684,7358],()=>s(6147)),_N_E=e.O()}]);