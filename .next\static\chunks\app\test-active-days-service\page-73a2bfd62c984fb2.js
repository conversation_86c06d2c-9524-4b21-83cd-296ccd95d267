(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2439,7410],{1420:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(5155),c=t(2115),r=t(6681),l=t(2439);function i(){let{user:e,loading:s}=(0,r.Nu)(),[t,i]=(0,c.useState)(""),[o,n]=(0,c.useState)(null),[d,u]=(0,c.useState)(!1),y=async()=>{if(!t.trim())return void alert("Please enter a user ID");u(!0),n(null);try{console.log("\uD83E\uDDEA Testing active days service for user: ".concat(t)),console.log("1. Testing getUserActiveDays...");let e=await (0,l.nd)(t.trim());console.log("✅ getUserActiveDays result:",e),console.log("2. Testing calculateActiveDays...");let s=await (0,l.i7)(t.trim());console.log("✅ calculateActiveDays result:",s),console.log("3. Testing updateUserActiveDays...");let a=await (0,l.updateUserActiveDays)(t.trim());console.log("✅ updateUserActiveDays result:",a),n({success:!0,currentActiveDays:e,calculation:s,updatedActiveDays:a,message:"Single user test completed successfully"})}catch(e){console.error("❌ Error in single user test:",e),n({success:!1,error:e.message,stack:e.stack,message:"Single user test failed"})}finally{u(!1)}},h=async()=>{u(!0),n(null);try{console.log("\uD83E\uDDEA Testing processAllUsersActiveDays...");let e=await (0,l.mH)();console.log("✅ processAllUsersActiveDays result:",e),n({success:!0,allUsersResult:e,message:"All users test completed successfully"})}catch(e){console.error("❌ Error in all users test:",e),n({success:!1,error:e.message,stack:e.stack,message:"All users test failed"})}finally{u(!1)}};return s?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading..."})]})}):(0,a.jsx)("div",{className:"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Test Active Days Service"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white mb-2",children:"User ID (for single user test):"}),(0,a.jsx)("input",{type:"text",value:t,onChange:e=>i(e.target.value),className:"w-full p-3 rounded-lg bg-white/10 text-white border border-white/20",placeholder:"Enter user ID"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:y,disabled:d,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test Single User"}),(0,a.jsx)("button",{onClick:h,disabled:d,className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50",children:d?"Testing...":"Test All Users"})]}),o&&(0,a.jsxs)("div",{className:"p-4 rounded-lg ".concat(o.success?"bg-green-500/20 border border-green-500/30":"bg-red-500/20 border border-red-500/30"),children:[(0,a.jsx)("h3",{className:"font-bold ".concat(o.success?"text-green-400":"text-red-400"),children:o.success?"Success!":"Failed"}),(0,a.jsx)("p",{className:"text-white mt-2",children:o.message}),void 0!==o.currentActiveDays&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Current Active Days:"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mt-1",children:o.currentActiveDays})]}),o.calculation&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Calculation Result:"}),(0,a.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(o.calculation,null,2)})]}),void 0!==o.updatedActiveDays&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Updated Active Days:"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mt-1",children:o.updatedActiveDays})]}),o.allUsersResult&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-white/10 rounded",children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"All Users Result:"}),(0,a.jsx)("pre",{className:"text-white/80 text-sm mt-1",children:JSON.stringify(o.allUsersResult,null,2)})]}),o.error&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-red-500/20 rounded",children:[(0,a.jsx)("h4",{className:"text-red-400 font-semibold",children:"Error Details:"}),(0,a.jsx)("p",{className:"text-white/80 text-sm mt-1",children:o.error}),o.stack&&(0,a.jsx)("pre",{className:"text-white/60 text-xs mt-2 overflow-auto",children:o.stack})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 p-4 bg-white/5 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-white font-bold mb-2",children:"Instructions:"}),(0,a.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Enter a user ID to test single user functions"}),(0,a.jsx)("li",{children:'• Click "Test Single User" to test calculation and update functions'}),(0,a.jsx)("li",{children:'• Click "Test All Users" to test the batch processing function'}),(0,a.jsx)("li",{children:"• Check browser console for detailed logs"})]})]})]})})})}},2439:(e,s,t)=>{"use strict";t.d(s,{S3:()=>y,i7:()=>o,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>n});var a=t(6104),c=t(5317);let r={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},l={users:"users"};class i{static async calculateActiveDays(e){try{var s,t;let r,o=await (0,c.x7)((0,c.H9)(a.db,l.users,e));if(!o.exists())return console.error("User ".concat(e," not found")),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let n=o.data(),d=(null==(s=n.joinedDate)?void 0:s.toDate())||new Date,u=null==(t=n.lastActiveDayUpdate)?void 0:t.toDate(),y=n.activeDays||0,h=n.plan||"Trial",v=new Date,g=v.toDateString(),m=u?u.toDateString():null;if(console.log("\uD83D\uDCC5 Calculating active days for user ".concat(e,":")),console.log("   - Joined: ".concat(d.toDateString())),console.log("   - Current active days: ".concat(y)),console.log("   - Last update: ".concat(m||"Never")),console.log("   - Today: ".concat(g)),console.log("   - Plan: ".concat(h)),m===g)return console.log("✅ Already updated today for user ".concat(e)),{activeDays:y,shouldUpdate:!1,isNewDay:!1};if("Admin"===h)return console.log("⏭️ Skipping active days increment for admin user ".concat(e)),await i.updateLastActiveDayUpdate(e),{activeDays:y,shouldUpdate:!1,isNewDay:!0};if(await i.isUserOnLeaveToday(e))return console.log("\uD83C\uDFD6️ User ".concat(e," is on leave today, not incrementing active days")),await i.updateLastActiveDayUpdate(e),{activeDays:y,shouldUpdate:!1,isNewDay:!0};return r="Trial"===h?Math.floor((v.getTime()-d.getTime())/864e5)+1:y+1,console.log("\uD83D\uDCC8 New active days calculated: ".concat(y," → ").concat(r)),{activeDays:r,shouldUpdate:r!==y,isNewDay:!0}}catch(s){return console.error("Error calculating active days for user ".concat(e,":"),s),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let s=await i.calculateActiveDays(e);if(s.shouldUpdate){let t=(0,c.H9)(a.db,l.users,e);await (0,c.mZ)(t,{[r.activeDays]:s.activeDays,[r.lastActiveDayUpdate]:c.Dc.now()}),console.log("✅ Updated active days for user ".concat(e,": ").concat(s.activeDays))}else s.isNewDay&&await i.updateLastActiveDayUpdate(e);return s.activeDays}catch(s){throw console.error("Error updating active days for user ".concat(e,":"),s),s}}static async updateLastActiveDayUpdate(e){try{let s=(0,c.H9)(a.db,l.users,e);await (0,c.mZ)(s,{[r.lastActiveDayUpdate]:c.Dc.now()})}catch(s){console.error("Error updating last active day timestamp for user ".concat(e,":"),s)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:s}=await t.e(9567).then(t.bind(t,9567));return await s(e,new Date)}catch(s){return console.error("Error checking leave status for user ".concat(e,":"),s),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,c.getDocs)((0,c.collection)(a.db,l.users)),s=0,t=0,r=0;for(let a of e.docs)try{s++;let e=await i.calculateActiveDays(a.id);(e.shouldUpdate||e.isNewDay)&&(await i.updateUserActiveDays(a.id),e.shouldUpdate&&t++)}catch(e){r++,console.error("Error processing active days for user ".concat(a.id,":"),e)}return console.log("✅ Daily active days processing complete:"),console.log("   - Processed: ".concat(s," users")),console.log("   - Updated: ".concat(t," users")),console.log("   - Errors: ".concat(r," users")),{processed:s,updated:t,errors:r}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let s=await (0,c.x7)((0,c.H9)(a.db,l.users,e));if(!s.exists())return 0;return s.data().activeDays||0}catch(s){return console.error("Error getting active days for user ".concat(e,":"),s),0}}static async initializeActiveDaysForNewUser(e){try{let s=(0,c.H9)(a.db,l.users,e);await (0,c.mZ)(s,{[r.activeDays]:1,[r.lastActiveDayUpdate]:c.Dc.now()}),console.log("✅ Initialized active days for new user ".concat(e,": Day 1"))}catch(s){throw console.error("Error initializing active days for user ".concat(e,":"),s),s}}}let o=i.calculateActiveDays,n=i.updateUserActiveDays,d=i.processAllUsersActiveDays,u=i.getUserActiveDays,y=i.initializeActiveDaysForNewUser},7935:(e,s,t)=>{Promise.resolve().then(t.bind(t,1420))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6681,8441,1684,7358],()=>s(7935)),_N_E=e.O()}]);