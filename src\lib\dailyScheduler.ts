import { processAllUsersActiveDays } from './activeDaysService'
import { processAllUsersCopyPasteReduction } from './copyPasteService'

// Daily Scheduler Service
export class DailyScheduler {
  
  /**
   * Process all daily tasks (active days increment + copy-paste reduction)
   */
  static async processDailyTasks(): Promise<{
    activeDaysResult: any
    copyPasteResult: any
    success: boolean
    timestamp: string
  }> {
    try {
      console.log('🌅 Starting daily tasks processing...')
      const startTime = new Date()
      
      // Process active days increment
      console.log('📅 Processing active days increment...')
      const activeDaysResult = await processAllUsersActiveDays()
      
      // Process copy-paste reduction
      console.log('📋 Processing copy-paste reduction...')
      const copyPasteResult = await processAllUsersCopyPasteReduction()
      
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()
      
      console.log('✅ Daily tasks completed successfully!')
      console.log(`⏱️ Total processing time: ${duration}ms`)
      console.log('📊 Results summary:')
      console.log(`   Active Days - Processed: ${activeDaysResult.processed}, Updated: ${activeDaysResult.updated}`)
      console.log(`   Copy-Paste - Processed: ${copyPasteResult.processed}, Reduced: ${copyPasteResult.reduced}, Expired: ${copyPasteResult.expired}`)
      
      return {
        activeDaysResult,
        copyPasteResult,
        success: true,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      console.error('❌ Error in daily tasks processing:', error)
      return {
        activeDaysResult: { processed: 0, updated: 0, errors: 1 },
        copyPasteResult: { processed: 0, reduced: 0, expired: 0, errors: 1 },
        success: false,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Check if daily tasks should run (once per day)
   */
  static shouldRunDailyTasks(): boolean {
    try {
      const lastRun = localStorage.getItem('lastDailyTasksRun')
      const today = new Date().toDateString()
      
      if (!lastRun || lastRun !== today) {
        return true
      }
      
      return false
    } catch (error) {
      console.error('Error checking daily tasks schedule:', error)
      return true // Default to running if we can't check
    }
  }

  /**
   * Mark daily tasks as completed for today
   */
  static markDailyTasksCompleted(): void {
    try {
      const today = new Date().toDateString()
      localStorage.setItem('lastDailyTasksRun', today)
      console.log(`✅ Daily tasks marked as completed for ${today}`)
    } catch (error) {
      console.error('Error marking daily tasks as completed:', error)
    }
  }

  /**
   * Run daily tasks if needed (automatic scheduler)
   */
  static async runDailyTasksIfNeeded(): Promise<boolean> {
    try {
      if (this.shouldRunDailyTasks()) {
        console.log('🔄 Running scheduled daily tasks...')
        const result = await this.processDailyTasks()
        
        if (result.success) {
          this.markDailyTasksCompleted()
          return true
        } else {
          console.error('❌ Daily tasks failed, will retry next time')
          return false
        }
      } else {
        console.log('✅ Daily tasks already completed today')
        return true
      }
    } catch (error) {
      console.error('❌ Error in automatic daily tasks:', error)
      return false
    }
  }

  /**
   * Force run daily tasks (admin function)
   */
  static async forceRunDailyTasks(): Promise<any> {
    try {
      console.log('🔧 Force running daily tasks...')
      const result = await this.processDailyTasks()
      
      if (result.success) {
        this.markDailyTasksCompleted()
      }
      
      return result
    } catch (error) {
      console.error('❌ Error in force running daily tasks:', error)
      throw error
    }
  }

  /**
   * Get daily tasks status
   */
  static getDailyTasksStatus(): {
    lastRun: string | null
    shouldRun: boolean
    nextRun: string
  } {
    try {
      const lastRun = localStorage.getItem('lastDailyTasksRun')
      const shouldRun = this.shouldRunDailyTasks()
      
      // Calculate next run time (tomorrow)
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(0, 0, 0, 0)
      
      return {
        lastRun,
        shouldRun,
        nextRun: tomorrow.toISOString()
      }
    } catch (error) {
      console.error('Error getting daily tasks status:', error)
      return {
        lastRun: null,
        shouldRun: true,
        nextRun: new Date().toISOString()
      }
    }
  }
}

// Export convenience functions
export const processDailyTasks = DailyScheduler.processDailyTasks
export const runDailyTasksIfNeeded = DailyScheduler.runDailyTasksIfNeeded
export const forceRunDailyTasks = DailyScheduler.forceRunDailyTasks
export const getDailyTasksStatus = DailyScheduler.getDailyTasksStatus

// DISABLED: Auto-run daily tasks when this module is imported
// This was causing duplicate runs with DailyActiveDaysScheduler component
// Only the DailyActiveDaysScheduler component should handle automatic scheduling
/*
if (typeof window !== 'undefined') {
  // Run in browser environment
  setTimeout(() => {
    DailyScheduler.runDailyTasksIfNeeded().catch(error => {
      console.error('Error in auto-scheduled daily tasks:', error)
    })
  }, 5000) // Wait 5 seconds after page load
}
*/
