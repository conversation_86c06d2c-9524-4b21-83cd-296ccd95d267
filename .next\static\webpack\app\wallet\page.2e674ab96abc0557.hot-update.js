"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./src/app/wallet/page.tsx":
/*!*********************************!*\
  !*** ./src/app/wallet/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WalletPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useBlockingNotifications */ \"(app-pages-browser)/./src/hooks/useBlockingNotifications.ts\");\n/* harmony import */ var _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useLeaveMonitor */ \"(app-pages-browser)/./src/hooks/useLeaveMonitor.ts\");\n/* harmony import */ var _lib_dataService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/dataService */ \"(app-pages-browser)/./src/lib/dataService.ts\");\n/* harmony import */ var _components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/BlockingNotificationModal */ \"(app-pages-browser)/./src/components/BlockingNotificationModal.tsx\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sweetalert2 */ \"(app-pages-browser)/./node_modules/sweetalert2/dist/sweetalert2.all.js\");\n/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_8__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth)();\n    const { hasBlockingNotifications, isChecking, markAllAsRead } = (0,_hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications)((user === null || user === void 0 ? void 0 : user.uid) || null);\n    const { isBlocked: isLeaveBlocked, leaveStatus } = (0,_hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor)({\n        userId: (user === null || user === void 0 ? void 0 : user.uid) || null,\n        checkInterval: 30000,\n        enabled: !!user\n    });\n    const [walletData, setWalletData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dataLoading, setDataLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWithdrawing, setIsWithdrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastWithdrawalAttempt, setLastWithdrawalAttempt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [bufferCountdown, setBufferCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Bank details state\n    const [bankDetails, setBankDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showBankForm, setShowBankForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bankFormData, setBankFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        accountHolderName: '',\n        accountNumber: '',\n        ifscCode: '',\n        bankName: ''\n    });\n    const [isSavingBank, setIsSavingBank] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [withdrawalAllowed, setWithdrawalAllowed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        allowed: true\n    });\n    const [checkingWithdrawal, setCheckingWithdrawal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            if (user) {\n                loadWalletData();\n                loadBankDetails();\n                loadUserData();\n                checkWithdrawalEligibility();\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        user\n    ]);\n    // Buffer countdown timer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            let interval = null;\n            if (bufferCountdown > 0) {\n                interval = setInterval({\n                    \"WalletPage.useEffect\": ()=>{\n                        setBufferCountdown({\n                            \"WalletPage.useEffect\": (prev)=>{\n                                if (prev <= 1) {\n                                    return 0;\n                                }\n                                return prev - 1;\n                            }\n                        }[\"WalletPage.useEffect\"]);\n                    }\n                }[\"WalletPage.useEffect\"], 1000);\n            }\n            return ({\n                \"WalletPage.useEffect\": ()=>{\n                    if (interval) {\n                        clearInterval(interval);\n                    }\n                }\n            })[\"WalletPage.useEffect\"];\n        }\n    }[\"WalletPage.useEffect\"], [\n        bufferCountdown\n    ]);\n    // Monitor leave status changes and update withdrawal eligibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            if (isLeaveBlocked) {\n                setWithdrawalAllowed({\n                    allowed: false,\n                    reason: leaveStatus.reason || 'Withdrawals are not available due to leave.'\n                });\n            } else if (user) {\n                // Re-check withdrawal eligibility when leave status changes\n                checkWithdrawalEligibility();\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        isLeaveBlocked,\n        leaveStatus,\n        user\n    ]);\n    const loadUserData = async ()=>{\n        try {\n            const data = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserData)(user.uid);\n            setUserData(data);\n        } catch (error) {\n            console.error('Error loading user data:', error);\n        }\n    };\n    const checkWithdrawalEligibility = async ()=>{\n        if (!user) return;\n        try {\n            setCheckingWithdrawal(true);\n            const result = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.checkWithdrawalAllowed)(user.uid);\n            setWithdrawalAllowed(result);\n        } catch (error) {\n            console.error('Error checking withdrawal eligibility:', error);\n            setWithdrawalAllowed({\n                allowed: false,\n                reason: 'Unable to verify withdrawal eligibility. Please try again.'\n            });\n        } finally{\n            setCheckingWithdrawal(false);\n        }\n    };\n    const loadWalletData = async ()=>{\n        try {\n            setDataLoading(true);\n            const [walletResult, withdrawalsResult] = await Promise.all([\n                (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getWalletData)(user.uid),\n                (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getUserWithdrawals)(user.uid, 20)\n            ]);\n            setWalletData(walletResult);\n            // Convert withdrawals to transaction format for display\n            const withdrawalTransactions = withdrawalsResult.map((withdrawal)=>({\n                    id: withdrawal.id,\n                    type: 'withdrawal',\n                    amount: -withdrawal.amount,\n                    description: \"Withdrawal request - ₹\".concat(withdrawal.amount),\n                    date: withdrawal.date,\n                    status: withdrawal.status\n                }));\n            setTransactions(withdrawalTransactions);\n        } catch (error) {\n            console.error('Error loading wallet data:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Error',\n                text: 'Failed to load wallet data. Please try again.'\n            });\n        } finally{\n            setDataLoading(false);\n        }\n    };\n    const loadBankDetails = async ()=>{\n        try {\n            const bankData = await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.getBankDetails)(user.uid);\n            setBankDetails(bankData);\n            if (bankData) {\n                setBankFormData(bankData);\n            }\n        } catch (error) {\n            console.error('Error loading bank details:', error);\n        }\n    };\n    const handleBankFormSubmit = async (e)=>{\n        e.preventDefault();\n        // Prevent multiple submissions\n        if (isSavingBank) return;\n        try {\n            setIsSavingBank(true);\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.saveBankDetails)(user.uid, bankFormData);\n            setBankDetails(bankFormData);\n            setShowBankForm(false);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'success',\n                title: 'Bank Details Saved',\n                text: 'Your bank details have been saved successfully',\n                timer: 2000,\n                showConfirmButton: false\n            });\n        } catch (error) {\n            console.error('Error saving bank details:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Error',\n                text: error.message || 'Failed to save bank details. Please try again.'\n            });\n        } finally{\n            setIsSavingBank(false);\n        }\n    };\n    const handleBankFormChange = (e)=>{\n        const { name, value } = e.target;\n        setBankFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleWithdraw = async ()=>{\n        const now = Date.now();\n        const WITHDRAWAL_BUFFER_TIME = 3000 // 3 seconds buffer between attempts\n        ;\n        // Prevent multiple clicks with buffer time\n        if (isWithdrawing) {\n            console.log('⚠️ Withdrawal already in progress, ignoring click');\n            return;\n        }\n        // Check buffer time to prevent rapid successive clicks\n        if (now - lastWithdrawalAttempt < WITHDRAWAL_BUFFER_TIME) {\n            const remainingTime = Math.ceil((WITHDRAWAL_BUFFER_TIME - (now - lastWithdrawalAttempt)) / 1000);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'warning',\n                title: 'Please Wait',\n                text: \"Please wait \".concat(remainingTime, \" more second(s) before attempting another withdrawal.\"),\n                timer: 2000,\n                showConfirmButton: false\n            });\n            return;\n        }\n        // Update last attempt timestamp\n        setLastWithdrawalAttempt(now);\n        // Check if withdrawals are blocked due to leave\n        if (isLeaveBlocked) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'warning',\n                title: 'Withdrawal Not Available',\n                text: leaveStatus.reason || 'Withdrawals are not available due to leave.',\n                confirmButtonText: 'OK'\n            });\n            return;\n        }\n        const amount = parseFloat(withdrawAmount);\n        if (!amount || amount <= 0) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Invalid Amount',\n                text: 'Please enter a valid amount to withdraw'\n            });\n            return;\n        }\n        if (amount < 50) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Minimum Withdrawal',\n                text: 'Minimum withdrawal amount is ₹50'\n            });\n            return;\n        }\n        if (amount > ((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0)) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Insufficient Balance',\n                text: 'You do not have enough balance in your wallet'\n            });\n            return;\n        }\n        if (!bankDetails) {\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'warning',\n                title: 'Bank Details Required',\n                text: 'Please add your bank details before making a withdrawal'\n            });\n            return;\n        }\n        try {\n            setIsWithdrawing(true);\n            // Create withdrawal request in Firestore\n            await (0,_lib_dataService__WEBPACK_IMPORTED_MODULE_6__.createWithdrawalRequest)(user.uid, amount, bankDetails);\n            // Reload wallet data to show updated transactions\n            await loadWalletData();\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'success',\n                title: 'Withdrawal Request Submitted',\n                text: \"Your withdrawal request for ₹\".concat(amount, \" has been submitted and will be processed within 24-48 hours.\")\n            });\n            setWithdrawAmount('');\n        } catch (error) {\n            console.error('Error processing withdrawal:', error);\n            sweetalert2__WEBPACK_IMPORTED_MODULE_8___default().fire({\n                icon: 'error',\n                title: 'Withdrawal Failed',\n                text: error.message || 'Failed to process withdrawal request. Please try again.'\n            });\n        } finally{\n            setIsWithdrawing(false);\n        }\n    };\n    const formatCurrency = (amount)=>{\n        if (amount === undefined || amount === null || isNaN(amount)) {\n            return '₹0.00';\n        }\n        return \"₹\".concat(amount.toFixed(2));\n    };\n    if (loading || dataLoading || isChecking) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading wallet...'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n            lineNumber: 324,\n            columnNumber: 7\n        }, this);\n    }\n    // Show blocking notifications if any exist\n    if (hasBlockingNotifications && user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BlockingNotificationModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            userId: user.uid,\n            onAllRead: markAllAsRead\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"glass-card p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/dashboard\",\n                            className: \"glass-button px-4 py-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-arrow-left mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                \"Back to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"My Wallet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadWalletData,\n                            className: \"glass-button px-4 py-2 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"fas fa-sync-alt mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-semibold text-white\",\n                                children: \"My Wallet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-wallet text-green-400 text-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-4xl font-bold text-green-400 mb-2\",\n                        children: formatCurrency((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60\",\n                        children: \"Total available balance\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-university mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this),\n                            bankDetails && !showBankForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-edit mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    !bankDetails && !showBankForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-university text-white/30 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-4\",\n                                children: \"No bank details added yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-plus mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, this) : showBankForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleBankFormSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Account Holder Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"accountHolderName\",\n                                                value: bankFormData.accountHolderName,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter account holder name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Bank Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"bankName\",\n                                                value: bankFormData.bankName,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter bank name\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"Account Number *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"accountNumber\",\n                                                value: bankFormData.accountNumber,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter account number\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-white font-medium mb-2\",\n                                                children: \"IFSC Code *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                name: \"ifscCode\",\n                                                value: bankFormData.ifscCode,\n                                                onChange: handleBankFormChange,\n                                                className: \"form-input\",\n                                                placeholder: \"Enter IFSC code (e.g., SBIN0001234)\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSavingBank,\n                                        className: \"\".concat(isSavingBank ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-primary hover:bg-blue-600'),\n                                        children: isSavingBank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Saving Bank Details...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-save mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Save Bank Details\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowBankForm(false),\n                                        className: \"btn-secondary\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/10 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Account Holder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.accountHolderName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Bank Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.bankName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Account Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: [\n                                                \"****\",\n                                                bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.accountNumber.slice(-4)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"IFSC Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: bankDetails === null || bankDetails === void 0 ? void 0 : bankDetails.ifscCode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-money-bill-wave mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this),\n                            \"Withdraw Funds\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 9\n                    }, this),\n                    (userData === null || userData === void 0 ? void 0 : userData.plan) === 'Trial' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-lock text-red-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-400 font-medium\",\n                                        children: \"Trial Plan Restriction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm mb-3\",\n                                children: \"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/plans\",\n                                    className: \"btn-primary inline-block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-arrow-up mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upgrade Plan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 529,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-clock text-blue-400 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-400 font-medium\",\n                                        children: \"Withdrawal Timings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm mb-2\",\n                                children: [\n                                    \"Withdrawals are only allowed between \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"10:00 AM to 6:00 PM\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 50\n                                    }, this),\n                                    \" on non-leave days.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-xs\",\n                                        children: [\n                                            \"Current time: \",\n                                            new Date().toLocaleTimeString(),\n                                            \" | Status: \",\n                                            withdrawalAllowed.allowed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-400 font-medium\",\n                                                children: \"✓ Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400 font-medium\",\n                                                children: \"✗ Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: checkWithdrawalEligibility,\n                                        disabled: checkingWithdrawal,\n                                        className: \"text-blue-400 hover:text-blue-300 text-xs\",\n                                        children: checkingWithdrawal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"spinner w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-sync-alt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 11\n                            }, this),\n                            !withdrawalAllowed.allowed && withdrawalAllowed.reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-400 text-sm mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-exclamation-triangle mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this),\n                                    withdrawalAllowed.reason\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 9\n                    }, this),\n                    !bankDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-4\",\n                                children: \"Please add your bank details before making a withdrawal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBankForm(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-university mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Bank Details\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 586,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        value: withdrawAmount,\n                                        onChange: (e)=>setWithdrawAmount(e.target.value),\n                                        placeholder: \"Enter amount to withdraw (Min: ₹50)\",\n                                        className: \"form-input flex-1\",\n                                        min: \"50\",\n                                        max: (walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleWithdraw,\n                                        disabled: isWithdrawing || !withdrawAmount || !withdrawalAllowed.allowed,\n                                        onKeyDown: (e)=>{\n                                            // Prevent keyboard interactions when disabled or processing\n                                            if (isWithdrawing || !withdrawAmount || !withdrawalAllowed.allowed) {\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            }\n                                        },\n                                        className: \"whitespace-nowrap transition-all duration-200 \".concat(isWithdrawing ? 'btn-disabled cursor-not-allowed opacity-50 pointer-events-none' : withdrawalAllowed.allowed && withdrawAmount ? 'btn-primary hover:bg-blue-600 active:scale-95' : 'btn-disabled cursor-not-allowed opacity-50'),\n                                        style: {\n                                            pointerEvents: isWithdrawing ? 'none' : 'auto'\n                                        },\n                                        children: isWithdrawing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"spinner mr-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Processing Withdrawal...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-download mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Withdraw ₹\",\n                                                withdrawAmount || '0'\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 text-sm mt-2\",\n                                children: [\n                                    \"Available: \",\n                                    formatCurrency((walletData === null || walletData === void 0 ? void 0 : walletData.wallet) || 0),\n                                    \" | Minimum: ₹50\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 643,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 521,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass-card p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-money-bill-wave mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Withdrawal History\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadWalletData,\n                                className: \"glass-button px-4 py-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-sync-alt mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 652,\n                        columnNumber: 9\n                    }, this),\n                    transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-money-bill-wave text-white/30 text-4xl mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60 mb-2\",\n                                children: \"No withdrawal requests yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/40 text-sm\",\n                                children: \"Your withdrawal requests will appear here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: transactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white/10 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-money-bill-wave text-red-400 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: transaction.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: [\n                                                            transaction.date.toLocaleDateString(),\n                                                            \" at \",\n                                                            transaction.date.toLocaleTimeString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-bold text-red-400\",\n                                                children: formatCurrency(Math.abs(transaction.amount))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs px-2 py-1 rounded-full \".concat(transaction.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' : transaction.status === 'approved' ? 'bg-green-500/20 text-green-400' : transaction.status === 'rejected' ? 'bg-red-500/20 text-red-400' : transaction.status === 'completed' ? 'bg-blue-500/20 text-blue-400' : 'bg-gray-500/20 text-gray-400'),\n                                                children: transaction.status === 'pending' ? '⏳ Pending' : transaction.status === 'approved' ? '✅ Approved' : transaction.status === 'rejected' ? '❌ Rejected' : transaction.status === 'completed' ? '✅ Completed' : transaction.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, transaction.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n                lineNumber: 651,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\wallet\\\\page.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"BfOyfpeXcxF6CwmzPphFYRFo5Qk=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useRequireAuth,\n        _hooks_useBlockingNotifications__WEBPACK_IMPORTED_MODULE_4__.useBlockingNotifications,\n        _hooks_useLeaveMonitor__WEBPACK_IMPORTED_MODULE_5__.useLeaveMonitor\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/wallet/page.tsx\n"));

/***/ })

});