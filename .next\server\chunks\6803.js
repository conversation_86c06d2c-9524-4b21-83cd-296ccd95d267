exports.id=6803,exports.ids=[6803],exports.modules={3152:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\ErrorBoundary.tsx","default")},8058:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),a=s(43210),n=s(87979);function i({children:e}){let[t,s]=(0,a.useState)(!0),[i,o]=(0,a.useState)(!1),[l,c]=(0,a.useState)(null),{user:d}=(0,n.hD)();return i?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4",children:(0,r.jsx)("div",{className:"max-w-md w-full",children:(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center border border-white/20",children:[(0,r.jsx)("div",{className:"mx-auto w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mb-6",children:(0,r.jsx)("i",{className:"fas fa-wifi-slash text-red-400 text-3xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"You're Offline"}),(0,r.jsx)("p",{className:"text-white/80 mb-6 leading-relaxed",children:"Your internet connection seems to be down. Don't worry - your work progress is safely saved locally and will be restored when you're back online."}),(0,r.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-white/60",children:"Connection Status:"}),(0,r.jsxs)("span",{className:"text-red-400 font-medium",children:[(0,r.jsx)("i",{className:"fas fa-circle text-xs mr-1"}),"Offline"]})]}),l&&(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,r.jsx)("span",{className:"text-white/60",children:"Offline Since:"}),(0,r.jsx)("span",{className:"text-white/80",children:l.toLocaleTimeString()})]}),d&&(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,r.jsx)("span",{className:"text-white/60",children:"User Session:"}),(0,r.jsxs)("span",{className:"text-green-400 font-medium",children:[(0,r.jsx)("i",{className:"fas fa-check text-xs mr-1"}),"Protected"]})]})]}),(0,r.jsxs)("div",{className:"text-left mb-6",children:[(0,r.jsx)("h3",{className:"text-white font-semibold mb-3 text-center",children:"Available Offline:"}),(0,r.jsxs)("ul",{className:"space-y-2 text-white/80 text-sm",children:[(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"Continue current translation work"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"Auto-save progress locally"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-check text-green-400 mr-2"}),"View completed translations"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-times text-red-400 mr-2"}),"Submit translations for earnings"]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{onClick:()=>window.location.reload(),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Check Connection"]}),(0,r.jsx)("button",{onClick:()=>o(!1),className:"w-full bg-white/10 hover:bg-white/20 text-white font-semibold py-3 px-6 rounded-lg transition-colors border border-white/20",children:"Continue Offline"})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/20",children:(0,r.jsxs)("p",{className:"text-yellow-200 text-xs",children:[(0,r.jsx)("i",{className:"fas fa-lightbulb mr-1"}),(0,r.jsx)("strong",{children:"Tip:"})," Your work is automatically saved every 10 seconds. When you reconnect, everything will sync automatically."]})})]})})}):(0,r.jsxs)("div",{className:"relative",children:[!t&&(0,r.jsxs)("div",{className:"fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 text-sm z-50",children:[(0,r.jsx)("i",{className:"fas fa-wifi-slash mr-2"}),"You're offline - work is saved locally",(0,r.jsx)("button",{onClick:()=>o(!0),className:"ml-4 underline hover:no-underline",children:"View Details"})]}),(0,r.jsx)("div",{className:t?"":"pt-10",children:e})]})}},9670:(e,t,s)=>{Promise.resolve().then(s.bind(s,25243)),Promise.resolve().then(s.bind(s,95758)),Promise.resolve().then(s.bind(s,8058)),Promise.resolve().then(s.bind(s,45016)),Promise.resolve().then(s.bind(s,62694))},10444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.t.bind(s,46533,23))},14329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(60687);s(43210);var a=s(85814),n=s.n(a),i=s(30474);function o({error:e,reset:t}){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)(i.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:80,height:80,className:"mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"Oops!"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-white/80 mb-8 max-w-md mx-auto",children:"We encountered an unexpected error. Please try again or contact support if the problem persists."}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("p",{className:"text-white/60 mb-4",children:"Need immediate help?"}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-envelope mr-2"}),"Email Support"]})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("button",{onClick:t,className:"btn-primary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-redo mr-2"}),"Try Again"]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(n(),{href:"/",className:"btn-secondary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-home mr-2"}),"Go Home"]}),(0,r.jsxs)(n(),{href:"/dashboard",className:"btn-secondary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-tachometer-alt mr-2"}),"Dashboard"]})]})]}),!1]})})}},20716:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.t.bind(s,49603,23))},22349:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},25243:(e,t,s)=>{"use strict";function r(){return null}s.d(t,{default:()=>r}),s(43210),s(51705),s(27878)},26700:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\OfflineSupport.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\OfflineSupport.tsx","default");(0,r.registerClientReference)(function(){throw Error("Attempted to call useOfflineStatus() from the server but useOfflineStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\OfflineSupport.tsx","useOfflineStatus")},26974:(e,t,s)=>{Promise.resolve().then(s.bind(s,39769)),Promise.resolve().then(s.bind(s,3152)),Promise.resolve().then(s.bind(s,26700)),Promise.resolve().then(s.bind(s,98046)),Promise.resolve().then(s.bind(s,51404))},27878:(e,t,s)=>{"use strict";s.d(t,{Mk:()=>m,checkCopyPastePermission:()=>l,grantCopyPastePermission:()=>c,i7:()=>u,removeCopyPastePermission:()=>d});var r=s(33784),a=s(75535);let n={quickTranslationAdvantageExpiry:"quickTranslationAdvantageExpiry",lastCopyPasteReduction:"lastCopyPasteReduction"},i={users:"users"};class o{static async checkCopyPastePermission(e){try{let t=await (0,a.x7)((0,a.H9)(r.db,i.users,e));if(!t.exists())return{hasPermission:!1,daysRemaining:0,expiryDate:null};let s=t.data()[n.quickTranslationAdvantageExpiry];if(!s)return{hasPermission:!1,daysRemaining:0,expiryDate:null};let o=s.toDate(),l=new Date,c=o>l,d=c?Math.ceil((o.getTime()-l.getTime())/864e5):0;return{hasPermission:c,daysRemaining:d,expiryDate:o}}catch(t){return console.error(`Error checking copy-paste permission for user ${e}:`,t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}static async grantCopyPastePermission(e,t){try{let s=new Date;s.setDate(s.getDate()+t);let o=(0,a.H9)(r.db,i.users,e);await (0,a.mZ)(o,{[n.quickTranslationAdvantageExpiry]:a.Dc.fromDate(s),[n.lastCopyPasteReduction]:a.Dc.now()}),console.log(`✅ Granted copy-paste permission to user ${e} for ${t} days (expires: ${s.toDateString()})`)}catch(t){throw console.error(`Error granting copy-paste permission to user ${e}:`,t),t}}static async removeCopyPastePermission(e){try{let t=(0,a.H9)(r.db,i.users,e);await (0,a.mZ)(t,{[n.quickTranslationAdvantageExpiry]:null}),console.log(`✅ Removed copy-paste permission from user ${e}`)}catch(t){throw console.error(`Error removing copy-paste permission from user ${e}:`,t),t}}static async reduceCopyPasteDays(e){try{let t=await (0,a.x7)((0,a.H9)(r.db,i.users,e));if(!t.exists())return{reduced:!1,daysRemaining:0,expired:!1};let s=t.data(),o=s[n.quickTranslationAdvantageExpiry],l=s[n.lastCopyPasteReduction];if(!o)return{reduced:!1,daysRemaining:0,expired:!1};let c=new Date().toDateString();if((l?l.toDate().toDateString():null)===c){let e=o.toDate(),t=new Date,s=Math.max(0,Math.ceil((e.getTime()-t.getTime())/864e5));return{reduced:!1,daysRemaining:s,expired:0===s}}let d=o.toDate(),u=new Date(d);u.setDate(u.getDate()-1);let m=(0,a.H9)(r.db,i.users,e);if(u<=new Date)return await (0,a.mZ)(m,{[n.quickTranslationAdvantageExpiry]:null,[n.lastCopyPasteReduction]:a.Dc.now()}),console.log(`📅 Copy-paste permission expired for user ${e}`),{reduced:!0,daysRemaining:0,expired:!0};{await (0,a.mZ)(m,{[n.quickTranslationAdvantageExpiry]:a.Dc.fromDate(u),[n.lastCopyPasteReduction]:a.Dc.now()});let t=Math.ceil((u.getTime()-new Date().getTime())/864e5);return console.log(`📅 Reduced copy-paste days for user ${e}: ${t} days remaining`),{reduced:!0,daysRemaining:t,expired:!1}}}catch(t){return console.error(`Error reducing copy-paste days for user ${e}:`,t),{reduced:!1,daysRemaining:0,expired:!1}}}static async processAllUsersCopyPasteReduction(){try{console.log("\uD83D\uDD04 Starting daily copy-paste reduction for all users...");let e=await (0,a.getDocs)((0,a.collection)(r.db,i.users)),t=0,s=0,n=0,l=0;for(let r of e.docs)try{t++;let e=await o.reduceCopyPasteDays(r.id);e.reduced&&(s++,e.expired&&n++)}catch(e){l++,console.error(`Error processing copy-paste reduction for user ${r.id}:`,e)}return console.log(`✅ Daily copy-paste reduction complete:`),console.log(`   - Processed: ${t} users`),console.log(`   - Reduced: ${s} users`),console.log(`   - Expired: ${n} users`),console.log(`   - Errors: ${l} users`),{processed:t,reduced:s,expired:n,errors:l}}catch(e){throw console.error("Error in daily copy-paste reduction processing:",e),e}}static async getCopyPasteStatus(e){try{let t=await o.checkCopyPastePermission(e);return{hasPermission:t.hasPermission,daysRemaining:t.daysRemaining,expiryDate:t.expiryDate?t.expiryDate.toDateString():null}}catch(t){return console.error(`Error getting copy-paste status for user ${e}:`,t),{hasPermission:!1,daysRemaining:0,expiryDate:null}}}}let l=o.checkCopyPastePermission,c=o.grantCopyPastePermission,d=o.removeCopyPastePermission,u=o.reduceCopyPasteDays,m=o.processAllUsersCopyPasteReduction;o.getCopyPasteStatus},33784:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j2:()=>l});var r=s(67989),a=s(63385),n=s(75535),i=s(70146);let o=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyCgnBVg5HcLtBaCJoebDWuzGefhvwnhX68",authDomain:"instra-global.firebaseapp.com",projectId:"instra-global",storageBucket:"instra-global.firebasestorage.app",messagingSenderId:"725774700748",appId:"1:725774700748:web:4cdac03d835a7e2e133269",measurementId:"G-QGHBLY3DLQ"}),l=(0,a.xI)(o),c=(0,n.aU)(o);(0,i.c7)(o)},39769:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\DailyActiveDaysScheduler.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\DailyActiveDaysScheduler.tsx","default")},45016:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(60687),a=s(43210);function n(){let[e,t]=(0,a.useState)(null),[s,n]=(0,a.useState)(!1),i=async()=>{if(!e)return;e.prompt();let{outcome:s}=await e.userChoice;"accepted"===s?console.log("User accepted the install prompt"):console.log("User dismissed the install prompt"),t(null),n(!1)};return s?(0,r.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,r.jsxs)("button",{onClick:i,className:"glass-button px-4 py-3 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Install App"]})}):null}},51278:(e,t,s)=>{"use strict";s.d(t,{M4:()=>o,_f:()=>i});var r=s(33784),a=s(77567);function n(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function i(e,t="/login"){try{if((await a.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e),await r.j2.signOut(),a.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),a.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e,t="/login"){try{e&&n(e),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},51404:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\UpdateManager.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\UpdateManager.tsx","default")},51705:(e,t,s)=>{"use strict";s.d(t,{S3:()=>m,i7:()=>l,mH:()=>d,nd:()=>u,updateUserActiveDays:()=>c});var r=s(33784),a=s(75535);let n={activeDays:"activeDays",lastActiveDayUpdate:"lastActiveDayUpdate"},i={users:"users"};class o{static async calculateActiveDays(e){try{let t,s=await (0,a.x7)((0,a.H9)(r.db,i.users,e));if(!s.exists())return console.error(`User ${e} not found`),{activeDays:0,shouldUpdate:!1,isNewDay:!1};let n=s.data(),l=n.joinedDate?.toDate()||new Date,c=n.lastActiveDayUpdate?.toDate(),d=n.activeDays||0,u=n.plan||"Trial",m=new Date,h=m.toDateString(),p=c?c.toDateString():null;if(console.log(`📅 Calculating active days for user ${e}:`),console.log(`   - Joined: ${l.toDateString()}`),console.log(`   - Current active days: ${d}`),console.log(`   - Last update: ${p||"Never"}`),console.log(`   - Today: ${h}`),console.log(`   - Plan: ${u}`),p===h)return console.log(`✅ Already updated today for user ${e}`),{activeDays:d,shouldUpdate:!1,isNewDay:!1};if("Admin"===u)return console.log(`⏭️ Skipping active days increment for admin user ${e}`),await o.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};if(await o.isUserOnLeaveToday(e))return console.log(`🏖️ User ${e} is on leave today, not incrementing active days`),await o.updateLastActiveDayUpdate(e),{activeDays:d,shouldUpdate:!1,isNewDay:!0};return t="Trial"===u?Math.floor((m.getTime()-l.getTime())/864e5)+1:d+1,console.log(`📈 New active days calculated: ${d} → ${t}`),{activeDays:t,shouldUpdate:t!==d,isNewDay:!0}}catch(t){return console.error(`Error calculating active days for user ${e}:`,t),{activeDays:0,shouldUpdate:!1,isNewDay:!1}}}static async updateUserActiveDays(e){try{let t=await o.calculateActiveDays(e);if(t.shouldUpdate){let s=(0,a.H9)(r.db,i.users,e);await (0,a.mZ)(s,{[n.activeDays]:t.activeDays,[n.lastActiveDayUpdate]:a.Dc.now()}),console.log(`✅ Updated active days for user ${e}: ${t.activeDays}`)}else t.isNewDay&&await o.updateLastActiveDayUpdate(e);return t.activeDays}catch(t){throw console.error(`Error updating active days for user ${e}:`,t),t}}static async updateLastActiveDayUpdate(e){try{let t=(0,a.H9)(r.db,i.users,e);await (0,a.mZ)(t,{[n.lastActiveDayUpdate]:a.Dc.now()})}catch(t){console.error(`Error updating last active day timestamp for user ${e}:`,t)}}static async isUserOnLeaveToday(e){try{let{isUserOnLeave:t}=await s.e(7087).then(s.bind(s,87087));return await t(e,new Date)}catch(t){return console.error(`Error checking leave status for user ${e}:`,t),!1}}static async processAllUsersActiveDays(){try{console.log("\uD83D\uDD04 Starting daily active days processing for all users...");let e=await (0,a.getDocs)((0,a.collection)(r.db,i.users)),t=0,s=0,n=0;for(let r of e.docs)try{t++;let e=await o.calculateActiveDays(r.id);(e.shouldUpdate||e.isNewDay)&&(await o.updateUserActiveDays(r.id),e.shouldUpdate&&s++)}catch(e){n++,console.error(`Error processing active days for user ${r.id}:`,e)}return console.log(`✅ Daily active days processing complete:`),console.log(`   - Processed: ${t} users`),console.log(`   - Updated: ${s} users`),console.log(`   - Errors: ${n} users`),{processed:t,updated:s,errors:n}}catch(e){throw console.error("Error in daily active days processing:",e),e}}static async getUserActiveDays(e){try{let t=await (0,a.x7)((0,a.H9)(r.db,i.users,e));if(!t.exists())return 0;return t.data().activeDays||0}catch(t){return console.error(`Error getting active days for user ${e}:`,t),0}}static async initializeActiveDaysForNewUser(e){try{let t=(0,a.H9)(r.db,i.users,e);await (0,a.mZ)(t,{[n.activeDays]:1,[n.lastActiveDayUpdate]:a.Dc.now()}),console.log(`✅ Initialized active days for new user ${e}: Day 1`)}catch(t){throw console.error(`Error initializing active days for user ${e}:`,t),t}}}let l=o.calculateActiveDays,c=o.updateUserActiveDays,d=o.processAllUsersActiveDays,u=o.getUserActiveDays,m=o.initializeActiveDaysForNewUser},54413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(37413),a=s(4536),n=s.n(a),i=s(53384);function o(){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)(i.default,{src:"/img/instra-logo.svg",alt:"Instra Global Logo",width:80,height:80,className:"mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-6xl font-bold text-white mb-4",children:"404"}),(0,r.jsx)("h2",{className:"text-2xl font-semibold text-white mb-2",children:"Page Not Found"}),(0,r.jsx)("p",{className:"text-white/80 mb-8 max-w-md mx-auto",children:"The page you're looking for doesn't exist or has been moved."}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("p",{className:"text-white/60 mb-4",children:"Need help finding what you're looking for?"}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-envelope mr-2"}),"Email Support"]})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(n(),{href:"/",className:"btn-primary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-home mr-2"}),"Go Home"]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(n(),{href:"/dashboard",className:"btn-secondary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-tachometer-alt mr-2"}),"Dashboard"]}),(0,r.jsxs)(n(),{href:"/work",className:"btn-secondary inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-play-circle mr-2"}),"Watch Videos"]})]})]})]})})}},54431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx","default")},61135:()=>{},62694:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),a=s(43210),n=s(77567);function i({children:e}){let[t,s]=(0,a.useState)(!1),[i,o]=(0,a.useState)(null),[l,c]=(0,a.useState)(""),d=async()=>{if(!i||!i.waiting)return void console.log("No waiting service worker found");try{n.A.fire({title:"Updating App...",text:"Please wait while we update the app.",allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1,didOpen:()=>{n.A.showLoading()}}),i.waiting.postMessage({type:"SKIP_WAITING"}),navigator.serviceWorker.addEventListener("controllerchange",()=>{console.log("\uD83D\uDD04 New service worker took control, reloading..."),"caches"in window&&caches.keys().then(e=>{e.forEach(e=>{e.includes("instra-global")&&caches.delete(e)})}),window.location.reload()})}catch(e){console.error("Error applying update:",e),n.A.fire({icon:"error",title:"Update Failed",text:"Failed to update the app. Please refresh the page manually.",confirmButtonText:"Refresh Page"}).then(()=>{window.location.reload()})}};return(0,r.jsxs)(r.Fragment,{children:[e,t&&(0,r.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,r.jsxs)("button",{onClick:d,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 animate-pulse",children:[(0,r.jsx)("i",{className:"fas fa-download"}),(0,r.jsx)("span",{children:"Update Available"})]})}),!1]})}},67393:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(37413);function a(){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white/80",children:"Loading Instra Global..."})]})})}},69613:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},70038:(e,t,s)=>{"use strict";s.d(t,{i:()=>i});var r=s(33784),a=s(63385);class n{constructor(){this.sessionData=null,this.heartbeatInterval=null,this.saveInterval=null,this.authStateListener=null,this.onlineListener=null,this.offlineListener=null,this.initializeSession()}static getInstance(){return n.instance||(n.instance=new n),n.instance}initializeSession(){console.log("Session Manager: Server-side rendering detected, skipping initialization")}setupAuthStateListener(){this.authStateListener=(0,a.hg)(r.j2,e=>{console.log("Auth state changed:",e?`User ${e.uid}`:"No user"),this.sessionData&&(this.sessionData.user=e,this.sessionData.lastActivity=new Date,e?this.restoreWorkProgress(e.uid):this.saveCurrentProgress(),this.saveSession())},e=>{console.error("Auth state listener error:",e)})}setupNetworkListeners(){}setupPeriodicSave(){this.saveInterval=setInterval(()=>{this.saveSession(),this.saveCurrentProgress()},3e4)}setupHeartbeat(){this.heartbeatInterval=setInterval(()=>{this.sessionData&&(this.sessionData.lastActivity=new Date,this.saveSession())},6e4)}saveWorkProgress(e){this.sessionData?.user}getWorkProgress(){return this.sessionData?.user,null}clearWorkProgress(){this.sessionData?.user}restoreWorkProgress(e){}saveCurrentProgress(){this.sessionData?.workProgress}saveSession(){this.sessionData}restoreSession(){}syncPendingData(){console.log("Syncing pending data...")}updateActivity(){this.sessionData&&(this.sessionData.lastActivity=new Date)}isOnline(){return"undefined"!=typeof navigator?navigator.onLine:this.sessionData?.isOnline||!0}getSessionId(){return this.sessionData?.sessionId||""}getCurrentUser(){return this.sessionData?.user||null}cleanup(){this.heartbeatInterval&&clearInterval(this.heartbeatInterval),this.saveInterval&&clearInterval(this.saveInterval),this.authStateListener&&this.authStateListener(),console.log("Session Manager cleaned up")}}let i=n.getInstance()},78335:()=>{},82993:(e,t,s)=>{Promise.resolve().then(s.bind(s,14329))},87979:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>i,hD:()=>n,wC:()=>o});var r=s(43210);s(63385),s(33784);var a=s(51278);function n(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!0),[i,o]=(0,r.useState)(!0),l=async()=>{try{await (0,a.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:l,isOnline:i}}function i(){let{user:e,loading:t,isOnline:s}=n(),[a,i]=(0,r.useState)(null);return{user:e,loading:t,isOnline:s}}function o(){let{user:e,loading:t}=n(),[s,a]=(0,r.useState)(!1),[i,o]=(0,r.useState)(!0);return{user:e,loading:t||i,isAdmin:s}}s(70038)},92721:(e,t,s)=>{Promise.resolve().then(s.bind(s,54431))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h,metadata:()=>u,viewport:()=>m});var r=s(37413),a=s(77247),n=s.n(a);s(61135);var i=s(98046),o=s(3152),l=s(39769),c=s(26700),d=s(51404);let u={title:"Instra Global - Instant Translation & Earn",description:"Translate text and earn money. Complete daily translation tasks to earn rewards.",keywords:"translation, earn money, online earning, translation tasks, rewards, language services",authors:[{name:"Instra Global Team"}],manifest:"/manifest.json",icons:{icon:"/img/instra-favicon.svg",apple:"/img/instra-favicon.svg"}},m={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,themeColor:"#6A11CB"};function h({children:e}){return(0,r.jsxs)("html",{lang:"en",className:n().variable,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),(0,r.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"}),(0,r.jsx)("script",{src:"https://cdn.jsdelivr.net/npm/sweetalert2@11",async:!0})]}),(0,r.jsxs)("body",{className:`${n().className} antialiased`,children:[(0,r.jsx)("div",{className:"animated-bg"}),(0,r.jsx)(o.default,{children:(0,r.jsx)(d.default,{children:(0,r.jsx)(c.default,{children:e})})}),(0,r.jsx)(i.default,{}),(0,r.jsx)(l.default,{})]})]})}},95758:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(60687),a=s(43210),n=s.n(a);class i extends n().Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"glass-card p-8 text-center max-w-md",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle text-red-400 text-4xl mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-white/80 mb-4",children:"An error occurred while loading this page. Please refresh and try again."}),(0,r.jsxs)("button",{onClick:()=>window.location.reload(),className:"btn-primary",children:[(0,r.jsx)("i",{className:"fas fa-refresh mr-2"}),"Refresh Page"]})]})}):this.props.children}}let o=i},96487:()=>{},98046:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\components\\\\PWAInstaller.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\components\\PWAInstaller.tsx","default")}};