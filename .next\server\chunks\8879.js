"use strict";exports.id=8879,exports.ids=[8879],exports.modules={28879:(e,a,t)=>{t.d(a,{Dt:()=>i,Qy:()=>c,bQ:()=>g,bj:()=>u,cb:()=>r,initializeTranslationSystem:()=>h,jQ:()=>T});let n={CURRENT_BATCH:"instra_translation_current_batch",BATCH_PREFIX:"instra_translation_batch_",TRANSLATION_INDEX:"instra_translation_index",TOTAL_TRANSLATIONS:"instra_total_translations",LAST_PROCESSED:"instra_translation_last_processed"},r=[{code:"hindi",name:"Hindi",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"spanish",name:"Spanish",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"french",name:"French",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"german",name:"German",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"italian",name:"Italian",flag:"\uD83C\uDDEE\uD83C\uDDF9"},{code:"portuguese",name:"Portuguese",flag:"\uD83C\uDDF5\uD83C\uDDF9"},{code:"russian",name:"Russian",flag:"\uD83C\uDDF7\uD83C\uDDFA"},{code:"arabic",name:"Arabic",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"chinese",name:"Chinese (Simplified)",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"japanese",name:"Japanese",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"korean",name:"Korean",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"turkish",name:"Turkish",flag:"\uD83C\uDDF9\uD83C\uDDF7"},{code:"dutch",name:"Dutch",flag:"\uD83C\uDDF3\uD83C\uDDF1"},{code:"swedish",name:"Swedish",flag:"\uD83C\uDDF8\uD83C\uDDEA"},{code:"polish",name:"Polish",flag:"\uD83C\uDDF5\uD83C\uDDF1"},{code:"ukrainian",name:"Ukrainian",flag:"\uD83C\uDDFA\uD83C\uDDE6"},{code:"greek",name:"Greek",flag:"\uD83C\uDDEC\uD83C\uDDF7"},{code:"hebrew",name:"Hebrew",flag:"\uD83C\uDDEE\uD83C\uDDF1"},{code:"vietnamese",name:"Vietnamese",flag:"\uD83C\uDDFB\uD83C\uDDF3"},{code:"thai",name:"Thai",flag:"\uD83C\uDDF9\uD83C\uDDED"}];function o(e){let a=[];return Array.isArray(e)&&e.forEach((e,t)=>{let n=e.English||e.english;n&&a.push({id:`translation_${t}_${Date.now()}`,english:n,hindi:e.Hindi||e.hindi,spanish:e.Spanish||e.spanish,french:e.French||e.french,german:e.German||e.german,italian:e.Italian||e.italian,portuguese:e.Portuguese||e.portuguese,russian:e.Russian||e.russian,arabic:e.Arabic||e.arabic,chinese:e["Chinese (Simplified)"]||e.chinese,japanese:e.Japanese||e.japanese,korean:e.Korean||e.korean,turkish:e.Turkish||e.turkish,dutch:e.Dutch||e.dutch,swedish:e.Swedish||e.swedish,polish:e.Polish||e.polish,ukrainian:e.Ukrainian||e.ukrainian,greek:e.Greek||e.greek,hebrew:e.Hebrew||e.hebrew,vietnamese:e.Vietnamese||e.vietnamese,thai:e.Thai||e.thai,category:"General",batchIndex:Math.floor(a.length/60)})}),a}function l(e){let a=function(e){try{let a=localStorage.getItem(`${n.BATCH_PREFIX}${e}`);if(!a)return null;let t=JSON.parse(a);if(Date.now()-t.lastUpdated>864e5)return localStorage.removeItem(`${n.BATCH_PREFIX}${e}`),null;return t}catch(a){return console.error(`Error loading translation batch ${e}:`,a),null}}(e);return a?a.translations:[]}function i(){let e=parseInt(localStorage.getItem(n.TOTAL_TRANSLATIONS)||"0"),a=parseInt(localStorage.getItem(n.CURRENT_BATCH)||"0"),t=Math.ceil(e/60),r=l(a),o=function(){try{let e=0;for(let a in localStorage)localStorage.hasOwnProperty(a)&&(e+=localStorage[a].length+a.length);let a=e/5242880*100;return{used:e,total:5242880,percentage:a}}catch(e){return{used:0,total:5242880,percentage:0}}}(),i=Object.keys(localStorage).filter(e=>e.startsWith(n.BATCH_PREFIX)).length;return{totalTranslations:e,currentBatch:a,totalBatches:t,translationsInCurrentBatch:r.length,storageUsage:o,cachedBatches:i}}function s(){try{let e=parseInt(localStorage.getItem(n.CURRENT_BATCH)||"0");Object.keys(localStorage).forEach(a=>{a.startsWith(n.BATCH_PREFIX)&&parseInt(a.replace(n.BATCH_PREFIX,""))!==e&&localStorage.removeItem(a)}),console.log("\uD83E\uDDF9 Force cleanup completed - removed all batches except current")}catch(e){console.error("Error in force cleanup:",e)}}function c(){Object.keys(localStorage).forEach(e=>{(e.startsWith(n.BATCH_PREFIX)||Object.values(n).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all translation storage")}async function g(){try{let e=await fetch("/instradata.json");if(!e.ok)throw Error(`Failed to load translations: ${e.statusText}`);let a=await e.json();console.log("Raw translation data loaded:",a.length,"entries");let t=a.slice(0,60);return console.log("Using minimal dataset:",t.length,"entries (optimized for daily usage)"),o(t)}catch(e){throw console.error("Error loading translations from file:",e),e}}async function d(){try{let e=l(parseInt(localStorage.getItem(n.CURRENT_BATCH)||"0"));if(e.length>=50)return console.log("✅ Using cached translations (fast load)"),e;console.log("\uD83D\uDCE6 Loading minimal translation data...");let a=await g();return localStorage.setItem(n.TOTAL_TRANSLATIONS,a.length.toString()),localStorage.setItem(n.CURRENT_BATCH,"0"),localStorage.setItem(n.LAST_PROCESSED,Date.now().toString()),function(e,a){try{var t=0;try{let e=Object.keys(localStorage).filter(e=>e.startsWith(n.BATCH_PREFIX)).sort((e,a)=>{let t=parseInt(e.replace(n.BATCH_PREFIX,"")),r=parseInt(a.replace(n.BATCH_PREFIX,""));return t-r});e.length>=1&&e.slice(0,e.length-1+1).forEach(e=>{let a=parseInt(e.replace(n.BATCH_PREFIX,""));a!==t&&(localStorage.removeItem(e),console.log(`🗑️ Removed old batch ${a} to free space`))})}catch(e){console.error("Error cleaning up old batches:",e)}let r={batchNumber:e,translations:a,totalTranslations:a.length,lastUpdated:Date.now()},o=JSON.stringify(r),l=`${n.BATCH_PREFIX}${e}`;!function(e){try{let a="storage_test_key",t="x".repeat(e);return localStorage.setItem(a,t),localStorage.removeItem(a),!0}catch(e){return!1}}(o.length)&&(console.warn(`Not enough localStorage space for batch ${e}, cleaning up...`),s()),localStorage.setItem(l,o),console.log(`✅ Saved batch ${e} (${Math.round(o.length/1024)}KB)`)}catch(t){t instanceof Error&&"QuotaExceededError"===t.name?(console.error(`❌ LocalStorage quota exceeded for batch ${e}`),function(e,a){try{console.log("\uD83D\uDEA8 Handling quota exceeded error..."),s();let t={batchNumber:e,translations:a.slice(0,25),totalTranslations:25,lastUpdated:Date.now()};localStorage.setItem(`${n.BATCH_PREFIX}${e}`,JSON.stringify(t)),console.log(`⚠️ Saved reduced batch ${e} with 25 translations due to quota limits`)}catch(e){console.error("Failed to handle quota exceeded error:",e),c()}}(e,a)):console.error(`Error saving translation batch ${e}:`,t)}}(0,a),console.log(`✅ Fast initialization complete: ${a.length} translations loaded`),a}catch(e){return console.error("❌ Error in fast initialization:",e),[]}}async function h(){return d()}async function u(e){let a=new Date().toDateString(),t=`work_data_${e}_${a}`;try{let a=localStorage.getItem(t);if(a){let e=JSON.parse(a);return console.log("✅ Using cached work data (instant load)"),e}console.log("\uD83D\uDCE6 Loading fresh work data for today...");let[n,r,o]=await Promise.all([f(),m(e),p(e)]),l=o.todayTranslations<50&&!o.planExpired,i={translations:n,userSettings:r,todayProgress:o,canWork:l,cachedAt:Date.now()};return localStorage.setItem(t,JSON.stringify(i)),console.log(`✅ Work data cached for today: ${n.length} translations`),i}catch(e){return console.error("❌ Error loading work data:",e),{translations:[],userSettings:{plan:"Trial",earningPerBatch:25,hasQuickAdvantage:!1},todayProgress:{todayTranslations:0,totalTranslations:0,planExpired:!1},canWork:!1}}}async function f(){let e=await fetch("/instradata.json");return o((await e.json()).sort(()=>.5-Math.random()).slice(0,60))}async function m(e){let{getUserVideoSettings:a,getUserData:n}=await t.e(3582).then(t.bind(t,3582)),[r,o]=await Promise.all([a(e),n(e)]),l=!!o?.quickTranslationAdvantageExpiry&&new Date(o.quickTranslationAdvantageExpiry)>new Date,i=l&&o?.quickTranslationAdvantageExpiry?Math.ceil((new Date(o.quickTranslationAdvantageExpiry).getTime()-Date.now())/864e5):0;return{plan:r.plan,earningPerBatch:r.earningPerBatch,hasQuickAdvantage:l,copyPasteDaysRemaining:i}}async function p(e){let{getVideoCountData:a,isUserPlanExpired:n}=await t.e(3582).then(t.bind(t,3582)),[r,o]=await Promise.all([a(e),n(e)]);return{todayTranslations:r.todayTranslations,totalTranslations:r.totalTranslations,planExpired:o.expired,daysLeft:o.daysLeft,activeDays:o.activeDays}}function T(){let e=Math.floor(Math.random()*r.length);return r[e].code}}};