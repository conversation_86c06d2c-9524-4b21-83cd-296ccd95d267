"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_dataService_ts";
exports.ids = ["_ssr_src_lib_dataService_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/dataService.ts":
/*!********************************!*\
  !*** ./src/lib/dataService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLLECTIONS: () => (/* binding */ COLLECTIONS),\n/* harmony export */   FIELD_NAMES: () => (/* binding */ FIELD_NAMES),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addTransaction: () => (/* binding */ addTransaction),\n/* harmony export */   checkCopyPastePermission: () => (/* binding */ checkCopyPastePermission),\n/* harmony export */   checkQuickTranslationAdvantageActive: () => (/* binding */ checkQuickTranslationAdvantageActive),\n/* harmony export */   checkWithdrawalAllowed: () => (/* binding */ checkWithdrawalAllowed),\n/* harmony export */   createWithdrawalRequest: () => (/* binding */ createWithdrawalRequest),\n/* harmony export */   deleteNotification: () => (/* binding */ deleteNotification),\n/* harmony export */   fixAllUsersActiveDays: () => (/* binding */ fixAllUsersActiveDays),\n/* harmony export */   generateSequentialReferralCode: () => (/* binding */ generateSequentialReferralCode),\n/* harmony export */   generateUniqueReferralCode: () => (/* binding */ generateUniqueReferralCode),\n/* harmony export */   getAllNotifications: () => (/* binding */ getAllNotifications),\n/* harmony export */   getBankDetails: () => (/* binding */ getBankDetails),\n/* harmony export */   getPlanEarning: () => (/* binding */ getPlanEarning),\n/* harmony export */   getPlanValidityDays: () => (/* binding */ getPlanValidityDays),\n/* harmony export */   getPlanVideoDuration: () => (/* binding */ getPlanVideoDuration),\n/* harmony export */   getReferralBonus: () => (/* binding */ getReferralBonus),\n/* harmony export */   getReferrals: () => (/* binding */ getReferrals),\n/* harmony export */   getTransactions: () => (/* binding */ getTransactions),\n/* harmony export */   getUnreadNotificationCount: () => (/* binding */ getUnreadNotificationCount),\n/* harmony export */   getUnreadNotifications: () => (/* binding */ getUnreadNotifications),\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   getUserNotifications: () => (/* binding */ getUserNotifications),\n/* harmony export */   getUserVideoSettings: () => (/* binding */ getUserVideoSettings),\n/* harmony export */   getUserWithdrawals: () => (/* binding */ getUserWithdrawals),\n/* harmony export */   getVideoCountData: () => (/* binding */ getVideoCountData),\n/* harmony export */   getWalletData: () => (/* binding */ getWalletData),\n/* harmony export */   grantQuickVideoAdvantage: () => (/* binding */ grantQuickVideoAdvantage),\n/* harmony export */   hasPendingWithdrawals: () => (/* binding */ hasPendingWithdrawals),\n/* harmony export */   hasUnreadNotifications: () => (/* binding */ hasUnreadNotifications),\n/* harmony export */   isNotificationRead: () => (/* binding */ isNotificationRead),\n/* harmony export */   isUserPlanExpired: () => (/* binding */ isUserPlanExpired),\n/* harmony export */   manualProcessReferralBonus: () => (/* binding */ manualProcessReferralBonus),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   processReferralBonus: () => (/* binding */ processReferralBonus),\n/* harmony export */   removeQuickVideoAdvantage: () => (/* binding */ removeQuickVideoAdvantage),\n/* harmony export */   resetDailyVideoCount: () => (/* binding */ resetDailyVideoCount),\n/* harmony export */   saveBankDetails: () => (/* binding */ saveBankDetails),\n/* harmony export */   updateUserActiveDays: () => (/* binding */ updateUserActiveDays),\n/* harmony export */   updateUserData: () => (/* binding */ updateUserData),\n/* harmony export */   updateUserPlanExpiry: () => (/* binding */ updateUserPlanExpiry),\n/* harmony export */   updateUserVideoDuration: () => (/* binding */ updateUserVideoDuration),\n/* harmony export */   updateVideoCount: () => (/* binding */ updateVideoCount),\n/* harmony export */   updateWalletBalance: () => (/* binding */ updateWalletBalance)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n\n\n// Field names for Firestore collections\nconst FIELD_NAMES = {\n    // User fields\n    name: 'name',\n    email: 'email',\n    mobile: 'mobile',\n    referralCode: 'referralCode',\n    referredBy: 'referredBy',\n    referralBonusCredited: 'referralBonusCredited',\n    plan: 'plan',\n    planExpiry: 'planExpiry',\n    activeDays: 'activeDays',\n    lastActiveDayUpdate: 'lastActiveDayUpdate',\n    joinedDate: 'joinedDate',\n    // Wallet fields\n    wallet: 'wallet',\n    // Bank details fields\n    bankAccountHolderName: 'bankAccountHolderName',\n    bankAccountNumber: 'bankAccountNumber',\n    bankIfscCode: 'bankIfscCode',\n    bankName: 'bankName',\n    bankDetailsUpdated: 'bankDetailsUpdated',\n    // Translation fields\n    totalTranslations: 'totalTranslations',\n    todayTranslations: 'todayTranslations',\n    lastTranslationDate: 'lastTranslationDate',\n    translationDuration: 'translationDuration',\n    // Quick Translation Advantage fields\n    quickTranslationAdvantage: 'quickTranslationAdvantage',\n    quickTranslationAdvantageExpiry: 'quickTranslationAdvantageExpiry',\n    quickTranslationAdvantageDays: 'quickTranslationAdvantageDays',\n    quickTranslationAdvantageSeconds: 'quickTranslationAdvantageSeconds',\n    quickTranslationAdvantageGrantedBy: 'quickTranslationAdvantageGrantedBy',\n    quickTranslationAdvantageGrantedAt: 'quickTranslationAdvantageGrantedAt',\n    // Transaction fields\n    type: 'type',\n    amount: 'amount',\n    date: 'date',\n    status: 'status',\n    description: 'description',\n    userId: 'userId'\n};\n// Collection names\nconst COLLECTIONS = {\n    users: 'users',\n    transactions: 'transactions',\n    withdrawals: 'withdrawals',\n    plans: 'plans',\n    settings: 'settings',\n    notifications: 'notifications',\n    adminLeaves: 'adminLeaves',\n    userLeaves: 'userLeaves'\n};\n// Get user data\nasync function getUserData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserData:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Ensure all values are properly typed\n            const result = {\n                name: String(data[FIELD_NAMES.name] || ''),\n                email: String(data[FIELD_NAMES.email] || ''),\n                mobile: String(data[FIELD_NAMES.mobile] || ''),\n                referralCode: String(data[FIELD_NAMES.referralCode] || ''),\n                referredBy: String(data[FIELD_NAMES.referredBy] || ''),\n                referralBonusCredited: Boolean(data[FIELD_NAMES.referralBonusCredited] || false),\n                plan: String(data[FIELD_NAMES.plan] || 'Trial'),\n                planExpiry: data[FIELD_NAMES.planExpiry]?.toDate() || null,\n                activeDays: Number(data[FIELD_NAMES.activeDays] || 0),\n                lastActiveDayUpdate: data[FIELD_NAMES.lastActiveDayUpdate]?.toDate() || null,\n                joinedDate: data[FIELD_NAMES.joinedDate]?.toDate() || new Date(),\n                translationDuration: Number(data[FIELD_NAMES.translationDuration] || (data[FIELD_NAMES.plan] === 'Trial' ? 30 : 300)),\n                // Quick Translation Advantage fields\n                quickTranslationAdvantage: Boolean(data[FIELD_NAMES.quickTranslationAdvantage] || false),\n                quickTranslationAdvantageExpiry: data[FIELD_NAMES.quickTranslationAdvantageExpiry]?.toDate() || null,\n                quickTranslationAdvantageDays: Number(data[FIELD_NAMES.quickTranslationAdvantageDays] || 0),\n                quickTranslationAdvantageSeconds: Number(data[FIELD_NAMES.quickTranslationAdvantageSeconds] || 30),\n                quickTranslationAdvantageGrantedBy: String(data[FIELD_NAMES.quickTranslationAdvantageGrantedBy] || ''),\n                quickTranslationAdvantageGrantedAt: data[FIELD_NAMES.quickTranslationAdvantageGrantedAt]?.toDate() || null\n            };\n            console.log('getUserData result:', result);\n            return result;\n        }\n        return null;\n    } catch (error) {\n        console.error('Error getting user data:', error);\n        return null // Return null instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get wallet data\nasync function getWalletData(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getWalletData:', userId);\n            return {\n                wallet: 0\n            };\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const result = {\n                wallet: Number(data[FIELD_NAMES.wallet] || 0)\n            };\n            console.log('getWalletData result:', result);\n            return result;\n        }\n        return {\n            wallet: 0\n        };\n    } catch (error) {\n        console.error('Error getting wallet data:', error);\n        return {\n            wallet: 0\n        } // Return default instead of throwing\n        ;\n    }\n}\n// Get translation count data\nasync function getVideoCountData(userId) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const totalTranslations = data[FIELD_NAMES.totalTranslations] || 0;\n            let todayTranslations = data[FIELD_NAMES.todayTranslations] || 0;\n            const lastTranslationDate = data[FIELD_NAMES.lastTranslationDate]?.toDate();\n            // Check if it's a new day\n            const today = new Date();\n            const isNewDay = !lastTranslationDate || lastTranslationDate.toDateString() !== today.toDateString();\n            // If it's a new day, reset todayTranslations and update active days\n            if (isNewDay && todayTranslations > 0) {\n                console.log(`🔄 Resetting daily translation count for user ${userId} (was ${todayTranslations})`);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.todayTranslations]: 0\n                });\n                todayTranslations = 0;\n                // Also update active days for accurate tracking\n                try {\n                    await updateUserActiveDays(userId);\n                } catch (error) {\n                    console.error('Error updating active days during daily reset:', error);\n                }\n            }\n            return {\n                totalTranslations: totalTranslations,\n                todayTranslations: todayTranslations,\n                remainingTranslations: Math.max(0, 50 - todayTranslations)\n            };\n        }\n        return {\n            totalTranslations: 0,\n            todayTranslations: 0,\n            remainingTranslations: 50\n        };\n    } catch (error) {\n        console.error('Error getting video count data:', error);\n        throw error;\n    }\n}\n// Update user data\nasync function updateUserData(userId, data) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId), data);\n    } catch (error) {\n        console.error('Error updating user data:', error);\n        throw error;\n    }\n}\n// Add transaction\nasync function addTransaction(userId, transactionData) {\n    try {\n        const transaction = {\n            [FIELD_NAMES.userId]: userId,\n            [FIELD_NAMES.type]: transactionData.type,\n            [FIELD_NAMES.amount]: transactionData.amount,\n            [FIELD_NAMES.description]: transactionData.description,\n            [FIELD_NAMES.status]: transactionData.status || 'completed',\n            [FIELD_NAMES.date]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), transaction);\n    } catch (error) {\n        console.error('Error adding transaction:', error);\n        throw error;\n    }\n}\n// Get transactions\nasync function getTransactions(userId, limitCount = 10) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getTransactions:', userId);\n            return [];\n        }\n        // Temporary fix: Use only where clause without orderBy to avoid index requirement\n        // TODO: Create composite index in Firebase console for better performance\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.transactions), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.userId, '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const transactions = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data()[FIELD_NAMES.date]?.toDate()\n            }));\n        // Sort in memory since we can't use orderBy without index\n        transactions.sort((a, b)=>{\n            const dateA = a.date || new Date(0);\n            const dateB = b.date || new Date(0);\n            return dateB.getTime() - dateA.getTime() // Descending order\n            ;\n        });\n        return transactions;\n    } catch (error) {\n        console.error('Error getting transactions:', error);\n        return [] // Return empty array instead of throwing to prevent crashes\n        ;\n    }\n}\n// Get referrals\nasync function getReferrals(referralCode) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referredBy, '==', referralCode));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                joinedDate: doc.data()[FIELD_NAMES.joinedDate]?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting referrals:', error);\n        throw error;\n    }\n}\n// Update translation count\nasync function updateVideoCount(userId) {\n    try {\n        const today = new Date();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        // Get current data to check if we need to reset daily count\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            const lastTranslationDate = data[FIELD_NAMES.lastTranslationDate]?.toDate();\n            const currentTodayTranslations = data[FIELD_NAMES.todayTranslations] || 0;\n            // Check if it's a new day\n            const isNewDay = !lastTranslationDate || lastTranslationDate.toDateString() !== today.toDateString();\n            if (isNewDay && currentTodayTranslations > 0) {\n                // Reset today's count and then increment\n                console.log(`🔄 Resetting and updating daily translation count for user ${userId}`);\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.totalTranslations]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.todayTranslations]: 1,\n                    [FIELD_NAMES.lastTranslationDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                });\n            } else {\n                // Normal increment\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                    [FIELD_NAMES.totalTranslations]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.todayTranslations]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                    [FIELD_NAMES.lastTranslationDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n                });\n            }\n        } else {\n            // User doesn't exist, create with initial values\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.totalTranslations]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                [FIELD_NAMES.todayTranslations]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(1),\n                [FIELD_NAMES.lastTranslationDate]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(today)\n            });\n        }\n    } catch (error) {\n        console.error('Error updating translation count:', error);\n        throw error;\n    }\n}\n// Reset daily translation count for a user (admin function)\nasync function resetDailyVideoCount(userId) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.todayTranslations]: 0\n        });\n        console.log(`✅ Reset daily translation count for user ${userId}`);\n    } catch (error) {\n        console.error('Error resetting daily translation count:', error);\n        throw error;\n    }\n}\n// Update user's active days using centralized service\nasync function updateUserActiveDays(userId) {\n    try {\n        const { updateUserActiveDays: centralizedUpdate } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./activeDaysService */ \"(ssr)/./src/lib/activeDaysService.ts\"));\n        return await centralizedUpdate(userId);\n    } catch (error) {\n        console.error('Error updating user active days:', error);\n        throw error;\n    }\n}\n// Fix all users' active days (admin function)\nasync function fixAllUsersActiveDays() {\n    try {\n        console.log('🔧 Starting to fix all users active days...');\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users));\n        let fixedCount = 0;\n        let errorCount = 0;\n        for (const userDoc of usersSnapshot.docs){\n            try {\n                await updateUserActiveDays(userDoc.id);\n                fixedCount++;\n            } catch (error) {\n                console.error(`Error fixing active days for user ${userDoc.id}:`, error);\n                errorCount++;\n            }\n        }\n        console.log(`✅ Fixed active days for ${fixedCount} users, ${errorCount} errors`);\n        return {\n            fixedCount,\n            errorCount\n        };\n    } catch (error) {\n        console.error('Error fixing all users active days:', error);\n        throw error;\n    }\n}\n// Update wallet balance\nasync function updateWalletBalance(userId, amount) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.wallet]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(amount)\n        });\n    } catch (error) {\n        console.error('Error updating wallet balance:', error);\n        throw error;\n    }\n}\n// Save bank details\nasync function saveBankDetails(userId, bankDetails) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            throw new Error('Invalid userId provided');\n        }\n        // Validate bank details\n        validateBankDetails(bankDetails);\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.bankAccountHolderName]: bankDetails.accountHolderName.trim(),\n            [FIELD_NAMES.bankAccountNumber]: bankDetails.accountNumber.trim(),\n            [FIELD_NAMES.bankIfscCode]: bankDetails.ifscCode.trim().toUpperCase(),\n            [FIELD_NAMES.bankName]: bankDetails.bankName.trim(),\n            [FIELD_NAMES.bankDetailsUpdated]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        });\n        console.log('Bank details saved successfully for user:', userId);\n    } catch (error) {\n        console.error('Error saving bank details:', error);\n        throw error;\n    }\n}\n// Get bank details\nasync function getBankDetails(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getBankDetails:', userId);\n            return null;\n        }\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            // Check if bank details exist\n            if (data[FIELD_NAMES.bankAccountNumber]) {\n                const result = {\n                    accountHolderName: String(data[FIELD_NAMES.bankAccountHolderName] || ''),\n                    accountNumber: String(data[FIELD_NAMES.bankAccountNumber] || ''),\n                    ifscCode: String(data[FIELD_NAMES.bankIfscCode] || ''),\n                    bankName: String(data[FIELD_NAMES.bankName] || '')\n                };\n                console.log('getBankDetails result found');\n                return result;\n            }\n        }\n        console.log('No bank details found for user');\n        return null;\n    } catch (error) {\n        console.error('Error getting bank details:', error);\n        return null;\n    }\n}\n// Get plan-based earning amount (per batch of 50 translations)\nfunction getPlanEarning(plan) {\n    const planEarnings = {\n        'Trial': 25,\n        'Junior': 150,\n        'Senior': 250,\n        'Expert': 400,\n        'Starter': 25,\n        'Basic': 75,\n        'Premium': 150,\n        'Gold': 200,\n        'Platinum': 250,\n        'Diamond': 400 // Legacy plan mapping\n    };\n    return planEarnings[plan] || 25 // Default to trial earning (per batch of 50 translations)\n    ;\n}\n// Check copy-paste permission using centralized service\nasync function checkCopyPastePermission(userId) {\n    try {\n        const { checkCopyPastePermission: centralizedCheck } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./copyPasteService */ \"(ssr)/./src/lib/copyPasteService.ts\"));\n        const result = await centralizedCheck(userId);\n        return {\n            hasPermission: result.hasPermission,\n            daysRemaining: result.daysRemaining\n        };\n    } catch (error) {\n        console.error('Error checking copy-paste permission:', error);\n        return {\n            hasPermission: false,\n            daysRemaining: 0\n        };\n    }\n}\n// Get plan-based translation duration (in seconds)\nfunction getPlanVideoDuration(plan) {\n    const planDurations = {\n        'Trial': 30,\n        'Starter': 300,\n        'Basic': 300,\n        'Premium': 300,\n        'Gold': 180,\n        'Platinum': 120,\n        'Diamond': 60 // 1 minute (Rs 9999 plan)\n    };\n    return planDurations[plan] || 30 // Default to trial duration (30 seconds)\n    ;\n}\n// Get plan validity duration in days\nfunction getPlanValidityDays(plan) {\n    const planValidityDays = {\n        'Trial': 2,\n        'Junior': 30,\n        'Senior': 30,\n        'Expert': 30,\n        'Starter': 30,\n        'Basic': 30,\n        'Premium': 30,\n        'Gold': 30,\n        'Platinum': 30,\n        'Diamond': 30,\n        '499': 30,\n        '1499': 30,\n        '2999': 30,\n        '3999': 30,\n        '5999': 30,\n        '9999': 30 // Legacy plan mapping\n    };\n    return planValidityDays[plan] || 2 // Default to trial duration (2 days)\n    ;\n}\n// Check if user's plan is expired based on active days and plan validity\nasync function isUserPlanExpired(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                expired: true,\n                reason: 'User data not found'\n            };\n        }\n        // If user is on Trial plan, check based on joined date\n        if (userData.plan === 'Trial') {\n            const joinedDate = userData.joinedDate || new Date();\n            const today = new Date();\n            // Calculate days since joining (0 on join day, 1 after first day, etc.)\n            const daysSinceJoining = Math.floor((today.getTime() - joinedDate.getTime()) / (1000 * 60 * 60 * 24));\n            // Active days start from 1 on join day\n            const activeDays = daysSinceJoining + 1;\n            // Trial lasts 2 days, so days left = 2 - days since joining\n            const trialDaysLeft = Math.max(0, 2 - daysSinceJoining);\n            console.log('📅 Trial plan calculation:', {\n                joinedDate: joinedDate.toDateString(),\n                today: today.toDateString(),\n                daysSinceJoining,\n                activeDays,\n                trialDaysLeft\n            });\n            return {\n                expired: trialDaysLeft <= 0,\n                reason: trialDaysLeft <= 0 ? 'Trial period expired' : undefined,\n                daysLeft: trialDaysLeft,\n                activeDays: activeDays\n            };\n        }\n        // For paid plans, check if planExpiry is set\n        if (userData.planExpiry) {\n            const today = new Date();\n            const expired = today > userData.planExpiry;\n            const daysLeft = expired ? 0 : Math.ceil((userData.planExpiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                expired,\n                reason: expired ? 'Plan subscription expired' : undefined,\n                daysLeft,\n                activeDays: userData.activeDays || 0\n            };\n        }\n        // If planExpiry is not set, calculate based on active days and plan validity\n        const planValidityDays = getPlanValidityDays(userData.plan);\n        const currentActiveDays = userData.activeDays || 0;\n        const daysLeft = Math.max(0, planValidityDays - currentActiveDays);\n        const expired = daysLeft <= 0;\n        return {\n            expired,\n            reason: expired ? `Plan validity period (${planValidityDays} days) exceeded based on active days` : undefined,\n            daysLeft,\n            activeDays: currentActiveDays\n        };\n    } catch (error) {\n        console.error('Error checking plan expiry:', error);\n        return {\n            expired: true,\n            reason: 'Error checking plan status'\n        };\n    }\n}\n// Update user's plan expiry when admin changes plan\nasync function updateUserPlanExpiry(userId, newPlan, customExpiryDate) {\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        if (newPlan === 'Trial') {\n            // Trial plan doesn't have expiry, it's based on joined date\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: null\n            });\n        } else {\n            // Set expiry date for paid plans\n            let expiryDate;\n            if (customExpiryDate) {\n                expiryDate = customExpiryDate;\n            } else {\n                // Calculate expiry based on plan validity\n                const validityDays = getPlanValidityDays(newPlan);\n                const today = new Date();\n                expiryDate = new Date(today.getTime() + validityDays * 24 * 60 * 60 * 1000);\n            }\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.planExpiry]: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.fromDate(expiryDate)\n            });\n            console.log(`Updated plan expiry for user ${userId} to ${expiryDate.toDateString()}`);\n        }\n    } catch (error) {\n        console.error('Error updating plan expiry:', error);\n        throw error;\n    }\n}\n// Get referral bonus based on plan\nfunction getReferralBonus(plan) {\n    const referralBonuses = {\n        'Trial': 0,\n        'Junior': 300,\n        'Senior': 700,\n        'Expert': 1200,\n        '499': 50,\n        '1499': 150,\n        '2999': 300,\n        '3999': 400,\n        '5999': 700,\n        '9999': 1200,\n        'Starter': 50,\n        'Basic': 150,\n        'Premium': 300,\n        'Gold': 400,\n        'Platinum': 700,\n        'Diamond': 1200 // Legacy plan mapping\n    };\n    return referralBonuses[plan] || 0;\n}\n// Process referral bonus when admin upgrades user from Trial to paid plan\nasync function processReferralBonus(userId, oldPlan, newPlan) {\n    try {\n        console.log(`🎯 REFERRAL BONUS DEBUG: Starting process for user ${userId}`);\n        console.log(`🎯 Plan change: ${oldPlan} → ${newPlan}`);\n        // Only process bonus when upgrading FROM Trial TO a paid plan\n        if (oldPlan !== 'Trial' || newPlan === 'Trial') {\n            console.log('❌ Referral bonus only applies when upgrading from Trial to paid plan');\n            console.log(`❌ Current conditions: oldPlan=${oldPlan}, newPlan=${newPlan}`);\n            return;\n        }\n        console.log(`✅ Processing referral bonus for user ${userId} upgrading from ${oldPlan} to ${newPlan}`);\n        // Get the user's data to find their referral info\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId));\n        if (!userDoc.exists()) {\n            console.log('❌ User not found in database');\n            return;\n        }\n        const userData = userDoc.data();\n        const referredBy = userData[FIELD_NAMES.referredBy];\n        const alreadyCredited = userData[FIELD_NAMES.referralBonusCredited];\n        console.log(`🔍 User referral data:`);\n        console.log(`   - Referred by: ${referredBy || 'None'}`);\n        console.log(`   - Already credited: ${alreadyCredited}`);\n        console.log(`   - User name: ${userData[FIELD_NAMES.name]}`);\n        if (!referredBy) {\n            console.log('❌ User was not referred by anyone, skipping bonus processing');\n            return;\n        }\n        if (alreadyCredited) {\n            console.log('❌ Referral bonus already credited for this user, skipping');\n            return;\n        }\n        console.log(`🔍 Finding referrer with code: ${referredBy}`);\n        // Find the referrer by referral code\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(FIELD_NAMES.referralCode, '==', referredBy), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        if (querySnapshot.empty) {\n            console.log(`❌ Referral code not found: ${referredBy}`);\n            console.log('❌ No referrer found with this code in database');\n            return;\n        }\n        const referrerDoc = querySnapshot.docs[0];\n        const referrerId = referrerDoc.id;\n        const referrerData = referrerDoc.data();\n        const bonusAmount = getReferralBonus(newPlan);\n        console.log(`✅ Found referrer:`);\n        console.log(`   - Referrer ID: ${referrerId}`);\n        console.log(`   - Referrer name: ${referrerData[FIELD_NAMES.name]}`);\n        console.log(`   - Bonus amount: ₹${bonusAmount}`);\n        console.log(`   - Plan: ${newPlan}`);\n        if (bonusAmount > 0) {\n            console.log(`💰 Processing bonus payment...`);\n            // Add bonus to referrer's wallet\n            await updateWalletBalance(referrerId, bonusAmount);\n            console.log(`✅ Added ₹${bonusAmount} to referrer's wallet`);\n            // Add 50 translations to referrer's total translation count\n            const referrerRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, referrerId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(referrerRef, {\n                [FIELD_NAMES.totalTranslations]: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.increment)(50)\n            });\n            console.log(`✅ Added 50 bonus translations to referrer`);\n            // Mark referral bonus as credited for this user\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n                [FIELD_NAMES.referralBonusCredited]: true\n            });\n            console.log(`✅ Marked referral bonus as credited for user ${userId}`);\n            // Add transaction record for referral bonus\n            await addTransaction(referrerId, {\n                type: 'referral_bonus',\n                amount: bonusAmount,\n                description: `Referral bonus for ${newPlan} plan upgrade + 50 bonus translations (User: ${userData[FIELD_NAMES.name]})`\n            });\n            console.log(`✅ Added transaction record for referral bonus`);\n            console.log(`🎉 REFERRAL BONUS COMPLETED: ₹${bonusAmount} + 50 translations for referrer ${referrerId}`);\n        } else {\n            console.log(`❌ No bonus amount calculated for plan: ${newPlan}`);\n            console.log(`❌ Available plans:`, Object.keys({\n                'Trial': 0,\n                'Junior': 300,\n                'Senior': 700,\n                'Expert': 1200\n            }));\n        }\n    } catch (error) {\n        console.error('❌ Error processing referral bonus:', error);\n        console.error('❌ Error details:', error);\n    // Don't throw error to avoid breaking plan update\n    }\n}\n// Manual referral bonus processing (for testing/debugging)\nasync function manualProcessReferralBonus(userId) {\n    try {\n        console.log(`🔧 MANUAL REFERRAL BONUS: Processing for user ${userId}`);\n        const userData = await getUserData(userId);\n        if (!userData) {\n            console.log('❌ User not found');\n            return {\n                success: false,\n                message: 'User not found'\n            };\n        }\n        console.log(`🔍 User data:`);\n        console.log(`   - Name: ${userData.name}`);\n        console.log(`   - Plan: ${userData.plan}`);\n        console.log(`   - Referred by: ${userData.referredBy || 'None'}`);\n        console.log(`   - Bonus credited: ${userData.referralBonusCredited}`);\n        if (userData.plan === 'Trial') {\n            return {\n                success: false,\n                message: 'User is still on Trial plan. Upgrade to paid plan first.'\n            };\n        }\n        if (!userData.referredBy) {\n            return {\n                success: false,\n                message: 'User was not referred by anyone'\n            };\n        }\n        if (userData.referralBonusCredited) {\n            return {\n                success: false,\n                message: 'Referral bonus already credited'\n            };\n        }\n        // Process the bonus manually\n        await processReferralBonus(userId, 'Trial', userData.plan);\n        return {\n            success: true,\n            message: `Referral bonus processed for ${userData.plan} plan`\n        };\n    } catch (error) {\n        console.error('❌ Error in manual referral bonus processing:', error);\n        return {\n            success: false,\n            message: `Error: ${error?.message || 'Unknown error'}`\n        };\n    }\n}\n// Get user translation settings (duration and earning per batch)\nasync function getUserVideoSettings(userId) {\n    try {\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                translationDuration: 30,\n                earningPerBatch: 25,\n                plan: 'Trial',\n                hasQuickAdvantage: false\n            };\n        }\n        // Check if user has active quick translation advantage\n        const hasActiveQuickAdvantage = checkQuickTranslationAdvantageActive(userData);\n        let translationDuration = userData.translationDuration;\n        // If user has active quick translation advantage, use custom seconds or default to 30\n        if (hasActiveQuickAdvantage) {\n            translationDuration = userData.quickTranslationAdvantageSeconds || 30 // Use custom duration or default to 30 seconds\n            ;\n        } else {\n            // Use plan-based translation duration, but allow admin overrides for non-trial users\n            if (!translationDuration || userData.plan === 'Trial') {\n                translationDuration = getPlanVideoDuration(userData.plan);\n            }\n        }\n        return {\n            translationDuration: translationDuration,\n            earningPerBatch: getPlanEarning(userData.plan),\n            plan: userData.plan,\n            hasQuickAdvantage: hasActiveQuickAdvantage,\n            quickAdvantageExpiry: userData.quickTranslationAdvantageExpiry\n        };\n    } catch (error) {\n        console.error('Error getting user translation settings:', error);\n        return {\n            translationDuration: 30,\n            earningPerBatch: 25,\n            plan: 'Trial',\n            hasQuickAdvantage: false\n        };\n    }\n}\n// Check if user has active quick translation advantage\nfunction checkQuickTranslationAdvantageActive(userData) {\n    if (!userData.quickTranslationAdvantage || !userData.quickTranslationAdvantageExpiry) {\n        return false;\n    }\n    const now = new Date();\n    const expiry = userData.quickTranslationAdvantageExpiry;\n    return now < expiry;\n}\n// Grant quick translation advantage to user (admin function)\nasync function grantQuickVideoAdvantage(userId, days, grantedBy, seconds = 30) {\n    try {\n        if (days <= 0 || days > 365) {\n            throw new Error('Days must be between 1 and 365');\n        }\n        if (seconds < 1 || seconds > 420) {\n            throw new Error('Seconds must be between 1 and 420 (7 minutes)');\n        }\n        // Use centralized copy-paste service\n        const { grantCopyPastePermission } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./copyPasteService */ \"(ssr)/./src/lib/copyPasteService.ts\"));\n        await grantCopyPastePermission(userId, days);\n        console.log(`Granted copy-paste permission to user ${userId} for ${days} days by ${grantedBy}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_granted',\n            amount: 0,\n            description: `Copy-paste permission granted for ${days} days by ${grantedBy}`\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error granting quick video advantage:', error);\n        throw error;\n    }\n}\n// Remove quick translation advantage from user (admin function)\nasync function removeQuickVideoAdvantage(userId, removedBy) {\n    try {\n        // Use centralized copy-paste service\n        const { removeCopyPastePermission } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./copyPasteService */ \"(ssr)/./src/lib/copyPasteService.ts\"));\n        await removeCopyPastePermission(userId);\n        console.log(`Removed copy-paste permission from user ${userId} by ${removedBy}`);\n        // Add transaction record\n        await addTransaction(userId, {\n            type: 'quick_advantage_removed',\n            amount: 0,\n            description: `Copy-paste permission removed by ${removedBy}`\n        });\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error removing quick video advantage:', error);\n        throw error;\n    }\n}\n// Update user translation duration (admin function)\nasync function updateUserVideoDuration(userId, durationInSeconds) {\n    try {\n        // Validate duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-7 minutes)\n        const isQuickDuration = [\n            1,\n            10,\n            30\n        ].includes(durationInSeconds);\n        const isStandardDuration = durationInSeconds >= 60 && durationInSeconds <= 420;\n        if (!isQuickDuration && !isStandardDuration) {\n            throw new Error('Translation duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration');\n        }\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users, userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(userRef, {\n            [FIELD_NAMES.translationDuration]: durationInSeconds\n        });\n        console.log(`Updated translation duration for user ${userId} to ${durationInSeconds} seconds`);\n    } catch (error) {\n        console.error('Error updating user translation duration:', error);\n        throw error;\n    }\n}\n// Validate bank details\nfunction validateBankDetails(bankDetails) {\n    const { accountHolderName, accountNumber, ifscCode, bankName } = bankDetails;\n    if (!accountHolderName || accountHolderName.trim().length < 2) {\n        throw new Error('Account holder name must be at least 2 characters long');\n    }\n    if (!accountNumber || !/^\\d{9,18}$/.test(accountNumber.trim())) {\n        throw new Error('Account number must be 9-18 digits');\n    }\n    if (!ifscCode || !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(ifscCode.trim().toUpperCase())) {\n        throw new Error('Invalid IFSC code format (e.g., SBIN0001234)');\n    }\n    if (!bankName || bankName.trim().length < 2) {\n        throw new Error('Bank name must be at least 2 characters long');\n    }\n}\n// Add notification (admin function) - All notifications are now blocking\nasync function addNotification(notification) {\n    try {\n        const notificationData = {\n            title: notification.title,\n            message: notification.message,\n            type: notification.type,\n            targetUsers: notification.targetUsers,\n            userIds: notification.userIds || [],\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdBy: notification.createdBy\n        };\n        console.log('Adding notification to Firestore:', notificationData);\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), notificationData);\n        console.log('Notification added successfully with ID:', docRef.id);\n        // Verify the notification was added\n        const addedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n        if (addedDoc.exists()) {\n            console.log('Notification verified in database:', addedDoc.data());\n        } else {\n            console.warn('Notification not found after adding');\n        }\n        return docRef.id;\n    } catch (error) {\n        console.error('Error adding notification:', error);\n        throw error;\n    }\n}\n// Get notifications for a user\nasync function getUserNotifications(userId, limitCount = 20) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUserNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading notifications for user: ${userId}`);\n        // Try to get notifications with fallback for indexing issues\n        let allUsersSnapshot, specificUserSnapshot;\n        try {\n            // Get notifications targeted to all users\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n            console.log(`Found ${allUsersSnapshot.docs.length} notifications for all users`);\n        } catch (error) {\n            console.warn('Error querying all users notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'all'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(allUsersQuery);\n        }\n        try {\n            // Get notifications targeted to specific user\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n            console.log(`Found ${specificUserSnapshot.docs.length} notifications for specific user`);\n        } catch (error) {\n            console.warn('Error querying specific user notifications, trying without orderBy:', error);\n            // Fallback without orderBy if index is not ready\n            const specificUserQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('targetUsers', '==', 'specific'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userIds', 'array-contains', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n            specificUserSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(specificUserQuery);\n        }\n        const notifications = [];\n        // Process all users notifications\n        allUsersSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Process specific user notifications\n        specificUserSnapshot.docs.forEach((doc)=>{\n            notifications.push({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            });\n        });\n        // Sort by creation date (newest first)\n        notifications.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        const finalNotifications = notifications.slice(0, limitCount);\n        console.log(`Returning ${finalNotifications.length} total notifications for user`);\n        return finalNotifications;\n    } catch (error) {\n        console.error('Error getting user notifications:', error);\n        return [];\n    }\n}\n// Get all notifications (admin function)\nasync function getAllNotifications(limitCount = 50) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('createdAt', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        const notifications = querySnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                createdAt: doc.data().createdAt?.toDate() || new Date()\n            }));\n        return notifications;\n    } catch (error) {\n        console.error('Error getting all notifications:', error);\n        return [];\n    }\n}\n// Delete notification (admin function)\nasync function deleteNotification(notificationId) {\n    try {\n        if (!notificationId || typeof notificationId !== 'string') {\n            throw new Error('Invalid notification ID provided');\n        }\n        console.log('Deleting notification:', notificationId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.notifications, notificationId));\n        console.log('Notification deleted successfully');\n    } catch (error) {\n        console.error('Error deleting notification:', error);\n        throw error;\n    }\n}\n// Mark notification as read\nasync function markNotificationAsRead(notificationId, userId) {\n    try {\n        // For now, we'll store read status in localStorage since it's user-specific\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        if (!readNotifications.includes(notificationId)) {\n            readNotifications.push(notificationId);\n            localStorage.setItem(`read_notifications_${userId}`, JSON.stringify(readNotifications));\n        }\n    } catch (error) {\n        console.error('Error marking notification as read:', error);\n    }\n}\n// Check if notification is read\nfunction isNotificationRead(notificationId, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return readNotifications.includes(notificationId);\n    } catch (error) {\n        console.error('Error checking notification read status:', error);\n        return false;\n    }\n}\n// Get unread notification count\nfunction getUnreadNotificationCount(notifications, userId) {\n    try {\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        return notifications.filter((notification)=>!readNotifications.includes(notification.id)).length;\n    } catch (error) {\n        console.error('Error getting unread notification count:', error);\n        return 0;\n    }\n}\n// Get unread notifications - All notifications are now blocking\nasync function getUnreadNotifications(userId) {\n    try {\n        if (!userId || typeof userId !== 'string') {\n            console.error('Invalid userId provided to getUnreadNotifications:', userId);\n            return [];\n        }\n        console.log(`Loading unread notifications for user: ${userId}`);\n        // Get all notifications for the user\n        const allNotifications = await getUserNotifications(userId, 50);\n        // Filter for unread notifications\n        const readNotifications = JSON.parse(localStorage.getItem(`read_notifications_${userId}`) || '[]');\n        const unreadNotifications = allNotifications.filter((notification)=>notification.id && !readNotifications.includes(notification.id));\n        console.log(`Found ${unreadNotifications.length} unread notifications`);\n        return unreadNotifications;\n    } catch (error) {\n        console.error('Error getting unread notifications:', error);\n        return [];\n    }\n}\n// Check if user has unread notifications\nasync function hasUnreadNotifications(userId) {\n    try {\n        const unreadNotifications = await getUnreadNotifications(userId);\n        return unreadNotifications.length > 0;\n    } catch (error) {\n        console.error('Error checking for unread notifications:', error);\n        return false;\n    }\n}\n// Check if user has pending withdrawals\nasync function hasPendingWithdrawals(userId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('status', '==', 'pending'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(1));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return !snapshot.empty;\n    } catch (error) {\n        console.error('Error checking pending withdrawals:', error);\n        return false;\n    }\n}\n// Check if withdrawal is allowed (timing, leave restrictions, and plan restrictions)\nasync function checkWithdrawalAllowed(userId) {\n    try {\n        // Check user plan first\n        const userData = await getUserData(userId);\n        if (!userData) {\n            return {\n                allowed: false,\n                reason: 'Unable to verify user information. Please try again.'\n            };\n        }\n        // Check if user is on trial plan\n        if (userData.plan === 'Trial') {\n            return {\n                allowed: false,\n                reason: 'Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals.'\n            };\n        }\n        // Check if user has pending withdrawals\n        const hasPending = await hasPendingWithdrawals(userId);\n        if (hasPending) {\n            return {\n                allowed: false,\n                reason: 'You have a pending withdrawal request. Please wait for it to be processed before submitting a new request.'\n            };\n        }\n        const now = new Date();\n        const currentHour = now.getHours();\n        // Check time restrictions (10 AM to 6 PM)\n        if (currentHour < 10 || currentHour >= 18) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are only allowed between 10:00 AM to 6:00 PM'\n            };\n        }\n        // Check admin leave day\n        const { isAdminLeaveDay } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isAdminLeave = await isAdminLeaveDay(now);\n        if (isAdminLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on admin leave/holiday days'\n            };\n        }\n        // Check user leave day\n        const { isUserOnLeave } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./leaveService */ \"(ssr)/./src/lib/leaveService.ts\"));\n        const isUserLeave = await isUserOnLeave(userId, now);\n        if (isUserLeave) {\n            return {\n                allowed: false,\n                reason: 'Withdrawals are not allowed on your leave days'\n            };\n        }\n        return {\n            allowed: true\n        };\n    } catch (error) {\n        console.error('Error checking withdrawal allowed:', error);\n        return {\n            allowed: false,\n            reason: 'Unable to verify withdrawal eligibility. Please try again.'\n        };\n    }\n}\n// Create withdrawal request\nasync function createWithdrawalRequest(userId, amount, bankDetails) {\n    try {\n        // Check minimum withdrawal amount\n        if (amount < 50) {\n            throw new Error('Minimum withdrawal amount is ₹50');\n        }\n        // Check if withdrawal is allowed\n        const withdrawalCheck = await checkWithdrawalAllowed(userId);\n        if (!withdrawalCheck.allowed) {\n            throw new Error(withdrawalCheck.reason);\n        }\n        // Check if user has sufficient balance\n        const walletData = await getWalletData(userId);\n        if (walletData.wallet < amount) {\n            throw new Error('Insufficient wallet balance');\n        }\n        // Debit the amount from user's wallet immediately\n        await updateWalletBalance(userId, -amount);\n        // Add transaction record for withdrawal debit\n        await addTransaction(userId, {\n            type: 'withdrawal_request',\n            amount: -amount,\n            description: `Withdrawal request submitted - ₹${amount} debited from wallet`\n        });\n        const withdrawalData = {\n            userId,\n            amount,\n            bankDetails,\n            status: 'pending',\n            date: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now(),\n            createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.Timestamp.now()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), withdrawalData);\n        return docRef.id;\n    } catch (error) {\n        console.error('Error creating withdrawal request:', error);\n        throw error;\n    }\n}\n// Get user withdrawals\nasync function getUserWithdrawals(userId, limitCount = 20) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.withdrawals), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)('userId', '==', userId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.orderBy)('date', 'desc'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.limit)(limitCount));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n        return snapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data(),\n                date: doc.data().date?.toDate()\n            }));\n    } catch (error) {\n        console.error('Error getting user withdrawals:', error);\n        return [];\n    }\n}\n// Generate unique referral code with TN prefix and sequential numbering\nasync function generateUniqueReferralCode() {\n    try {\n        // Try to get count from server for sequential numbering\n        try {\n            const usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, COLLECTIONS.users);\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getCountFromServer)(usersCollection);\n            const count = snapshot.data().count;\n            const sequentialNumber = (count + 1).toString().padStart(4, '0');\n            return `TN${sequentialNumber}`;\n        } catch (countError) {\n            console.warn('Failed to get count from server, using fallback method:', countError);\n            // Fallback to timestamp-based generation\n            const timestamp = Date.now().toString().slice(-4);\n            const randomPart = Math.random().toString(36).substring(2, 4).toUpperCase();\n            return `TN${timestamp}${randomPart}`;\n        }\n    } catch (error) {\n        console.error('Error generating unique referral code:', error);\n        // Final fallback\n        const timestamp = Date.now().toString().slice(-4);\n        return `TN${timestamp}`;\n    }\n}\n// Generate sequential referral code (alias for generateUniqueReferralCode)\nasync function generateSequentialReferralCode() {\n    return generateUniqueReferralCode();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2RhdGFTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBZTJCO0FBQ0k7QUFFL0Isd0NBQXdDO0FBQ2pDLE1BQU1lLGNBQWM7SUFDekIsY0FBYztJQUNkQyxNQUFNO0lBQ05DLE9BQU87SUFDUEMsUUFBUTtJQUNSQyxjQUFjO0lBQ2RDLFlBQVk7SUFDWkMsdUJBQXVCO0lBQ3ZCQyxNQUFNO0lBQ05DLFlBQVk7SUFDWkMsWUFBWTtJQUNaQyxxQkFBcUI7SUFDckJDLFlBQVk7SUFFWixnQkFBZ0I7SUFDaEJDLFFBQVE7SUFFUixzQkFBc0I7SUFDdEJDLHVCQUF1QjtJQUN2QkMsbUJBQW1CO0lBQ25CQyxjQUFjO0lBQ2RDLFVBQVU7SUFDVkMsb0JBQW9CO0lBRXBCLHFCQUFxQjtJQUNyQkMsbUJBQW1CO0lBQ25CQyxtQkFBbUI7SUFDbkJDLHFCQUFxQjtJQUNyQkMscUJBQXFCO0lBRXJCLHFDQUFxQztJQUNyQ0MsMkJBQTJCO0lBQzNCQyxpQ0FBaUM7SUFDakNDLCtCQUErQjtJQUMvQkMsa0NBQWtDO0lBQ2xDQyxvQ0FBb0M7SUFDcENDLG9DQUFvQztJQUVwQyxxQkFBcUI7SUFDckJDLE1BQU07SUFDTkMsUUFBUTtJQUNSQyxNQUFNO0lBQ05DLFFBQVE7SUFDUkMsYUFBYTtJQUNiQyxRQUFRO0FBQ1YsRUFBQztBQUVELG1CQUFtQjtBQUNaLE1BQU1DLGNBQWM7SUFDekJDLE9BQU87SUFDUEMsY0FBYztJQUNkQyxhQUFhO0lBQ2JDLE9BQU87SUFDUEMsVUFBVTtJQUNWQyxlQUFlO0lBQ2ZDLGFBQWE7SUFDYkMsWUFBWTtBQUNkLEVBQUM7QUFFRCxnQkFBZ0I7QUFDVCxlQUFlQyxZQUFZVixNQUFjO0lBQzlDLElBQUk7UUFDRixJQUFJLENBQUNBLFVBQVUsT0FBT0EsV0FBVyxVQUFVO1lBQ3pDVyxRQUFRQyxLQUFLLENBQUMsMkNBQTJDWjtZQUN6RCxPQUFPO1FBQ1Q7UUFFQSxNQUFNYSxVQUFVLE1BQU01RCwwREFBTUEsQ0FBQ0QsdURBQUdBLENBQUNjLHlDQUFFQSxFQUFFbUMsWUFBWUMsS0FBSyxFQUFFRjtRQUN4RCxJQUFJYSxRQUFRQyxNQUFNLElBQUk7WUFDcEIsTUFBTUMsT0FBT0YsUUFBUUUsSUFBSTtZQUV6Qix1Q0FBdUM7WUFDdkMsTUFBTUMsU0FBUztnQkFDYmhELE1BQU1pRCxPQUFPRixJQUFJLENBQUNoRCxZQUFZQyxJQUFJLENBQUMsSUFBSTtnQkFDdkNDLE9BQU9nRCxPQUFPRixJQUFJLENBQUNoRCxZQUFZRSxLQUFLLENBQUMsSUFBSTtnQkFDekNDLFFBQVErQyxPQUFPRixJQUFJLENBQUNoRCxZQUFZRyxNQUFNLENBQUMsSUFBSTtnQkFDM0NDLGNBQWM4QyxPQUFPRixJQUFJLENBQUNoRCxZQUFZSSxZQUFZLENBQUMsSUFBSTtnQkFDdkRDLFlBQVk2QyxPQUFPRixJQUFJLENBQUNoRCxZQUFZSyxVQUFVLENBQUMsSUFBSTtnQkFDbkRDLHVCQUF1QjZDLFFBQVFILElBQUksQ0FBQ2hELFlBQVlNLHFCQUFxQixDQUFDLElBQUk7Z0JBQzFFQyxNQUFNMkMsT0FBT0YsSUFBSSxDQUFDaEQsWUFBWU8sSUFBSSxDQUFDLElBQUk7Z0JBQ3ZDQyxZQUFZd0MsSUFBSSxDQUFDaEQsWUFBWVEsVUFBVSxDQUFDLEVBQUU0QyxZQUFZO2dCQUN0RDNDLFlBQVk0QyxPQUFPTCxJQUFJLENBQUNoRCxZQUFZUyxVQUFVLENBQUMsSUFBSTtnQkFDbkRDLHFCQUFxQnNDLElBQUksQ0FBQ2hELFlBQVlVLG1CQUFtQixDQUFDLEVBQUUwQyxZQUFZO2dCQUN4RXpDLFlBQVlxQyxJQUFJLENBQUNoRCxZQUFZVyxVQUFVLENBQUMsRUFBRXlDLFlBQVksSUFBSUU7Z0JBQzFEakMscUJBQXFCZ0MsT0FBT0wsSUFBSSxDQUFDaEQsWUFBWXFCLG1CQUFtQixDQUFDLElBQUsyQixDQUFBQSxJQUFJLENBQUNoRCxZQUFZTyxJQUFJLENBQUMsS0FBSyxVQUFVLEtBQUssR0FBRTtnQkFFbEgscUNBQXFDO2dCQUNyQ2UsMkJBQTJCNkIsUUFBUUgsSUFBSSxDQUFDaEQsWUFBWXNCLHlCQUF5QixDQUFDLElBQUk7Z0JBQ2xGQyxpQ0FBaUN5QixJQUFJLENBQUNoRCxZQUFZdUIsK0JBQStCLENBQUMsRUFBRTZCLFlBQVk7Z0JBQ2hHNUIsK0JBQStCNkIsT0FBT0wsSUFBSSxDQUFDaEQsWUFBWXdCLDZCQUE2QixDQUFDLElBQUk7Z0JBQ3pGQyxrQ0FBa0M0QixPQUFPTCxJQUFJLENBQUNoRCxZQUFZeUIsZ0NBQWdDLENBQUMsSUFBSTtnQkFDL0ZDLG9DQUFvQ3dCLE9BQU9GLElBQUksQ0FBQ2hELFlBQVkwQixrQ0FBa0MsQ0FBQyxJQUFJO2dCQUNuR0Msb0NBQW9DcUIsSUFBSSxDQUFDaEQsWUFBWTJCLGtDQUFrQyxDQUFDLEVBQUV5QixZQUFZO1lBQ3hHO1lBRUFSLFFBQVFXLEdBQUcsQ0FBQyx1QkFBdUJOO1lBQ25DLE9BQU9BO1FBQ1Q7UUFDQSxPQUFPO0lBQ1QsRUFBRSxPQUFPSixPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU8sS0FBSyxxREFBcUQ7O0lBQ25FO0FBQ0Y7QUFFQSxrQkFBa0I7QUFDWCxlQUFlVyxjQUFjdkIsTUFBYztJQUNoRCxJQUFJO1FBQ0YsSUFBSSxDQUFDQSxVQUFVLE9BQU9BLFdBQVcsVUFBVTtZQUN6Q1csUUFBUUMsS0FBSyxDQUFDLDZDQUE2Q1o7WUFDM0QsT0FBTztnQkFBRXJCLFFBQVE7WUFBRTtRQUNyQjtRQUVBLE1BQU1rQyxVQUFVLE1BQU01RCwwREFBTUEsQ0FBQ0QsdURBQUdBLENBQUNjLHlDQUFFQSxFQUFFbUMsWUFBWUMsS0FBSyxFQUFFRjtRQUN4RCxJQUFJYSxRQUFRQyxNQUFNLElBQUk7WUFDcEIsTUFBTUMsT0FBT0YsUUFBUUUsSUFBSTtZQUN6QixNQUFNQyxTQUFTO2dCQUNickMsUUFBUXlDLE9BQU9MLElBQUksQ0FBQ2hELFlBQVlZLE1BQU0sQ0FBQyxJQUFJO1lBQzdDO1lBRUFnQyxRQUFRVyxHQUFHLENBQUMseUJBQXlCTjtZQUNyQyxPQUFPQTtRQUNUO1FBQ0EsT0FBTztZQUFFckMsUUFBUTtRQUFFO0lBQ3JCLEVBQUUsT0FBT2lDLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLDhCQUE4QkE7UUFDNUMsT0FBTztZQUFFakMsUUFBUTtRQUFFLEVBQUUscUNBQXFDOztJQUM1RDtBQUNGO0FBRUEsNkJBQTZCO0FBQ3RCLGVBQWU2QyxrQkFBa0J4QixNQUFjO0lBQ3BELElBQUk7UUFDRixNQUFNeUIsVUFBVXpFLHVEQUFHQSxDQUFDYyx5Q0FBRUEsRUFBRW1DLFlBQVlDLEtBQUssRUFBRUY7UUFDM0MsTUFBTWEsVUFBVSxNQUFNNUQsMERBQU1BLENBQUN3RTtRQUM3QixJQUFJWixRQUFRQyxNQUFNLElBQUk7WUFDcEIsTUFBTUMsT0FBT0YsUUFBUUUsSUFBSTtZQUN6QixNQUFNOUIsb0JBQW9COEIsSUFBSSxDQUFDaEQsWUFBWWtCLGlCQUFpQixDQUFDLElBQUk7WUFDakUsSUFBSUMsb0JBQW9CNkIsSUFBSSxDQUFDaEQsWUFBWW1CLGlCQUFpQixDQUFDLElBQUk7WUFDL0QsTUFBTUMsc0JBQXNCNEIsSUFBSSxDQUFDaEQsWUFBWW9CLG1CQUFtQixDQUFDLEVBQUVnQztZQUVuRSwwQkFBMEI7WUFDMUIsTUFBTU8sUUFBUSxJQUFJTDtZQUNsQixNQUFNTSxXQUFXLENBQUN4Qyx1QkFDaEJBLG9CQUFvQnlDLFlBQVksT0FBT0YsTUFBTUUsWUFBWTtZQUUzRCxvRUFBb0U7WUFDcEUsSUFBSUQsWUFBWXpDLG9CQUFvQixHQUFHO2dCQUNyQ3lCLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLDhDQUE4QyxFQUFFdEIsT0FBTyxNQUFNLEVBQUVkLGtCQUFrQixDQUFDLENBQUM7Z0JBQ2hHLE1BQU1oQyw2REFBU0EsQ0FBQ3VFLFNBQVM7b0JBQ3ZCLENBQUMxRCxZQUFZbUIsaUJBQWlCLENBQUMsRUFBRTtnQkFDbkM7Z0JBQ0FBLG9CQUFvQjtnQkFFcEIsZ0RBQWdEO2dCQUNoRCxJQUFJO29CQUNGLE1BQU0yQyxxQkFBcUI3QjtnQkFDN0IsRUFBRSxPQUFPWSxPQUFPO29CQUNkRCxRQUFRQyxLQUFLLENBQUMsa0RBQWtEQTtnQkFDbEU7WUFDRjtZQUVBLE9BQU87Z0JBQ0wzQixtQkFBbUJBO2dCQUNuQkMsbUJBQW1CQTtnQkFDbkI0Qyx1QkFBdUJDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHLEtBQUs5QztZQUMxQztRQUNGO1FBQ0EsT0FBTztZQUFFRCxtQkFBbUI7WUFBR0MsbUJBQW1CO1lBQUc0Qyx1QkFBdUI7UUFBRztJQUNqRixFQUFFLE9BQU9sQixPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ2pELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLG1CQUFtQjtBQUNaLGVBQWVxQixlQUFlakMsTUFBYyxFQUFFZSxJQUFTO0lBQzVELElBQUk7UUFDRixNQUFNN0QsNkRBQVNBLENBQUNGLHVEQUFHQSxDQUFDYyx5Q0FBRUEsRUFBRW1DLFlBQVlDLEtBQUssRUFBRUYsU0FBU2U7SUFDdEQsRUFBRSxPQUFPSCxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBLGtCQUFrQjtBQUNYLGVBQWVzQixlQUFlbEMsTUFBYyxFQUFFbUMsZUFLcEQ7SUFDQyxJQUFJO1FBQ0YsTUFBTUMsY0FBYztZQUNsQixDQUFDckUsWUFBWWlDLE1BQU0sQ0FBQyxFQUFFQTtZQUN0QixDQUFDakMsWUFBWTRCLElBQUksQ0FBQyxFQUFFd0MsZ0JBQWdCeEMsSUFBSTtZQUN4QyxDQUFDNUIsWUFBWTZCLE1BQU0sQ0FBQyxFQUFFdUMsZ0JBQWdCdkMsTUFBTTtZQUM1QyxDQUFDN0IsWUFBWWdDLFdBQVcsQ0FBQyxFQUFFb0MsZ0JBQWdCcEMsV0FBVztZQUN0RCxDQUFDaEMsWUFBWStCLE1BQU0sQ0FBQyxFQUFFcUMsZ0JBQWdCckMsTUFBTSxJQUFJO1lBQ2hELENBQUMvQixZQUFZOEIsSUFBSSxDQUFDLEVBQUVsQyx5REFBU0EsQ0FBQzBFLEdBQUc7UUFDbkM7UUFFQSxNQUFNM0UsMERBQU1BLENBQUNOLDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlFLFlBQVksR0FBR2lDO0lBQ3pELEVBQUUsT0FBT3hCLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsTUFBTUE7SUFDUjtBQUNGO0FBRUEsbUJBQW1CO0FBQ1osZUFBZTBCLGdCQUFnQnRDLE1BQWMsRUFBRXVDLGFBQWEsRUFBRTtJQUNuRSxJQUFJO1FBQ0YsSUFBSSxDQUFDdkMsVUFBVSxPQUFPQSxXQUFXLFVBQVU7WUFDekNXLFFBQVFDLEtBQUssQ0FBQywrQ0FBK0NaO1lBQzdELE9BQU8sRUFBRTtRQUNYO1FBRUEsa0ZBQWtGO1FBQ2xGLDBFQUEwRTtRQUMxRSxNQUFNd0MsSUFBSW5GLHlEQUFLQSxDQUNiRCw4REFBVUEsQ0FBQ1UseUNBQUVBLEVBQUVtQyxZQUFZRSxZQUFZLEdBQ3ZDN0MseURBQUtBLENBQUNTLFlBQVlpQyxNQUFNLEVBQUUsTUFBTUEsU0FDaEN4Qyx5REFBS0EsQ0FBQytFO1FBR1IsTUFBTUUsZ0JBQWdCLE1BQU1oRiwyREFBT0EsQ0FBQytFO1FBQ3BDLE1BQU1yQyxlQUFlc0MsY0FBY0MsSUFBSSxDQUFDQyxHQUFHLENBQUMzRixDQUFBQSxNQUFRO2dCQUNsRDRGLElBQUk1RixJQUFJNEYsRUFBRTtnQkFDVixHQUFHNUYsSUFBSStELElBQUksRUFBRTtnQkFDYmxCLE1BQU03QyxJQUFJK0QsSUFBSSxFQUFFLENBQUNoRCxZQUFZOEIsSUFBSSxDQUFDLEVBQUVzQjtZQUN0QztRQUVBLDBEQUEwRDtRQUMxRGhCLGFBQWEwQyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7WUFDcEIsTUFBTUMsUUFBUUYsRUFBRWpELElBQUksSUFBSSxJQUFJd0IsS0FBSztZQUNqQyxNQUFNNEIsUUFBUUYsRUFBRWxELElBQUksSUFBSSxJQUFJd0IsS0FBSztZQUNqQyxPQUFPNEIsTUFBTUMsT0FBTyxLQUFLRixNQUFNRSxPQUFPLEdBQUcsbUJBQW1COztRQUM5RDtRQUVBLE9BQU8vQztJQUNULEVBQUUsT0FBT1MsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsK0JBQStCQTtRQUM3QyxPQUFPLEVBQUUsQ0FBQyw0REFBNEQ7O0lBQ3hFO0FBQ0Y7QUFFQSxnQkFBZ0I7QUFDVCxlQUFldUMsYUFBYWhGLFlBQW9CO0lBQ3JELElBQUk7UUFDRixNQUFNcUUsSUFBSW5GLHlEQUFLQSxDQUNiRCw4REFBVUEsQ0FBQ1UseUNBQUVBLEVBQUVtQyxZQUFZQyxLQUFLLEdBQ2hDNUMseURBQUtBLENBQUNTLFlBQVlLLFVBQVUsRUFBRSxNQUFNRDtRQUd0QyxNQUFNc0UsZ0JBQWdCLE1BQU1oRiwyREFBT0EsQ0FBQytFO1FBQ3BDLE9BQU9DLGNBQWNDLElBQUksQ0FBQ0MsR0FBRyxDQUFDM0YsQ0FBQUEsTUFBUTtnQkFDcEM0RixJQUFJNUYsSUFBSTRGLEVBQUU7Z0JBQ1YsR0FBRzVGLElBQUkrRCxJQUFJLEVBQUU7Z0JBQ2JyQyxZQUFZMUIsSUFBSStELElBQUksRUFBRSxDQUFDaEQsWUFBWVcsVUFBVSxDQUFDLEVBQUV5QztZQUNsRDtJQUNGLEVBQUUsT0FBT1AsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSwyQkFBMkI7QUFDcEIsZUFBZXdDLGlCQUFpQnBELE1BQWM7SUFDbkQsSUFBSTtRQUNGLE1BQU0wQixRQUFRLElBQUlMO1FBQ2xCLE1BQU1JLFVBQVV6RSx1REFBR0EsQ0FBQ2MseUNBQUVBLEVBQUVtQyxZQUFZQyxLQUFLLEVBQUVGO1FBRTNDLDREQUE0RDtRQUM1RCxNQUFNYSxVQUFVLE1BQU01RCwwREFBTUEsQ0FBQ3dFO1FBQzdCLElBQUlaLFFBQVFDLE1BQU0sSUFBSTtZQUNwQixNQUFNQyxPQUFPRixRQUFRRSxJQUFJO1lBQ3pCLE1BQU01QixzQkFBc0I0QixJQUFJLENBQUNoRCxZQUFZb0IsbUJBQW1CLENBQUMsRUFBRWdDO1lBQ25FLE1BQU1rQywyQkFBMkJ0QyxJQUFJLENBQUNoRCxZQUFZbUIsaUJBQWlCLENBQUMsSUFBSTtZQUV4RSwwQkFBMEI7WUFDMUIsTUFBTXlDLFdBQVcsQ0FBQ3hDLHVCQUNoQkEsb0JBQW9CeUMsWUFBWSxPQUFPRixNQUFNRSxZQUFZO1lBRTNELElBQUlELFlBQVkwQiwyQkFBMkIsR0FBRztnQkFDNUMseUNBQXlDO2dCQUN6QzFDLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLDJEQUEyRCxFQUFFdEIsUUFBUTtnQkFDbEYsTUFBTTlDLDZEQUFTQSxDQUFDdUUsU0FBUztvQkFDdkIsQ0FBQzFELFlBQVlrQixpQkFBaUIsQ0FBQyxFQUFFckIsNkRBQVNBLENBQUM7b0JBQzNDLENBQUNHLFlBQVltQixpQkFBaUIsQ0FBQyxFQUFFO29CQUNqQyxDQUFDbkIsWUFBWW9CLG1CQUFtQixDQUFDLEVBQUV4Qix5REFBU0EsQ0FBQzJGLFFBQVEsQ0FBQzVCO2dCQUN4RDtZQUNGLE9BQU87Z0JBQ0wsbUJBQW1CO2dCQUNuQixNQUFNeEUsNkRBQVNBLENBQUN1RSxTQUFTO29CQUN2QixDQUFDMUQsWUFBWWtCLGlCQUFpQixDQUFDLEVBQUVyQiw2REFBU0EsQ0FBQztvQkFDM0MsQ0FBQ0csWUFBWW1CLGlCQUFpQixDQUFDLEVBQUV0Qiw2REFBU0EsQ0FBQztvQkFDM0MsQ0FBQ0csWUFBWW9CLG1CQUFtQixDQUFDLEVBQUV4Qix5REFBU0EsQ0FBQzJGLFFBQVEsQ0FBQzVCO2dCQUN4RDtZQUNGO1FBQ0YsT0FBTztZQUNMLGlEQUFpRDtZQUNqRCxNQUFNeEUsNkRBQVNBLENBQUN1RSxTQUFTO2dCQUN2QixDQUFDMUQsWUFBWWtCLGlCQUFpQixDQUFDLEVBQUVyQiw2REFBU0EsQ0FBQztnQkFDM0MsQ0FBQ0csWUFBWW1CLGlCQUFpQixDQUFDLEVBQUV0Qiw2REFBU0EsQ0FBQztnQkFDM0MsQ0FBQ0csWUFBWW9CLG1CQUFtQixDQUFDLEVBQUV4Qix5REFBU0EsQ0FBQzJGLFFBQVEsQ0FBQzVCO1lBQ3hEO1FBQ0Y7SUFDRixFQUFFLE9BQU9kLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsTUFBTUE7SUFDUjtBQUNGO0FBRUEsNERBQTREO0FBQ3JELGVBQWUyQyxxQkFBcUJ2RCxNQUFjO0lBQ3ZELElBQUk7UUFDRixNQUFNeUIsVUFBVXpFLHVEQUFHQSxDQUFDYyx5Q0FBRUEsRUFBRW1DLFlBQVlDLEtBQUssRUFBRUY7UUFDM0MsTUFBTTlDLDZEQUFTQSxDQUFDdUUsU0FBUztZQUN2QixDQUFDMUQsWUFBWW1CLGlCQUFpQixDQUFDLEVBQUU7UUFDbkM7UUFDQXlCLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLHlDQUF5QyxFQUFFdEIsUUFBUTtJQUNsRSxFQUFFLE9BQU9ZLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLDRDQUE0Q0E7UUFDMUQsTUFBTUE7SUFDUjtBQUNGO0FBRUEsc0RBQXNEO0FBQy9DLGVBQWVpQixxQkFBcUI3QixNQUFjO0lBQ3ZELElBQUk7UUFDRixNQUFNLEVBQUU2QixzQkFBc0IyQixpQkFBaUIsRUFBRSxHQUFHLE1BQU0sdUpBQTZCO1FBQ3ZGLE9BQU8sTUFBTUEsa0JBQWtCeEQ7SUFDakMsRUFBRSxPQUFPWSxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyxvQ0FBb0NBO1FBQ2xELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLDhDQUE4QztBQUN2QyxlQUFlNkM7SUFDcEIsSUFBSTtRQUNGOUMsUUFBUVcsR0FBRyxDQUFDO1FBQ1osTUFBTW9DLGdCQUFnQixNQUFNakcsMkRBQU9BLENBQUNMLDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlDLEtBQUs7UUFDcEUsSUFBSXlELGFBQWE7UUFDakIsSUFBSUMsYUFBYTtRQUVqQixLQUFLLE1BQU0vQyxXQUFXNkMsY0FBY2hCLElBQUksQ0FBRTtZQUN4QyxJQUFJO2dCQUNGLE1BQU1iLHFCQUFxQmhCLFFBQVErQixFQUFFO2dCQUNyQ2U7WUFDRixFQUFFLE9BQU8vQyxPQUFPO2dCQUNkRCxRQUFRQyxLQUFLLENBQUMsQ0FBQyxrQ0FBa0MsRUFBRUMsUUFBUStCLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRWhDO2dCQUNsRWdEO1lBQ0Y7UUFDRjtRQUVBakQsUUFBUVcsR0FBRyxDQUFDLENBQUMsd0JBQXdCLEVBQUVxQyxXQUFXLFFBQVEsRUFBRUMsV0FBVyxPQUFPLENBQUM7UUFDL0UsT0FBTztZQUFFRDtZQUFZQztRQUFXO0lBQ2xDLEVBQUUsT0FBT2hELE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLHVDQUF1Q0E7UUFDckQsTUFBTUE7SUFDUjtBQUNGO0FBRUEsd0JBQXdCO0FBQ2pCLGVBQWVpRCxvQkFBb0I3RCxNQUFjLEVBQUVKLE1BQWM7SUFDdEUsSUFBSTtRQUNGLE1BQU02QixVQUFVekUsdURBQUdBLENBQUNjLHlDQUFFQSxFQUFFbUMsWUFBWUMsS0FBSyxFQUFFRjtRQUMzQyxNQUFNOUMsNkRBQVNBLENBQUN1RSxTQUFTO1lBQ3ZCLENBQUMxRCxZQUFZWSxNQUFNLENBQUMsRUFBRWYsNkRBQVNBLENBQUNnQztRQUNsQztJQUNGLEVBQUUsT0FBT2dCLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsTUFBTUE7SUFDUjtBQUNGO0FBVUEsb0JBQW9CO0FBQ2IsZUFBZWtELGdCQUFnQjlELE1BQWMsRUFBRStELFdBQXdCO0lBQzVFLElBQUk7UUFDRixJQUFJLENBQUMvRCxVQUFVLE9BQU9BLFdBQVcsVUFBVTtZQUN6QyxNQUFNLElBQUlnRSxNQUFNO1FBQ2xCO1FBRUEsd0JBQXdCO1FBQ3hCQyxvQkFBb0JGO1FBRXBCLE1BQU10QyxVQUFVekUsdURBQUdBLENBQUNjLHlDQUFFQSxFQUFFbUMsWUFBWUMsS0FBSyxFQUFFRjtRQUMzQyxNQUFNOUMsNkRBQVNBLENBQUN1RSxTQUFTO1lBQ3ZCLENBQUMxRCxZQUFZYSxxQkFBcUIsQ0FBQyxFQUFFbUYsWUFBWUcsaUJBQWlCLENBQUNDLElBQUk7WUFDdkUsQ0FBQ3BHLFlBQVljLGlCQUFpQixDQUFDLEVBQUVrRixZQUFZSyxhQUFhLENBQUNELElBQUk7WUFDL0QsQ0FBQ3BHLFlBQVllLFlBQVksQ0FBQyxFQUFFaUYsWUFBWU0sUUFBUSxDQUFDRixJQUFJLEdBQUdHLFdBQVc7WUFDbkUsQ0FBQ3ZHLFlBQVlnQixRQUFRLENBQUMsRUFBRWdGLFlBQVloRixRQUFRLENBQUNvRixJQUFJO1lBQ2pELENBQUNwRyxZQUFZaUIsa0JBQWtCLENBQUMsRUFBRXJCLHlEQUFTQSxDQUFDMEUsR0FBRztRQUNqRDtRQUVBMUIsUUFBUVcsR0FBRyxDQUFDLDZDQUE2Q3RCO0lBQzNELEVBQUUsT0FBT1ksT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsOEJBQThCQTtRQUM1QyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSxtQkFBbUI7QUFDWixlQUFlMkQsZUFBZXZFLE1BQWM7SUFDakQsSUFBSTtRQUNGLElBQUksQ0FBQ0EsVUFBVSxPQUFPQSxXQUFXLFVBQVU7WUFDekNXLFFBQVFDLEtBQUssQ0FBQyw4Q0FBOENaO1lBQzVELE9BQU87UUFDVDtRQUVBLE1BQU1hLFVBQVUsTUFBTTVELDBEQUFNQSxDQUFDRCx1REFBR0EsQ0FBQ2MseUNBQUVBLEVBQUVtQyxZQUFZQyxLQUFLLEVBQUVGO1FBQ3hELElBQUlhLFFBQVFDLE1BQU0sSUFBSTtZQUNwQixNQUFNQyxPQUFPRixRQUFRRSxJQUFJO1lBRXpCLDhCQUE4QjtZQUM5QixJQUFJQSxJQUFJLENBQUNoRCxZQUFZYyxpQkFBaUIsQ0FBQyxFQUFFO2dCQUN2QyxNQUFNbUMsU0FBc0I7b0JBQzFCa0QsbUJBQW1CakQsT0FBT0YsSUFBSSxDQUFDaEQsWUFBWWEscUJBQXFCLENBQUMsSUFBSTtvQkFDckV3RixlQUFlbkQsT0FBT0YsSUFBSSxDQUFDaEQsWUFBWWMsaUJBQWlCLENBQUMsSUFBSTtvQkFDN0R3RixVQUFVcEQsT0FBT0YsSUFBSSxDQUFDaEQsWUFBWWUsWUFBWSxDQUFDLElBQUk7b0JBQ25EQyxVQUFVa0MsT0FBT0YsSUFBSSxDQUFDaEQsWUFBWWdCLFFBQVEsQ0FBQyxJQUFJO2dCQUNqRDtnQkFFQTRCLFFBQVFXLEdBQUcsQ0FBQztnQkFDWixPQUFPTjtZQUNUO1FBQ0Y7UUFFQUwsUUFBUVcsR0FBRyxDQUFDO1FBQ1osT0FBTztJQUNULEVBQUUsT0FBT1YsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsK0JBQStCQTtRQUM3QyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLCtEQUErRDtBQUN4RCxTQUFTNEQsZUFBZWxHLElBQVk7SUFDekMsTUFBTW1HLGVBQTBDO1FBQzlDLFNBQVM7UUFDVCxVQUFVO1FBQ1YsVUFBVTtRQUNWLFVBQVU7UUFDVixXQUFXO1FBQ1gsU0FBUztRQUNULFdBQVc7UUFDWCxRQUFRO1FBQ1IsWUFBWTtRQUNaLFdBQVcsSUFBTSxzQkFBc0I7SUFDekM7SUFFQSxPQUFPQSxZQUFZLENBQUNuRyxLQUFLLElBQUksR0FBRywwREFBMEQ7O0FBQzVGO0FBRUEsd0RBQXdEO0FBQ2pELGVBQWVvRyx5QkFBeUIxRSxNQUFjO0lBSTNELElBQUk7UUFDRixNQUFNLEVBQUUwRSwwQkFBMEJDLGdCQUFnQixFQUFFLEdBQUcsTUFBTSxxSkFBNEI7UUFDekYsTUFBTTNELFNBQVMsTUFBTTJELGlCQUFpQjNFO1FBQ3RDLE9BQU87WUFDTDRFLGVBQWU1RCxPQUFPNEQsYUFBYTtZQUNuQ0MsZUFBZTdELE9BQU82RCxhQUFhO1FBQ3JDO0lBQ0YsRUFBRSxPQUFPakUsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMseUNBQXlDQTtRQUN2RCxPQUFPO1lBQUVnRSxlQUFlO1lBQU9DLGVBQWU7UUFBRTtJQUNsRDtBQUNGO0FBRUEsbURBQW1EO0FBQzVDLFNBQVNDLHFCQUFxQnhHLElBQVk7SUFDL0MsTUFBTXlHLGdCQUEyQztRQUMvQyxTQUFTO1FBQ1QsV0FBVztRQUNYLFNBQVM7UUFDVCxXQUFXO1FBQ1gsUUFBUTtRQUNSLFlBQVk7UUFDWixXQUFXLEdBQU8sMEJBQTBCO0lBQzlDO0lBRUEsT0FBT0EsYUFBYSxDQUFDekcsS0FBSyxJQUFJLEdBQUcseUNBQXlDOztBQUM1RTtBQUVBLHFDQUFxQztBQUM5QixTQUFTMEcsb0JBQW9CMUcsSUFBWTtJQUM5QyxNQUFNMkcsbUJBQThDO1FBQ2xELFNBQVM7UUFDVCxVQUFVO1FBQ1YsVUFBVTtRQUNWLFVBQVU7UUFDVixXQUFXO1FBQ1gsU0FBUztRQUNULFdBQVc7UUFDWCxRQUFRO1FBQ1IsWUFBWTtRQUNaLFdBQVc7UUFDWCxPQUFPO1FBQ1AsUUFBUTtRQUNSLFFBQVE7UUFDUixRQUFRO1FBQ1IsUUFBUTtRQUNSLFFBQVEsR0FBVSxzQkFBc0I7SUFDMUM7SUFFQSxPQUFPQSxnQkFBZ0IsQ0FBQzNHLEtBQUssSUFBSSxFQUFFLHFDQUFxQzs7QUFDMUU7QUFFQSx5RUFBeUU7QUFDbEUsZUFBZTRHLGtCQUFrQmxGLE1BQWM7SUFDcEQsSUFBSTtRQUNGLE1BQU1tRixXQUFXLE1BQU16RSxZQUFZVjtRQUNuQyxJQUFJLENBQUNtRixVQUFVO1lBQ2IsT0FBTztnQkFBRUMsU0FBUztnQkFBTUMsUUFBUTtZQUFzQjtRQUN4RDtRQUVBLHVEQUF1RDtRQUN2RCxJQUFJRixTQUFTN0csSUFBSSxLQUFLLFNBQVM7WUFDN0IsTUFBTUksYUFBYXlHLFNBQVN6RyxVQUFVLElBQUksSUFBSTJDO1lBQzlDLE1BQU1LLFFBQVEsSUFBSUw7WUFFbEIsd0VBQXdFO1lBQ3hFLE1BQU1pRSxtQkFBbUJ2RCxLQUFLd0QsS0FBSyxDQUFDLENBQUM3RCxNQUFNd0IsT0FBTyxLQUFLeEUsV0FBV3dFLE9BQU8sRUFBQyxJQUFNLFFBQU8sS0FBSyxLQUFLLEVBQUM7WUFFbEcsdUNBQXVDO1lBQ3ZDLE1BQU0xRSxhQUFhOEcsbUJBQW1CO1lBRXRDLDREQUE0RDtZQUM1RCxNQUFNRSxnQkFBZ0J6RCxLQUFLQyxHQUFHLENBQUMsR0FBRyxJQUFJc0Q7WUFFdEMzRSxRQUFRVyxHQUFHLENBQUMsOEJBQThCO2dCQUN4QzVDLFlBQVlBLFdBQVdrRCxZQUFZO2dCQUNuQ0YsT0FBT0EsTUFBTUUsWUFBWTtnQkFDekIwRDtnQkFDQTlHO2dCQUNBZ0g7WUFDRjtZQUVBLE9BQU87Z0JBQ0xKLFNBQVNJLGlCQUFpQjtnQkFDMUJILFFBQVFHLGlCQUFpQixJQUFJLHlCQUF5QkM7Z0JBQ3REQyxVQUFVRjtnQkFDVmhILFlBQVlBO1lBQ2Q7UUFDRjtRQUVBLDZDQUE2QztRQUM3QyxJQUFJMkcsU0FBUzVHLFVBQVUsRUFBRTtZQUN2QixNQUFNbUQsUUFBUSxJQUFJTDtZQUNsQixNQUFNK0QsVUFBVTFELFFBQVF5RCxTQUFTNUcsVUFBVTtZQUMzQyxNQUFNbUgsV0FBV04sVUFBVSxJQUFJckQsS0FBSzRELElBQUksQ0FBQyxDQUFDUixTQUFTNUcsVUFBVSxDQUFDMkUsT0FBTyxLQUFLeEIsTUFBTXdCLE9BQU8sRUFBQyxJQUFNLFFBQU8sS0FBSyxLQUFLLEVBQUM7WUFFaEgsT0FBTztnQkFDTGtDO2dCQUNBQyxRQUFRRCxVQUFVLDhCQUE4Qks7Z0JBQ2hEQztnQkFDQWxILFlBQVkyRyxTQUFTM0csVUFBVSxJQUFJO1lBQ3JDO1FBQ0Y7UUFFQSw2RUFBNkU7UUFDN0UsTUFBTXlHLG1CQUFtQkQsb0JBQW9CRyxTQUFTN0csSUFBSTtRQUMxRCxNQUFNc0gsb0JBQW9CVCxTQUFTM0csVUFBVSxJQUFJO1FBQ2pELE1BQU1rSCxXQUFXM0QsS0FBS0MsR0FBRyxDQUFDLEdBQUdpRCxtQkFBbUJXO1FBQ2hELE1BQU1SLFVBQVVNLFlBQVk7UUFFNUIsT0FBTztZQUNMTjtZQUNBQyxRQUFRRCxVQUFVLENBQUMsc0JBQXNCLEVBQUVILGlCQUFpQixvQ0FBb0MsQ0FBQyxHQUFHUTtZQUNwR0M7WUFDQWxILFlBQVlvSDtRQUNkO0lBQ0YsRUFBRSxPQUFPaEYsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsK0JBQStCQTtRQUM3QyxPQUFPO1lBQUV3RSxTQUFTO1lBQU1DLFFBQVE7UUFBNkI7SUFDL0Q7QUFDRjtBQUVBLG9EQUFvRDtBQUM3QyxlQUFlUSxxQkFBcUI3RixNQUFjLEVBQUU4RixPQUFlLEVBQUVDLGdCQUF1QjtJQUNqRyxJQUFJO1FBQ0YsTUFBTXRFLFVBQVV6RSx1REFBR0EsQ0FBQ2MseUNBQUVBLEVBQUVtQyxZQUFZQyxLQUFLLEVBQUVGO1FBRTNDLElBQUk4RixZQUFZLFNBQVM7WUFDdkIsNERBQTREO1lBQzVELE1BQU01SSw2REFBU0EsQ0FBQ3VFLFNBQVM7Z0JBQ3ZCLENBQUMxRCxZQUFZUSxVQUFVLENBQUMsRUFBRTtZQUM1QjtRQUNGLE9BQU87WUFDTCxpQ0FBaUM7WUFDakMsSUFBSXlIO1lBRUosSUFBSUQsa0JBQWtCO2dCQUNwQkMsYUFBYUQ7WUFDZixPQUFPO2dCQUNMLDBDQUEwQztnQkFDMUMsTUFBTUUsZUFBZWpCLG9CQUFvQmM7Z0JBQ3pDLE1BQU1wRSxRQUFRLElBQUlMO2dCQUNsQjJFLGFBQWEsSUFBSTNFLEtBQUtLLE1BQU13QixPQUFPLEtBQU0rQyxlQUFlLEtBQUssS0FBSyxLQUFLO1lBQ3pFO1lBRUEsTUFBTS9JLDZEQUFTQSxDQUFDdUUsU0FBUztnQkFDdkIsQ0FBQzFELFlBQVlRLFVBQVUsQ0FBQyxFQUFFWix5REFBU0EsQ0FBQzJGLFFBQVEsQ0FBQzBDO1lBQy9DO1lBRUFyRixRQUFRVyxHQUFHLENBQUMsQ0FBQyw2QkFBNkIsRUFBRXRCLE9BQU8sSUFBSSxFQUFFZ0csV0FBV3BFLFlBQVksSUFBSTtRQUN0RjtJQUNGLEVBQUUsT0FBT2hCLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsTUFBTUE7SUFDUjtBQUNGO0FBRUEsbUNBQW1DO0FBQzVCLFNBQVNzRixpQkFBaUI1SCxJQUFZO0lBQzNDLE1BQU02SCxrQkFBNkM7UUFDakQsU0FBUztRQUNULFVBQVU7UUFDVixVQUFVO1FBQ1YsVUFBVTtRQUNWLE9BQU87UUFDUCxRQUFRO1FBQ1IsUUFBUTtRQUNSLFFBQVE7UUFDUixRQUFRO1FBQ1IsUUFBUTtRQUNSLFdBQVc7UUFDWCxTQUFTO1FBQ1QsV0FBVztRQUNYLFFBQVE7UUFDUixZQUFZO1FBQ1osV0FBVyxLQUFLLHNCQUFzQjtJQUN4QztJQUVBLE9BQU9BLGVBQWUsQ0FBQzdILEtBQUssSUFBSTtBQUNsQztBQUVBLDBFQUEwRTtBQUNuRSxlQUFlOEgscUJBQXFCcEcsTUFBYyxFQUFFcUcsT0FBZSxFQUFFUCxPQUFlO0lBQ3pGLElBQUk7UUFDRm5GLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLG1EQUFtRCxFQUFFdEIsUUFBUTtRQUMxRVcsUUFBUVcsR0FBRyxDQUFDLENBQUMsZ0JBQWdCLEVBQUUrRSxRQUFRLEdBQUcsRUFBRVAsU0FBUztRQUVyRCw4REFBOEQ7UUFDOUQsSUFBSU8sWUFBWSxXQUFXUCxZQUFZLFNBQVM7WUFDOUNuRixRQUFRVyxHQUFHLENBQUM7WUFDWlgsUUFBUVcsR0FBRyxDQUFDLENBQUMsOEJBQThCLEVBQUUrRSxRQUFRLFVBQVUsRUFBRVAsU0FBUztZQUMxRTtRQUNGO1FBRUFuRixRQUFRVyxHQUFHLENBQUMsQ0FBQyxxQ0FBcUMsRUFBRXRCLE9BQU8sZ0JBQWdCLEVBQUVxRyxRQUFRLElBQUksRUFBRVAsU0FBUztRQUVwRyxrREFBa0Q7UUFDbEQsTUFBTWpGLFVBQVUsTUFBTTVELDBEQUFNQSxDQUFDRCx1REFBR0EsQ0FBQ2MseUNBQUVBLEVBQUVtQyxZQUFZQyxLQUFLLEVBQUVGO1FBQ3hELElBQUksQ0FBQ2EsUUFBUUMsTUFBTSxJQUFJO1lBQ3JCSCxRQUFRVyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUEsTUFBTTZELFdBQVd0RSxRQUFRRSxJQUFJO1FBQzdCLE1BQU0zQyxhQUFhK0csUUFBUSxDQUFDcEgsWUFBWUssVUFBVSxDQUFDO1FBQ25ELE1BQU1rSSxrQkFBa0JuQixRQUFRLENBQUNwSCxZQUFZTSxxQkFBcUIsQ0FBQztRQUVuRXNDLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixDQUFDO1FBQ3BDWCxRQUFRVyxHQUFHLENBQUMsQ0FBQyxrQkFBa0IsRUFBRWxELGNBQWMsUUFBUTtRQUN2RHVDLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLHVCQUF1QixFQUFFZ0YsaUJBQWlCO1FBQ3ZEM0YsUUFBUVcsR0FBRyxDQUFDLENBQUMsZ0JBQWdCLEVBQUU2RCxRQUFRLENBQUNwSCxZQUFZQyxJQUFJLENBQUMsRUFBRTtRQUUzRCxJQUFJLENBQUNJLFlBQVk7WUFDZnVDLFFBQVFXLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQSxJQUFJZ0YsaUJBQWlCO1lBQ25CM0YsUUFBUVcsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBWCxRQUFRVyxHQUFHLENBQUMsQ0FBQywrQkFBK0IsRUFBRWxELFlBQVk7UUFFMUQscUNBQXFDO1FBQ3JDLE1BQU1vRSxJQUFJbkYseURBQUtBLENBQ2JELDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlDLEtBQUssR0FDaEM1Qyx5REFBS0EsQ0FBQ1MsWUFBWUksWUFBWSxFQUFFLE1BQU1DLGFBQ3RDWix5REFBS0EsQ0FBQztRQUdSLE1BQU1pRixnQkFBZ0IsTUFBTWhGLDJEQUFPQSxDQUFDK0U7UUFFcEMsSUFBSUMsY0FBYzhELEtBQUssRUFBRTtZQUN2QjVGLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLDJCQUEyQixFQUFFbEQsWUFBWTtZQUN0RHVDLFFBQVFXLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQSxNQUFNa0YsY0FBYy9ELGNBQWNDLElBQUksQ0FBQyxFQUFFO1FBQ3pDLE1BQU0rRCxhQUFhRCxZQUFZNUQsRUFBRTtRQUNqQyxNQUFNOEQsZUFBZUYsWUFBWXpGLElBQUk7UUFDckMsTUFBTTRGLGNBQWNULGlCQUFpQko7UUFFckNuRixRQUFRVyxHQUFHLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQztRQUMvQlgsUUFBUVcsR0FBRyxDQUFDLENBQUMsa0JBQWtCLEVBQUVtRixZQUFZO1FBQzdDOUYsUUFBUVcsR0FBRyxDQUFDLENBQUMsb0JBQW9CLEVBQUVvRixZQUFZLENBQUMzSSxZQUFZQyxJQUFJLENBQUMsRUFBRTtRQUNuRTJDLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFcUYsYUFBYTtRQUNoRGhHLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLFdBQVcsRUFBRXdFLFNBQVM7UUFFbkMsSUFBSWEsY0FBYyxHQUFHO1lBQ25CaEcsUUFBUVcsR0FBRyxDQUFDLENBQUMsOEJBQThCLENBQUM7WUFFNUMsaUNBQWlDO1lBQ2pDLE1BQU11QyxvQkFBb0I0QyxZQUFZRTtZQUN0Q2hHLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLFNBQVMsRUFBRXFGLFlBQVkscUJBQXFCLENBQUM7WUFFMUQsNERBQTREO1lBQzVELE1BQU1DLGNBQWM1Six1REFBR0EsQ0FBQ2MseUNBQUVBLEVBQUVtQyxZQUFZQyxLQUFLLEVBQUV1RztZQUMvQyxNQUFNdkosNkRBQVNBLENBQUMwSixhQUFhO2dCQUMzQixDQUFDN0ksWUFBWWtCLGlCQUFpQixDQUFDLEVBQUVyQiw2REFBU0EsQ0FBQztZQUM3QztZQUNBK0MsUUFBUVcsR0FBRyxDQUFDLENBQUMseUNBQXlDLENBQUM7WUFFdkQsZ0RBQWdEO1lBQ2hELE1BQU1HLFVBQVV6RSx1REFBR0EsQ0FBQ2MseUNBQUVBLEVBQUVtQyxZQUFZQyxLQUFLLEVBQUVGO1lBQzNDLE1BQU05Qyw2REFBU0EsQ0FBQ3VFLFNBQVM7Z0JBQ3ZCLENBQUMxRCxZQUFZTSxxQkFBcUIsQ0FBQyxFQUFFO1lBQ3ZDO1lBQ0FzQyxRQUFRVyxHQUFHLENBQUMsQ0FBQyw2Q0FBNkMsRUFBRXRCLFFBQVE7WUFFcEUsNENBQTRDO1lBQzVDLE1BQU1rQyxlQUFldUUsWUFBWTtnQkFDL0I5RyxNQUFNO2dCQUNOQyxRQUFRK0c7Z0JBQ1I1RyxhQUFhLENBQUMsbUJBQW1CLEVBQUUrRixRQUFRLDZDQUE2QyxFQUFFWCxRQUFRLENBQUNwSCxZQUFZQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDekg7WUFDQTJDLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLDZDQUE2QyxDQUFDO1lBRTNEWCxRQUFRVyxHQUFHLENBQUMsQ0FBQyw4QkFBOEIsRUFBRXFGLFlBQVksZ0NBQWdDLEVBQUVGLFlBQVk7UUFDekcsT0FBTztZQUNMOUYsUUFBUVcsR0FBRyxDQUFDLENBQUMsdUNBQXVDLEVBQUV3RSxTQUFTO1lBQy9EbkYsUUFBUVcsR0FBRyxDQUFDLENBQUMsa0JBQWtCLENBQUMsRUFBRXVGLE9BQU9DLElBQUksQ0FBQztnQkFDNUMsU0FBUztnQkFBRyxVQUFVO2dCQUFLLFVBQVU7Z0JBQUssVUFBVTtZQUN0RDtRQUNGO0lBQ0YsRUFBRSxPQUFPbEcsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsc0NBQXNDQTtRQUNwREQsUUFBUUMsS0FBSyxDQUFDLG9CQUFvQkE7SUFDbEMsa0RBQWtEO0lBQ3BEO0FBQ0Y7QUFFQSwyREFBMkQ7QUFDcEQsZUFBZW1HLDJCQUEyQi9HLE1BQWM7SUFDN0QsSUFBSTtRQUNGVyxRQUFRVyxHQUFHLENBQUMsQ0FBQyw4Q0FBOEMsRUFBRXRCLFFBQVE7UUFFckUsTUFBTW1GLFdBQVcsTUFBTXpFLFlBQVlWO1FBQ25DLElBQUksQ0FBQ21GLFVBQVU7WUFDYnhFLFFBQVFXLEdBQUcsQ0FBQztZQUNaLE9BQU87Z0JBQUUwRixTQUFTO2dCQUFPQyxTQUFTO1lBQWlCO1FBQ3JEO1FBRUF0RyxRQUFRVyxHQUFHLENBQUMsQ0FBQyxhQUFhLENBQUM7UUFDM0JYLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLFdBQVcsRUFBRTZELFNBQVNuSCxJQUFJLEVBQUU7UUFDekMyQyxRQUFRVyxHQUFHLENBQUMsQ0FBQyxXQUFXLEVBQUU2RCxTQUFTN0csSUFBSSxFQUFFO1FBQ3pDcUMsUUFBUVcsR0FBRyxDQUFDLENBQUMsa0JBQWtCLEVBQUU2RCxTQUFTL0csVUFBVSxJQUFJLFFBQVE7UUFDaEV1QyxRQUFRVyxHQUFHLENBQUMsQ0FBQyxxQkFBcUIsRUFBRTZELFNBQVM5RyxxQkFBcUIsRUFBRTtRQUVwRSxJQUFJOEcsU0FBUzdHLElBQUksS0FBSyxTQUFTO1lBQzdCLE9BQU87Z0JBQUUwSSxTQUFTO2dCQUFPQyxTQUFTO1lBQTJEO1FBQy9GO1FBRUEsSUFBSSxDQUFDOUIsU0FBUy9HLFVBQVUsRUFBRTtZQUN4QixPQUFPO2dCQUFFNEksU0FBUztnQkFBT0MsU0FBUztZQUFrQztRQUN0RTtRQUVBLElBQUk5QixTQUFTOUcscUJBQXFCLEVBQUU7WUFDbEMsT0FBTztnQkFBRTJJLFNBQVM7Z0JBQU9DLFNBQVM7WUFBa0M7UUFDdEU7UUFFQSw2QkFBNkI7UUFDN0IsTUFBTWIscUJBQXFCcEcsUUFBUSxTQUFTbUYsU0FBUzdHLElBQUk7UUFFekQsT0FBTztZQUFFMEksU0FBUztZQUFNQyxTQUFTLENBQUMsNkJBQTZCLEVBQUU5QixTQUFTN0csSUFBSSxDQUFDLEtBQUssQ0FBQztRQUFDO0lBQ3hGLEVBQUUsT0FBT3NDLE9BQVk7UUFDbkJELFFBQVFDLEtBQUssQ0FBQyxnREFBZ0RBO1FBQzlELE9BQU87WUFBRW9HLFNBQVM7WUFBT0MsU0FBUyxDQUFDLE9BQU8sRUFBRXJHLE9BQU9xRyxXQUFXLGlCQUFpQjtRQUFDO0lBQ2xGO0FBQ0Y7QUFFQSxpRUFBaUU7QUFDMUQsZUFBZUMscUJBQXFCbEgsTUFBYztJQUN2RCxJQUFJO1FBQ0YsTUFBTW1GLFdBQVcsTUFBTXpFLFlBQVlWO1FBQ25DLElBQUksQ0FBQ21GLFVBQVU7WUFDYixPQUFPO2dCQUNML0YscUJBQXFCO2dCQUNyQitILGlCQUFpQjtnQkFDakI3SSxNQUFNO2dCQUNOOEksbUJBQW1CO1lBQ3JCO1FBQ0Y7UUFFQSx1REFBdUQ7UUFDdkQsTUFBTUMsMEJBQTBCQyxxQ0FBcUNuQztRQUVyRSxJQUFJL0Ysc0JBQXNCK0YsU0FBUy9GLG1CQUFtQjtRQUV0RCxzRkFBc0Y7UUFDdEYsSUFBSWlJLHlCQUF5QjtZQUMzQmpJLHNCQUFzQitGLFNBQVMzRixnQ0FBZ0MsSUFBSSxHQUFHLCtDQUErQzs7UUFDdkgsT0FBTztZQUNMLHFGQUFxRjtZQUNyRixJQUFJLENBQUNKLHVCQUF1QitGLFNBQVM3RyxJQUFJLEtBQUssU0FBUztnQkFDckRjLHNCQUFzQjBGLHFCQUFxQkssU0FBUzdHLElBQUk7WUFDMUQ7UUFDRjtRQUVBLE9BQU87WUFDTGMscUJBQXFCQTtZQUNyQitILGlCQUFpQjNDLGVBQWVXLFNBQVM3RyxJQUFJO1lBQzdDQSxNQUFNNkcsU0FBUzdHLElBQUk7WUFDbkI4SSxtQkFBbUJDO1lBQ25CRSxzQkFBc0JwQyxTQUFTN0YsK0JBQStCO1FBQ2hFO0lBQ0YsRUFBRSxPQUFPc0IsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsNENBQTRDQTtRQUMxRCxPQUFPO1lBQ0x4QixxQkFBcUI7WUFDckIrSCxpQkFBaUI7WUFDakI3SSxNQUFNO1lBQ044SSxtQkFBbUI7UUFDckI7SUFDRjtBQUNGO0FBRUEsdURBQXVEO0FBQ2hELFNBQVNFLHFDQUFxQ25DLFFBQWE7SUFDaEUsSUFBSSxDQUFDQSxTQUFTOUYseUJBQXlCLElBQUksQ0FBQzhGLFNBQVM3RiwrQkFBK0IsRUFBRTtRQUNwRixPQUFPO0lBQ1Q7SUFFQSxNQUFNK0MsTUFBTSxJQUFJaEI7SUFDaEIsTUFBTW1HLFNBQVNyQyxTQUFTN0YsK0JBQStCO0lBRXZELE9BQU8rQyxNQUFNbUY7QUFDZjtBQUVBLDZEQUE2RDtBQUN0RCxlQUFlQyx5QkFBeUJ6SCxNQUFjLEVBQUUwSCxJQUFZLEVBQUVDLFNBQWlCLEVBQUVDLFVBQWtCLEVBQUU7SUFDbEgsSUFBSTtRQUNGLElBQUlGLFFBQVEsS0FBS0EsT0FBTyxLQUFLO1lBQzNCLE1BQU0sSUFBSTFELE1BQU07UUFDbEI7UUFFQSxJQUFJNEQsVUFBVSxLQUFLQSxVQUFVLEtBQUs7WUFDaEMsTUFBTSxJQUFJNUQsTUFBTTtRQUNsQjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNLEVBQUU2RCx3QkFBd0IsRUFBRSxHQUFHLE1BQU0scUpBQTRCO1FBQ3ZFLE1BQU1BLHlCQUF5QjdILFFBQVEwSDtRQUV2Qy9HLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLHNDQUFzQyxFQUFFdEIsT0FBTyxLQUFLLEVBQUUwSCxLQUFLLFNBQVMsRUFBRUMsV0FBVztRQUU5Rix5QkFBeUI7UUFDekIsTUFBTXpGLGVBQWVsQyxRQUFRO1lBQzNCTCxNQUFNO1lBQ05DLFFBQVE7WUFDUkcsYUFBYSxDQUFDLGtDQUFrQyxFQUFFMkgsS0FBSyxTQUFTLEVBQUVDLFdBQVc7UUFDL0U7UUFFQSxPQUFPO1lBQUVYLFNBQVM7UUFBSztJQUN6QixFQUFFLE9BQU9wRyxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyx5Q0FBeUNBO1FBQ3ZELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLGdFQUFnRTtBQUN6RCxlQUFla0gsMEJBQTBCOUgsTUFBYyxFQUFFK0gsU0FBaUI7SUFDL0UsSUFBSTtRQUNGLHFDQUFxQztRQUNyQyxNQUFNLEVBQUVDLHlCQUF5QixFQUFFLEdBQUcsTUFBTSxxSkFBNEI7UUFDeEUsTUFBTUEsMEJBQTBCaEk7UUFFaENXLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLHdDQUF3QyxFQUFFdEIsT0FBTyxJQUFJLEVBQUUrSCxXQUFXO1FBRS9FLHlCQUF5QjtRQUN6QixNQUFNN0YsZUFBZWxDLFFBQVE7WUFDM0JMLE1BQU07WUFDTkMsUUFBUTtZQUNSRyxhQUFhLENBQUMsaUNBQWlDLEVBQUVnSSxXQUFXO1FBQzlEO1FBRUEsT0FBTztZQUFFZixTQUFTO1FBQUs7SUFDekIsRUFBRSxPQUFPcEcsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMseUNBQXlDQTtRQUN2RCxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSxvREFBb0Q7QUFDN0MsZUFBZXFILHdCQUF3QmpJLE1BQWMsRUFBRWtJLGlCQUF5QjtJQUNyRixJQUFJO1FBQ0YsNEZBQTRGO1FBQzVGLE1BQU1DLGtCQUFrQjtZQUFDO1lBQUc7WUFBSTtTQUFHLENBQUNDLFFBQVEsQ0FBQ0Y7UUFDN0MsTUFBTUcscUJBQXFCSCxxQkFBcUIsTUFBTUEscUJBQXFCO1FBRTNFLElBQUksQ0FBQ0MsbUJBQW1CLENBQUNFLG9CQUFvQjtZQUMzQyxNQUFNLElBQUlyRSxNQUFNO1FBQ2xCO1FBRUEsTUFBTXZDLFVBQVV6RSx1REFBR0EsQ0FBQ2MseUNBQUVBLEVBQUVtQyxZQUFZQyxLQUFLLEVBQUVGO1FBQzNDLE1BQU05Qyw2REFBU0EsQ0FBQ3VFLFNBQVM7WUFDdkIsQ0FBQzFELFlBQVlxQixtQkFBbUIsQ0FBQyxFQUFFOEk7UUFDckM7UUFFQXZILFFBQVFXLEdBQUcsQ0FBQyxDQUFDLHNDQUFzQyxFQUFFdEIsT0FBTyxJQUFJLEVBQUVrSSxrQkFBa0IsUUFBUSxDQUFDO0lBQy9GLEVBQUUsT0FBT3RILE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLDZDQUE2Q0E7UUFDM0QsTUFBTUE7SUFDUjtBQUNGO0FBRUEsd0JBQXdCO0FBQ3hCLFNBQVNxRCxvQkFBb0JGLFdBQXdCO0lBQ25ELE1BQU0sRUFBRUcsaUJBQWlCLEVBQUVFLGFBQWEsRUFBRUMsUUFBUSxFQUFFdEYsUUFBUSxFQUFFLEdBQUdnRjtJQUVqRSxJQUFJLENBQUNHLHFCQUFxQkEsa0JBQWtCQyxJQUFJLEdBQUdtRSxNQUFNLEdBQUcsR0FBRztRQUM3RCxNQUFNLElBQUl0RSxNQUFNO0lBQ2xCO0lBRUEsSUFBSSxDQUFDSSxpQkFBaUIsQ0FBQyxhQUFhbUUsSUFBSSxDQUFDbkUsY0FBY0QsSUFBSSxLQUFLO1FBQzlELE1BQU0sSUFBSUgsTUFBTTtJQUNsQjtJQUVBLElBQUksQ0FBQ0ssWUFBWSxDQUFDLHlCQUF5QmtFLElBQUksQ0FBQ2xFLFNBQVNGLElBQUksR0FBR0csV0FBVyxLQUFLO1FBQzlFLE1BQU0sSUFBSU4sTUFBTTtJQUNsQjtJQUVBLElBQUksQ0FBQ2pGLFlBQVlBLFNBQVNvRixJQUFJLEdBQUdtRSxNQUFNLEdBQUcsR0FBRztRQUMzQyxNQUFNLElBQUl0RSxNQUFNO0lBQ2xCO0FBQ0Y7QUFlQSx5RUFBeUU7QUFDbEUsZUFBZXdFLGdCQUFnQkMsWUFBb0Q7SUFDeEYsSUFBSTtRQUNGLE1BQU1DLG1CQUFtQjtZQUN2QkMsT0FBT0YsYUFBYUUsS0FBSztZQUN6QjFCLFNBQVN3QixhQUFheEIsT0FBTztZQUM3QnRILE1BQU04SSxhQUFhOUksSUFBSTtZQUN2QmlKLGFBQWFILGFBQWFHLFdBQVc7WUFDckNDLFNBQVNKLGFBQWFJLE9BQU8sSUFBSSxFQUFFO1lBQ25DQyxXQUFXbkwseURBQVNBLENBQUMwRSxHQUFHO1lBQ3hCMEcsV0FBV04sYUFBYU0sU0FBUztRQUNuQztRQUVBcEksUUFBUVcsR0FBRyxDQUFDLHFDQUFxQ29IO1FBRWpELE1BQU1NLFNBQVMsTUFBTXRMLDBEQUFNQSxDQUFDTiw4REFBVUEsQ0FBQ1UseUNBQUVBLEVBQUVtQyxZQUFZTSxhQUFhLEdBQUdtSTtRQUN2RS9ILFFBQVFXLEdBQUcsQ0FBQyw0Q0FBNEMwSCxPQUFPcEcsRUFBRTtRQUVqRSxvQ0FBb0M7UUFDcEMsTUFBTXFHLFdBQVcsTUFBTWhNLDBEQUFNQSxDQUFDK0w7UUFDOUIsSUFBSUMsU0FBU25JLE1BQU0sSUFBSTtZQUNyQkgsUUFBUVcsR0FBRyxDQUFDLHNDQUFzQzJILFNBQVNsSSxJQUFJO1FBQ2pFLE9BQU87WUFDTEosUUFBUXVJLElBQUksQ0FBQztRQUNmO1FBRUEsT0FBT0YsT0FBT3BHLEVBQUU7SUFDbEIsRUFBRSxPQUFPaEMsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsOEJBQThCQTtRQUM1QyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSwrQkFBK0I7QUFDeEIsZUFBZXVJLHFCQUFxQm5KLE1BQWMsRUFBRXVDLGFBQWEsRUFBRTtJQUN4RSxJQUFJO1FBQ0YsSUFBSSxDQUFDdkMsVUFBVSxPQUFPQSxXQUFXLFVBQVU7WUFDekNXLFFBQVFDLEtBQUssQ0FBQyxvREFBb0RaO1lBQ2xFLE9BQU8sRUFBRTtRQUNYO1FBRUFXLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLGdDQUFnQyxFQUFFdEIsUUFBUTtRQUV2RCw2REFBNkQ7UUFDN0QsSUFBSW9KLGtCQUFrQkM7UUFFdEIsSUFBSTtZQUNGLDBDQUEwQztZQUMxQyxNQUFNQyxnQkFBZ0JqTSx5REFBS0EsQ0FDekJELDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlNLGFBQWEsR0FDeENqRCx5REFBS0EsQ0FBQyxlQUFlLE1BQU0sUUFDM0JDLDJEQUFPQSxDQUFDLGFBQWEsU0FDckJDLHlEQUFLQSxDQUFDK0U7WUFHUjZHLG1CQUFtQixNQUFNM0wsMkRBQU9BLENBQUM2TDtZQUNqQzNJLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLE1BQU0sRUFBRThILGlCQUFpQjFHLElBQUksQ0FBQzRGLE1BQU0sQ0FBQyw0QkFBNEIsQ0FBQztRQUNqRixFQUFFLE9BQU8xSCxPQUFPO1lBQ2RELFFBQVF1SSxJQUFJLENBQUMsbUVBQW1FdEk7WUFDaEYsaURBQWlEO1lBQ2pELE1BQU0wSSxnQkFBZ0JqTSx5REFBS0EsQ0FDekJELDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlNLGFBQWEsR0FDeENqRCx5REFBS0EsQ0FBQyxlQUFlLE1BQU0sUUFDM0JFLHlEQUFLQSxDQUFDK0U7WUFFUjZHLG1CQUFtQixNQUFNM0wsMkRBQU9BLENBQUM2TDtRQUNuQztRQUVBLElBQUk7WUFDRiw4Q0FBOEM7WUFDOUMsTUFBTUMsb0JBQW9CbE0seURBQUtBLENBQzdCRCw4REFBVUEsQ0FBQ1UseUNBQUVBLEVBQUVtQyxZQUFZTSxhQUFhLEdBQ3hDakQseURBQUtBLENBQUMsZUFBZSxNQUFNLGFBQzNCQSx5REFBS0EsQ0FBQyxXQUFXLGtCQUFrQjBDLFNBQ25DekMsMkRBQU9BLENBQUMsYUFBYSxTQUNyQkMseURBQUtBLENBQUMrRTtZQUdSOEcsdUJBQXVCLE1BQU01TCwyREFBT0EsQ0FBQzhMO1lBQ3JDNUksUUFBUVcsR0FBRyxDQUFDLENBQUMsTUFBTSxFQUFFK0gscUJBQXFCM0csSUFBSSxDQUFDNEYsTUFBTSxDQUFDLGdDQUFnQyxDQUFDO1FBQ3pGLEVBQUUsT0FBTzFILE9BQU87WUFDZEQsUUFBUXVJLElBQUksQ0FBQyx1RUFBdUV0STtZQUNwRixpREFBaUQ7WUFDakQsTUFBTTJJLG9CQUFvQmxNLHlEQUFLQSxDQUM3QkQsOERBQVVBLENBQUNVLHlDQUFFQSxFQUFFbUMsWUFBWU0sYUFBYSxHQUN4Q2pELHlEQUFLQSxDQUFDLGVBQWUsTUFBTSxhQUMzQkEseURBQUtBLENBQUMsV0FBVyxrQkFBa0IwQyxTQUNuQ3hDLHlEQUFLQSxDQUFDK0U7WUFFUjhHLHVCQUF1QixNQUFNNUwsMkRBQU9BLENBQUM4TDtRQUN2QztRQUVBLE1BQU1oSixnQkFBZ0MsRUFBRTtRQUV4QyxrQ0FBa0M7UUFDbEM2SSxpQkFBaUIxRyxJQUFJLENBQUM4RyxPQUFPLENBQUN4TSxDQUFBQTtZQUM1QnVELGNBQWNrSixJQUFJLENBQUM7Z0JBQ2pCN0csSUFBSTVGLElBQUk0RixFQUFFO2dCQUNWLEdBQUc1RixJQUFJK0QsSUFBSSxFQUFFO2dCQUNiK0gsV0FBVzlMLElBQUkrRCxJQUFJLEdBQUcrSCxTQUFTLEVBQUUzSCxZQUFZLElBQUlFO1lBQ25EO1FBQ0Y7UUFFQSxzQ0FBc0M7UUFDdENnSSxxQkFBcUIzRyxJQUFJLENBQUM4RyxPQUFPLENBQUN4TSxDQUFBQTtZQUNoQ3VELGNBQWNrSixJQUFJLENBQUM7Z0JBQ2pCN0csSUFBSTVGLElBQUk0RixFQUFFO2dCQUNWLEdBQUc1RixJQUFJK0QsSUFBSSxFQUFFO2dCQUNiK0gsV0FBVzlMLElBQUkrRCxJQUFJLEdBQUcrSCxTQUFTLEVBQUUzSCxZQUFZLElBQUlFO1lBQ25EO1FBQ0Y7UUFFQSx1Q0FBdUM7UUFDdkNkLGNBQWNzQyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsRUFBRStGLFNBQVMsQ0FBQzVGLE9BQU8sS0FBS0osRUFBRWdHLFNBQVMsQ0FBQzVGLE9BQU87UUFFeEUsTUFBTXdHLHFCQUFxQm5KLGNBQWNvSixLQUFLLENBQUMsR0FBR3BIO1FBQ2xENUIsUUFBUVcsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFb0ksbUJBQW1CcEIsTUFBTSxDQUFDLDZCQUE2QixDQUFDO1FBRWpGLE9BQU9vQjtJQUNULEVBQUUsT0FBTzlJLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVBLHlDQUF5QztBQUNsQyxlQUFlZ0osb0JBQW9CckgsYUFBYSxFQUFFO0lBQ3ZELElBQUk7UUFDRixNQUFNQyxJQUFJbkYseURBQUtBLENBQ2JELDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlNLGFBQWEsR0FDeENoRCwyREFBT0EsQ0FBQyxhQUFhLFNBQ3JCQyx5REFBS0EsQ0FBQytFO1FBR1IsTUFBTUUsZ0JBQWdCLE1BQU1oRiwyREFBT0EsQ0FBQytFO1FBQ3BDLE1BQU1qQyxnQkFBZ0JrQyxjQUFjQyxJQUFJLENBQUNDLEdBQUcsQ0FBQzNGLENBQUFBLE1BQVE7Z0JBQ25ENEYsSUFBSTVGLElBQUk0RixFQUFFO2dCQUNWLEdBQUc1RixJQUFJK0QsSUFBSSxFQUFFO2dCQUNiK0gsV0FBVzlMLElBQUkrRCxJQUFJLEdBQUcrSCxTQUFTLEVBQUUzSCxZQUFZLElBQUlFO1lBQ25EO1FBRUEsT0FBT2Q7SUFDVCxFQUFFLE9BQU9LLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLG9DQUFvQ0E7UUFDbEQsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVBLHVDQUF1QztBQUNoQyxlQUFlaUosbUJBQW1CQyxjQUFzQjtJQUM3RCxJQUFJO1FBQ0YsSUFBSSxDQUFDQSxrQkFBa0IsT0FBT0EsbUJBQW1CLFVBQVU7WUFDekQsTUFBTSxJQUFJOUYsTUFBTTtRQUNsQjtRQUVBckQsUUFBUVcsR0FBRyxDQUFDLDBCQUEwQndJO1FBQ3RDLE1BQU0zTSw2REFBU0EsQ0FBQ0gsdURBQUdBLENBQUNjLHlDQUFFQSxFQUFFbUMsWUFBWU0sYUFBYSxFQUFFdUo7UUFDbkRuSixRQUFRVyxHQUFHLENBQUM7SUFDZCxFQUFFLE9BQU9WLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsTUFBTUE7SUFDUjtBQUNGO0FBRUEsNEJBQTRCO0FBQ3JCLGVBQWVtSix1QkFBdUJELGNBQXNCLEVBQUU5SixNQUFjO0lBQ2pGLElBQUk7UUFDRiw0RUFBNEU7UUFDNUUsTUFBTWdLLG9CQUFvQkMsS0FBS0MsS0FBSyxDQUFDQyxhQUFhQyxPQUFPLENBQUMsQ0FBQyxtQkFBbUIsRUFBRXBLLFFBQVEsS0FBSztRQUM3RixJQUFJLENBQUNnSyxrQkFBa0I1QixRQUFRLENBQUMwQixpQkFBaUI7WUFDL0NFLGtCQUFrQlAsSUFBSSxDQUFDSztZQUN2QkssYUFBYUUsT0FBTyxDQUFDLENBQUMsbUJBQW1CLEVBQUVySyxRQUFRLEVBQUVpSyxLQUFLSyxTQUFTLENBQUNOO1FBQ3RFO0lBQ0YsRUFBRSxPQUFPcEosT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsdUNBQXVDQTtJQUN2RDtBQUNGO0FBRUEsZ0NBQWdDO0FBQ3pCLFNBQVMySixtQkFBbUJULGNBQXNCLEVBQUU5SixNQUFjO0lBQ3ZFLElBQUk7UUFDRixNQUFNZ0ssb0JBQW9CQyxLQUFLQyxLQUFLLENBQUNDLGFBQWFDLE9BQU8sQ0FBQyxDQUFDLG1CQUFtQixFQUFFcEssUUFBUSxLQUFLO1FBQzdGLE9BQU9nSyxrQkFBa0I1QixRQUFRLENBQUMwQjtJQUNwQyxFQUFFLE9BQU9sSixPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyw0Q0FBNENBO1FBQzFELE9BQU87SUFDVDtBQUNGO0FBRUEsZ0NBQWdDO0FBQ3pCLFNBQVM0SiwyQkFBMkJqSyxhQUE2QixFQUFFUCxNQUFjO0lBQ3RGLElBQUk7UUFDRixNQUFNZ0ssb0JBQW9CQyxLQUFLQyxLQUFLLENBQUNDLGFBQWFDLE9BQU8sQ0FBQyxDQUFDLG1CQUFtQixFQUFFcEssUUFBUSxLQUFLO1FBQzdGLE9BQU9PLGNBQWNrSyxNQUFNLENBQUNoQyxDQUFBQSxlQUFnQixDQUFDdUIsa0JBQWtCNUIsUUFBUSxDQUFDSyxhQUFhN0YsRUFBRSxHQUFHMEYsTUFBTTtJQUNsRyxFQUFFLE9BQU8xSCxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyw0Q0FBNENBO1FBQzFELE9BQU87SUFDVDtBQUNGO0FBRUEsZ0VBQWdFO0FBQ3pELGVBQWU4Six1QkFBdUIxSyxNQUFjO0lBQ3pELElBQUk7UUFDRixJQUFJLENBQUNBLFVBQVUsT0FBT0EsV0FBVyxVQUFVO1lBQ3pDVyxRQUFRQyxLQUFLLENBQUMsc0RBQXNEWjtZQUNwRSxPQUFPLEVBQUU7UUFDWDtRQUVBVyxRQUFRVyxHQUFHLENBQUMsQ0FBQyx1Q0FBdUMsRUFBRXRCLFFBQVE7UUFFOUQscUNBQXFDO1FBQ3JDLE1BQU0ySyxtQkFBbUIsTUFBTXhCLHFCQUFxQm5KLFFBQVE7UUFFNUQsa0NBQWtDO1FBQ2xDLE1BQU1nSyxvQkFBb0JDLEtBQUtDLEtBQUssQ0FBQ0MsYUFBYUMsT0FBTyxDQUFDLENBQUMsbUJBQW1CLEVBQUVwSyxRQUFRLEtBQUs7UUFDN0YsTUFBTTRLLHNCQUFzQkQsaUJBQWlCRixNQUFNLENBQUNoQyxDQUFBQSxlQUNsREEsYUFBYTdGLEVBQUUsSUFDZixDQUFDb0gsa0JBQWtCNUIsUUFBUSxDQUFDSyxhQUFhN0YsRUFBRTtRQUc3Q2pDLFFBQVFXLEdBQUcsQ0FBQyxDQUFDLE1BQU0sRUFBRXNKLG9CQUFvQnRDLE1BQU0sQ0FBQyxxQkFBcUIsQ0FBQztRQUN0RSxPQUFPc0M7SUFDVCxFQUFFLE9BQU9oSyxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyx1Q0FBdUNBO1FBQ3JELE9BQU8sRUFBRTtJQUNYO0FBQ0Y7QUFFQSx5Q0FBeUM7QUFDbEMsZUFBZWlLLHVCQUF1QjdLLE1BQWM7SUFDekQsSUFBSTtRQUNGLE1BQU00SyxzQkFBc0IsTUFBTUYsdUJBQXVCMUs7UUFDekQsT0FBTzRLLG9CQUFvQnRDLE1BQU0sR0FBRztJQUN0QyxFQUFFLE9BQU8xSCxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyw0Q0FBNENBO1FBQzFELE9BQU87SUFDVDtBQUNGO0FBRUEsd0NBQXdDO0FBQ2pDLGVBQWVrSyxzQkFBc0I5SyxNQUFjO0lBQ3hELElBQUk7UUFDRixNQUFNd0MsSUFBSW5GLHlEQUFLQSxDQUNiRCw4REFBVUEsQ0FBQ1UseUNBQUVBLEVBQUVtQyxZQUFZRyxXQUFXLEdBQ3RDOUMseURBQUtBLENBQUMsVUFBVSxNQUFNMEMsU0FDdEIxQyx5REFBS0EsQ0FBQyxVQUFVLE1BQU0sWUFDdEJFLHlEQUFLQSxDQUFDO1FBR1IsTUFBTXVOLFdBQVcsTUFBTXROLDJEQUFPQSxDQUFDK0U7UUFDL0IsT0FBTyxDQUFDdUksU0FBU3hFLEtBQUs7SUFDeEIsRUFBRSxPQUFPM0YsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsdUNBQXVDQTtRQUNyRCxPQUFPO0lBQ1Q7QUFDRjtBQUVBLHFGQUFxRjtBQUM5RSxlQUFlb0ssdUJBQXVCaEwsTUFBYztJQUN6RCxJQUFJO1FBQ0Ysd0JBQXdCO1FBQ3hCLE1BQU1tRixXQUFXLE1BQU16RSxZQUFZVjtRQUNuQyxJQUFJLENBQUNtRixVQUFVO1lBQ2IsT0FBTztnQkFDTDhGLFNBQVM7Z0JBQ1Q1RixRQUFRO1lBQ1Y7UUFDRjtRQUVBLGlDQUFpQztRQUNqQyxJQUFJRixTQUFTN0csSUFBSSxLQUFLLFNBQVM7WUFDN0IsT0FBTztnQkFDTDJNLFNBQVM7Z0JBQ1Q1RixRQUFRO1lBQ1Y7UUFDRjtRQUVBLHdDQUF3QztRQUN4QyxNQUFNNkYsYUFBYSxNQUFNSixzQkFBc0I5SztRQUMvQyxJQUFJa0wsWUFBWTtZQUNkLE9BQU87Z0JBQ0xELFNBQVM7Z0JBQ1Q1RixRQUFRO1lBQ1Y7UUFDRjtRQUVBLE1BQU1oRCxNQUFNLElBQUloQjtRQUNoQixNQUFNOEosY0FBYzlJLElBQUkrSSxRQUFRO1FBRWhDLDBDQUEwQztRQUMxQyxJQUFJRCxjQUFjLE1BQU1BLGVBQWUsSUFBSTtZQUN6QyxPQUFPO2dCQUNMRixTQUFTO2dCQUNUNUYsUUFBUTtZQUNWO1FBQ0Y7UUFFQSx3QkFBd0I7UUFDeEIsTUFBTSxFQUFFZ0csZUFBZSxFQUFFLEdBQUcsTUFBTSw2SUFBd0I7UUFDMUQsTUFBTUMsZUFBZSxNQUFNRCxnQkFBZ0JoSjtRQUMzQyxJQUFJaUosY0FBYztZQUNoQixPQUFPO2dCQUNMTCxTQUFTO2dCQUNUNUYsUUFBUTtZQUNWO1FBQ0Y7UUFFQSx1QkFBdUI7UUFDdkIsTUFBTSxFQUFFa0csYUFBYSxFQUFFLEdBQUcsTUFBTSw2SUFBd0I7UUFDeEQsTUFBTUMsY0FBYyxNQUFNRCxjQUFjdkwsUUFBUXFDO1FBQ2hELElBQUltSixhQUFhO1lBQ2YsT0FBTztnQkFDTFAsU0FBUztnQkFDVDVGLFFBQVE7WUFDVjtRQUNGO1FBRUEsT0FBTztZQUFFNEYsU0FBUztRQUFLO0lBQ3pCLEVBQUUsT0FBT3JLLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLHNDQUFzQ0E7UUFDcEQsT0FBTztZQUFFcUssU0FBUztZQUFPNUYsUUFBUTtRQUE2RDtJQUNoRztBQUNGO0FBRUEsNEJBQTRCO0FBQ3JCLGVBQWVvRyx3QkFBd0J6TCxNQUFjLEVBQUVKLE1BQWMsRUFBRW1FLFdBQWdCO0lBQzVGLElBQUk7UUFDRixrQ0FBa0M7UUFDbEMsSUFBSW5FLFNBQVMsSUFBSTtZQUNmLE1BQU0sSUFBSW9FLE1BQU07UUFDbEI7UUFFQSxpQ0FBaUM7UUFDakMsTUFBTTBILGtCQUFrQixNQUFNVix1QkFBdUJoTDtRQUNyRCxJQUFJLENBQUMwTCxnQkFBZ0JULE9BQU8sRUFBRTtZQUM1QixNQUFNLElBQUlqSCxNQUFNMEgsZ0JBQWdCckcsTUFBTTtRQUN4QztRQUVBLHVDQUF1QztRQUN2QyxNQUFNc0csYUFBYSxNQUFNcEssY0FBY3ZCO1FBQ3ZDLElBQUkyTCxXQUFXaE4sTUFBTSxHQUFHaUIsUUFBUTtZQUM5QixNQUFNLElBQUlvRSxNQUFNO1FBQ2xCO1FBRUEsa0RBQWtEO1FBQ2xELE1BQU1ILG9CQUFvQjdELFFBQVEsQ0FBQ0o7UUFFbkMsOENBQThDO1FBQzlDLE1BQU1zQyxlQUFlbEMsUUFBUTtZQUMzQkwsTUFBTTtZQUNOQyxRQUFRLENBQUNBO1lBQ1RHLGFBQWEsQ0FBQyxnQ0FBZ0MsRUFBRUgsT0FBTyxvQkFBb0IsQ0FBQztRQUM5RTtRQUVBLE1BQU1nTSxpQkFBaUI7WUFDckI1TDtZQUNBSjtZQUNBbUU7WUFDQWpFLFFBQVE7WUFDUkQsTUFBTWxDLHlEQUFTQSxDQUFDMEUsR0FBRztZQUNuQnlHLFdBQVduTCx5REFBU0EsQ0FBQzBFLEdBQUc7UUFDMUI7UUFFQSxNQUFNMkcsU0FBUyxNQUFNdEwsMERBQU1BLENBQUNOLDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlHLFdBQVcsR0FBR3dMO1FBRXJFLE9BQU81QyxPQUFPcEcsRUFBRTtJQUNsQixFQUFFLE9BQU9oQyxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyxzQ0FBc0NBO1FBQ3BELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLHVCQUF1QjtBQUNoQixlQUFlaUwsbUJBQW1CN0wsTUFBYyxFQUFFdUMsYUFBYSxFQUFFO0lBQ3RFLElBQUk7UUFDRixNQUFNQyxJQUFJbkYseURBQUtBLENBQ2JELDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlHLFdBQVcsR0FDdEM5Qyx5REFBS0EsQ0FBQyxVQUFVLE1BQU0wQyxTQUN0QnpDLDJEQUFPQSxDQUFDLFFBQVEsU0FDaEJDLHlEQUFLQSxDQUFDK0U7UUFHUixNQUFNd0ksV0FBVyxNQUFNdE4sMkRBQU9BLENBQUMrRTtRQUMvQixPQUFPdUksU0FBU3JJLElBQUksQ0FBQ0MsR0FBRyxDQUFDM0YsQ0FBQUEsTUFBUTtnQkFDL0I0RixJQUFJNUYsSUFBSTRGLEVBQUU7Z0JBQ1YsR0FBRzVGLElBQUkrRCxJQUFJLEVBQUU7Z0JBQ2JsQixNQUFNN0MsSUFBSStELElBQUksR0FBR2xCLElBQUksRUFBRXNCO1lBQ3pCO0lBQ0YsRUFBRSxPQUFPUCxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ2pELE9BQU8sRUFBRTtJQUNYO0FBQ0Y7QUFFQSx3RUFBd0U7QUFDakUsZUFBZWtMO0lBQ3BCLElBQUk7UUFDRix3REFBd0Q7UUFDeEQsSUFBSTtZQUNGLE1BQU1DLGtCQUFrQjNPLDhEQUFVQSxDQUFDVSx5Q0FBRUEsRUFBRW1DLFlBQVlDLEtBQUs7WUFDeEQsTUFBTTZLLFdBQVcsTUFBTWxOLHNFQUFrQkEsQ0FBQ2tPO1lBQzFDLE1BQU1DLFFBQVFqQixTQUFTaEssSUFBSSxHQUFHaUwsS0FBSztZQUNuQyxNQUFNQyxtQkFBbUIsQ0FBQ0QsUUFBUSxHQUFHRSxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHO1lBQzVELE9BQU8sQ0FBQyxFQUFFLEVBQUVGLGtCQUFrQjtRQUNoQyxFQUFFLE9BQU9HLFlBQVk7WUFDbkJ6TCxRQUFRdUksSUFBSSxDQUFDLDJEQUEyRGtEO1lBQ3hFLHlDQUF5QztZQUN6QyxNQUFNQyxZQUFZaEwsS0FBS2dCLEdBQUcsR0FBRzZKLFFBQVEsR0FBR3ZDLEtBQUssQ0FBQyxDQUFDO1lBQy9DLE1BQU0yQyxhQUFhdkssS0FBS3dLLE1BQU0sR0FBR0wsUUFBUSxDQUFDLElBQUlNLFNBQVMsQ0FBQyxHQUFHLEdBQUdsSSxXQUFXO1lBQ3pFLE9BQU8sQ0FBQyxFQUFFLEVBQUUrSCxZQUFZQyxZQUFZO1FBQ3RDO0lBQ0YsRUFBRSxPQUFPMUwsT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsMENBQTBDQTtRQUN4RCxpQkFBaUI7UUFDakIsTUFBTXlMLFlBQVloTCxLQUFLZ0IsR0FBRyxHQUFHNkosUUFBUSxHQUFHdkMsS0FBSyxDQUFDLENBQUM7UUFDL0MsT0FBTyxDQUFDLEVBQUUsRUFBRTBDLFdBQVc7SUFDekI7QUFDRjtBQUVBLDJFQUEyRTtBQUNwRSxlQUFlSTtJQUNwQixPQUFPWDtBQUNUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFTVVNcXE9uZURyaXZlXFxEZXNrdG9wXFxNWSBQUk9KRUNUU1xcTm9kZSBJbnN0cmFcXHNyY1xcbGliXFxkYXRhU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBkb2MsXG4gIGdldERvYyxcbiAgdXBkYXRlRG9jLFxuICBkZWxldGVEb2MsXG4gIGNvbGxlY3Rpb24sXG4gIHF1ZXJ5LFxuICB3aGVyZSxcbiAgb3JkZXJCeSxcbiAgbGltaXQsXG4gIGdldERvY3MsXG4gIGFkZERvYyxcbiAgVGltZXN0YW1wLFxuICBpbmNyZW1lbnQsXG4gIGdldENvdW50RnJvbVNlcnZlclxufSBmcm9tICdmaXJlYmFzZS9maXJlc3RvcmUnXG5pbXBvcnQgeyBkYiB9IGZyb20gJy4vZmlyZWJhc2UnXG5cbi8vIEZpZWxkIG5hbWVzIGZvciBGaXJlc3RvcmUgY29sbGVjdGlvbnNcbmV4cG9ydCBjb25zdCBGSUVMRF9OQU1FUyA9IHtcbiAgLy8gVXNlciBmaWVsZHNcbiAgbmFtZTogJ25hbWUnLFxuICBlbWFpbDogJ2VtYWlsJyxcbiAgbW9iaWxlOiAnbW9iaWxlJyxcbiAgcmVmZXJyYWxDb2RlOiAncmVmZXJyYWxDb2RlJyxcbiAgcmVmZXJyZWRCeTogJ3JlZmVycmVkQnknLFxuICByZWZlcnJhbEJvbnVzQ3JlZGl0ZWQ6ICdyZWZlcnJhbEJvbnVzQ3JlZGl0ZWQnLCAvLyBUcmFjayBpZiByZWZlcnJhbCBib251cyBoYXMgYmVlbiBjcmVkaXRlZFxuICBwbGFuOiAncGxhbicsXG4gIHBsYW5FeHBpcnk6ICdwbGFuRXhwaXJ5JyxcbiAgYWN0aXZlRGF5czogJ2FjdGl2ZURheXMnLFxuICBsYXN0QWN0aXZlRGF5VXBkYXRlOiAnbGFzdEFjdGl2ZURheVVwZGF0ZScsIC8vIFRyYWNrIHdoZW4gYWN0aXZlIGRheXMgd2VyZSBsYXN0IHVwZGF0ZWRcbiAgam9pbmVkRGF0ZTogJ2pvaW5lZERhdGUnLFxuXG4gIC8vIFdhbGxldCBmaWVsZHNcbiAgd2FsbGV0OiAnd2FsbGV0JywgLy8gU2luZ2xlIHdhbGxldCBpbnN0ZWFkIG9mIG11bHRpcGxlXG5cbiAgLy8gQmFuayBkZXRhaWxzIGZpZWxkc1xuICBiYW5rQWNjb3VudEhvbGRlck5hbWU6ICdiYW5rQWNjb3VudEhvbGRlck5hbWUnLFxuICBiYW5rQWNjb3VudE51bWJlcjogJ2JhbmtBY2NvdW50TnVtYmVyJyxcbiAgYmFua0lmc2NDb2RlOiAnYmFua0lmc2NDb2RlJyxcbiAgYmFua05hbWU6ICdiYW5rTmFtZScsXG4gIGJhbmtEZXRhaWxzVXBkYXRlZDogJ2JhbmtEZXRhaWxzVXBkYXRlZCcsXG5cbiAgLy8gVHJhbnNsYXRpb24gZmllbGRzXG4gIHRvdGFsVHJhbnNsYXRpb25zOiAndG90YWxUcmFuc2xhdGlvbnMnLFxuICB0b2RheVRyYW5zbGF0aW9uczogJ3RvZGF5VHJhbnNsYXRpb25zJyxcbiAgbGFzdFRyYW5zbGF0aW9uRGF0ZTogJ2xhc3RUcmFuc2xhdGlvbkRhdGUnLFxuICB0cmFuc2xhdGlvbkR1cmF0aW9uOiAndHJhbnNsYXRpb25EdXJhdGlvbicsIC8vIER1cmF0aW9uIGluIHNlY29uZHMgZm9yIHRyYW5zbGF0aW9uIHRhc2tzXG5cbiAgLy8gUXVpY2sgVHJhbnNsYXRpb24gQWR2YW50YWdlIGZpZWxkc1xuICBxdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlOiAncXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZScsIC8vIEJvb2xlYW46IHdoZXRoZXIgdXNlciBoYXMgcXVpY2sgdHJhbnNsYXRpb24gYWR2YW50YWdlXG4gIHF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VFeHBpcnk6ICdxdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlRXhwaXJ5JywgLy8gRGF0ZTogd2hlbiB0aGUgYWR2YW50YWdlIGV4cGlyZXNcbiAgcXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZURheXM6ICdxdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlRGF5cycsIC8vIE51bWJlcjogdG90YWwgZGF5cyBncmFudGVkXG4gIHF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VTZWNvbmRzOiAncXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZVNlY29uZHMnLCAvLyBOdW1iZXI6IHRyYW5zbGF0aW9uIGR1cmF0aW9uIGluIHNlY29uZHMgZHVyaW5nIGFkdmFudGFnZVxuICBxdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlR3JhbnRlZEJ5OiAncXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZUdyYW50ZWRCeScsIC8vIFN0cmluZzogYWRtaW4gd2hvIGdyYW50ZWQgaXRcbiAgcXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZUdyYW50ZWRBdDogJ3F1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VHcmFudGVkQXQnLCAvLyBEYXRlOiB3aGVuIGl0IHdhcyBncmFudGVkXG5cbiAgLy8gVHJhbnNhY3Rpb24gZmllbGRzXG4gIHR5cGU6ICd0eXBlJyxcbiAgYW1vdW50OiAnYW1vdW50JyxcbiAgZGF0ZTogJ2RhdGUnLFxuICBzdGF0dXM6ICdzdGF0dXMnLFxuICBkZXNjcmlwdGlvbjogJ2Rlc2NyaXB0aW9uJyxcbiAgdXNlcklkOiAndXNlcklkJ1xufVxuXG4vLyBDb2xsZWN0aW9uIG5hbWVzXG5leHBvcnQgY29uc3QgQ09MTEVDVElPTlMgPSB7XG4gIHVzZXJzOiAndXNlcnMnLFxuICB0cmFuc2FjdGlvbnM6ICd0cmFuc2FjdGlvbnMnLFxuICB3aXRoZHJhd2FsczogJ3dpdGhkcmF3YWxzJyxcbiAgcGxhbnM6ICdwbGFucycsXG4gIHNldHRpbmdzOiAnc2V0dGluZ3MnLFxuICBub3RpZmljYXRpb25zOiAnbm90aWZpY2F0aW9ucycsXG4gIGFkbWluTGVhdmVzOiAnYWRtaW5MZWF2ZXMnLFxuICB1c2VyTGVhdmVzOiAndXNlckxlYXZlcydcbn1cblxuLy8gR2V0IHVzZXIgZGF0YVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJEYXRhKHVzZXJJZDogc3RyaW5nKSB7XG4gIHRyeSB7XG4gICAgaWYgKCF1c2VySWQgfHwgdHlwZW9mIHVzZXJJZCAhPT0gJ3N0cmluZycpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ludmFsaWQgdXNlcklkIHByb3ZpZGVkIHRvIGdldFVzZXJEYXRhOicsIHVzZXJJZClcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuXG4gICAgY29uc3QgdXNlckRvYyA9IGF3YWl0IGdldERvYyhkb2MoZGIsIENPTExFQ1RJT05TLnVzZXJzLCB1c2VySWQpKVxuICAgIGlmICh1c2VyRG9jLmV4aXN0cygpKSB7XG4gICAgICBjb25zdCBkYXRhID0gdXNlckRvYy5kYXRhKClcblxuICAgICAgLy8gRW5zdXJlIGFsbCB2YWx1ZXMgYXJlIHByb3Blcmx5IHR5cGVkXG4gICAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICAgIG5hbWU6IFN0cmluZyhkYXRhW0ZJRUxEX05BTUVTLm5hbWVdIHx8ICcnKSxcbiAgICAgICAgZW1haWw6IFN0cmluZyhkYXRhW0ZJRUxEX05BTUVTLmVtYWlsXSB8fCAnJyksXG4gICAgICAgIG1vYmlsZTogU3RyaW5nKGRhdGFbRklFTERfTkFNRVMubW9iaWxlXSB8fCAnJyksXG4gICAgICAgIHJlZmVycmFsQ29kZTogU3RyaW5nKGRhdGFbRklFTERfTkFNRVMucmVmZXJyYWxDb2RlXSB8fCAnJyksXG4gICAgICAgIHJlZmVycmVkQnk6IFN0cmluZyhkYXRhW0ZJRUxEX05BTUVTLnJlZmVycmVkQnldIHx8ICcnKSxcbiAgICAgICAgcmVmZXJyYWxCb251c0NyZWRpdGVkOiBCb29sZWFuKGRhdGFbRklFTERfTkFNRVMucmVmZXJyYWxCb251c0NyZWRpdGVkXSB8fCBmYWxzZSksXG4gICAgICAgIHBsYW46IFN0cmluZyhkYXRhW0ZJRUxEX05BTUVTLnBsYW5dIHx8ICdUcmlhbCcpLFxuICAgICAgICBwbGFuRXhwaXJ5OiBkYXRhW0ZJRUxEX05BTUVTLnBsYW5FeHBpcnldPy50b0RhdGUoKSB8fCBudWxsLFxuICAgICAgICBhY3RpdmVEYXlzOiBOdW1iZXIoZGF0YVtGSUVMRF9OQU1FUy5hY3RpdmVEYXlzXSB8fCAwKSxcbiAgICAgICAgbGFzdEFjdGl2ZURheVVwZGF0ZTogZGF0YVtGSUVMRF9OQU1FUy5sYXN0QWN0aXZlRGF5VXBkYXRlXT8udG9EYXRlKCkgfHwgbnVsbCxcbiAgICAgICAgam9pbmVkRGF0ZTogZGF0YVtGSUVMRF9OQU1FUy5qb2luZWREYXRlXT8udG9EYXRlKCkgfHwgbmV3IERhdGUoKSxcbiAgICAgICAgdHJhbnNsYXRpb25EdXJhdGlvbjogTnVtYmVyKGRhdGFbRklFTERfTkFNRVMudHJhbnNsYXRpb25EdXJhdGlvbl0gfHwgKGRhdGFbRklFTERfTkFNRVMucGxhbl0gPT09ICdUcmlhbCcgPyAzMCA6IDMwMCkpLCAvLyBEZWZhdWx0IDMwIHNlY29uZHMgZm9yIHRyaWFsLCA1IG1pbnV0ZXMgZm9yIG90aGVyc1xuXG4gICAgICAgIC8vIFF1aWNrIFRyYW5zbGF0aW9uIEFkdmFudGFnZSBmaWVsZHNcbiAgICAgICAgcXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZTogQm9vbGVhbihkYXRhW0ZJRUxEX05BTUVTLnF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VdIHx8IGZhbHNlKSxcbiAgICAgICAgcXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZUV4cGlyeTogZGF0YVtGSUVMRF9OQU1FUy5xdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlRXhwaXJ5XT8udG9EYXRlKCkgfHwgbnVsbCxcbiAgICAgICAgcXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZURheXM6IE51bWJlcihkYXRhW0ZJRUxEX05BTUVTLnF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VEYXlzXSB8fCAwKSxcbiAgICAgICAgcXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZVNlY29uZHM6IE51bWJlcihkYXRhW0ZJRUxEX05BTUVTLnF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VTZWNvbmRzXSB8fCAzMCksXG4gICAgICAgIHF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VHcmFudGVkQnk6IFN0cmluZyhkYXRhW0ZJRUxEX05BTUVTLnF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VHcmFudGVkQnldIHx8ICcnKSxcbiAgICAgICAgcXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZUdyYW50ZWRBdDogZGF0YVtGSUVMRF9OQU1FUy5xdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlR3JhbnRlZEF0XT8udG9EYXRlKCkgfHwgbnVsbFxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnZ2V0VXNlckRhdGEgcmVzdWx0OicsIHJlc3VsdClcbiAgICAgIHJldHVybiByZXN1bHRcbiAgICB9XG4gICAgcmV0dXJuIG51bGxcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVzZXIgZGF0YTonLCBlcnJvcilcbiAgICByZXR1cm4gbnVsbCAvLyBSZXR1cm4gbnVsbCBpbnN0ZWFkIG9mIHRocm93aW5nIHRvIHByZXZlbnQgY3Jhc2hlc1xuICB9XG59XG5cbi8vIEdldCB3YWxsZXQgZGF0YVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFdhbGxldERhdGEodXNlcklkOiBzdHJpbmcpIHtcbiAgdHJ5IHtcbiAgICBpZiAoIXVzZXJJZCB8fCB0eXBlb2YgdXNlcklkICE9PSAnc3RyaW5nJykge1xuICAgICAgY29uc29sZS5lcnJvcignSW52YWxpZCB1c2VySWQgcHJvdmlkZWQgdG8gZ2V0V2FsbGV0RGF0YTonLCB1c2VySWQpXG4gICAgICByZXR1cm4geyB3YWxsZXQ6IDAgfVxuICAgIH1cblxuICAgIGNvbnN0IHVzZXJEb2MgPSBhd2FpdCBnZXREb2MoZG9jKGRiLCBDT0xMRUNUSU9OUy51c2VycywgdXNlcklkKSlcbiAgICBpZiAodXNlckRvYy5leGlzdHMoKSkge1xuICAgICAgY29uc3QgZGF0YSA9IHVzZXJEb2MuZGF0YSgpXG4gICAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICAgIHdhbGxldDogTnVtYmVyKGRhdGFbRklFTERfTkFNRVMud2FsbGV0XSB8fCAwKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnZ2V0V2FsbGV0RGF0YSByZXN1bHQ6JywgcmVzdWx0KVxuICAgICAgcmV0dXJuIHJlc3VsdFxuICAgIH1cbiAgICByZXR1cm4geyB3YWxsZXQ6IDAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgd2FsbGV0IGRhdGE6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgd2FsbGV0OiAwIH0gLy8gUmV0dXJuIGRlZmF1bHQgaW5zdGVhZCBvZiB0aHJvd2luZ1xuICB9XG59XG5cbi8vIEdldCB0cmFuc2xhdGlvbiBjb3VudCBkYXRhXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VmlkZW9Db3VudERhdGEodXNlcklkOiBzdHJpbmcpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB1c2VyUmVmID0gZG9jKGRiLCBDT0xMRUNUSU9OUy51c2VycywgdXNlcklkKVxuICAgIGNvbnN0IHVzZXJEb2MgPSBhd2FpdCBnZXREb2ModXNlclJlZilcbiAgICBpZiAodXNlckRvYy5leGlzdHMoKSkge1xuICAgICAgY29uc3QgZGF0YSA9IHVzZXJEb2MuZGF0YSgpXG4gICAgICBjb25zdCB0b3RhbFRyYW5zbGF0aW9ucyA9IGRhdGFbRklFTERfTkFNRVMudG90YWxUcmFuc2xhdGlvbnNdIHx8IDBcbiAgICAgIGxldCB0b2RheVRyYW5zbGF0aW9ucyA9IGRhdGFbRklFTERfTkFNRVMudG9kYXlUcmFuc2xhdGlvbnNdIHx8IDBcbiAgICAgIGNvbnN0IGxhc3RUcmFuc2xhdGlvbkRhdGUgPSBkYXRhW0ZJRUxEX05BTUVTLmxhc3RUcmFuc2xhdGlvbkRhdGVdPy50b0RhdGUoKVxuXG4gICAgICAvLyBDaGVjayBpZiBpdCdzIGEgbmV3IGRheVxuICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpXG4gICAgICBjb25zdCBpc05ld0RheSA9ICFsYXN0VHJhbnNsYXRpb25EYXRlIHx8XG4gICAgICAgIGxhc3RUcmFuc2xhdGlvbkRhdGUudG9EYXRlU3RyaW5nKCkgIT09IHRvZGF5LnRvRGF0ZVN0cmluZygpXG5cbiAgICAgIC8vIElmIGl0J3MgYSBuZXcgZGF5LCByZXNldCB0b2RheVRyYW5zbGF0aW9ucyBhbmQgdXBkYXRlIGFjdGl2ZSBkYXlzXG4gICAgICBpZiAoaXNOZXdEYXkgJiYgdG9kYXlUcmFuc2xhdGlvbnMgPiAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFJlc2V0dGluZyBkYWlseSB0cmFuc2xhdGlvbiBjb3VudCBmb3IgdXNlciAke3VzZXJJZH0gKHdhcyAke3RvZGF5VHJhbnNsYXRpb25zfSlgKVxuICAgICAgICBhd2FpdCB1cGRhdGVEb2ModXNlclJlZiwge1xuICAgICAgICAgIFtGSUVMRF9OQU1FUy50b2RheVRyYW5zbGF0aW9uc106IDBcbiAgICAgICAgfSlcbiAgICAgICAgdG9kYXlUcmFuc2xhdGlvbnMgPSAwXG5cbiAgICAgICAgLy8gQWxzbyB1cGRhdGUgYWN0aXZlIGRheXMgZm9yIGFjY3VyYXRlIHRyYWNraW5nXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgYXdhaXQgdXBkYXRlVXNlckFjdGl2ZURheXModXNlcklkKVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGFjdGl2ZSBkYXlzIGR1cmluZyBkYWlseSByZXNldDonLCBlcnJvcilcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICB0b3RhbFRyYW5zbGF0aW9uczogdG90YWxUcmFuc2xhdGlvbnMsXG4gICAgICAgIHRvZGF5VHJhbnNsYXRpb25zOiB0b2RheVRyYW5zbGF0aW9ucyxcbiAgICAgICAgcmVtYWluaW5nVHJhbnNsYXRpb25zOiBNYXRoLm1heCgwLCA1MCAtIHRvZGF5VHJhbnNsYXRpb25zKVxuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4geyB0b3RhbFRyYW5zbGF0aW9uczogMCwgdG9kYXlUcmFuc2xhdGlvbnM6IDAsIHJlbWFpbmluZ1RyYW5zbGF0aW9uczogNTAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgdmlkZW8gY291bnQgZGF0YTonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIFVwZGF0ZSB1c2VyIGRhdGFcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1cGRhdGVVc2VyRGF0YSh1c2VySWQ6IHN0cmluZywgZGF0YTogYW55KSB7XG4gIHRyeSB7XG4gICAgYXdhaXQgdXBkYXRlRG9jKGRvYyhkYiwgQ09MTEVDVElPTlMudXNlcnMsIHVzZXJJZCksIGRhdGEpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgdXNlciBkYXRhOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cblxuLy8gQWRkIHRyYW5zYWN0aW9uXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gYWRkVHJhbnNhY3Rpb24odXNlcklkOiBzdHJpbmcsIHRyYW5zYWN0aW9uRGF0YToge1xuICB0eXBlOiBzdHJpbmdcbiAgYW1vdW50OiBudW1iZXJcbiAgZGVzY3JpcHRpb246IHN0cmluZ1xuICBzdGF0dXM/OiBzdHJpbmdcbn0pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB0cmFuc2FjdGlvbiA9IHtcbiAgICAgIFtGSUVMRF9OQU1FUy51c2VySWRdOiB1c2VySWQsXG4gICAgICBbRklFTERfTkFNRVMudHlwZV06IHRyYW5zYWN0aW9uRGF0YS50eXBlLFxuICAgICAgW0ZJRUxEX05BTUVTLmFtb3VudF06IHRyYW5zYWN0aW9uRGF0YS5hbW91bnQsXG4gICAgICBbRklFTERfTkFNRVMuZGVzY3JpcHRpb25dOiB0cmFuc2FjdGlvbkRhdGEuZGVzY3JpcHRpb24sXG4gICAgICBbRklFTERfTkFNRVMuc3RhdHVzXTogdHJhbnNhY3Rpb25EYXRhLnN0YXR1cyB8fCAnY29tcGxldGVkJyxcbiAgICAgIFtGSUVMRF9OQU1FUy5kYXRlXTogVGltZXN0YW1wLm5vdygpXG4gICAgfVxuICAgIFxuICAgIGF3YWl0IGFkZERvYyhjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy50cmFuc2FjdGlvbnMpLCB0cmFuc2FjdGlvbilcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhZGRpbmcgdHJhbnNhY3Rpb246JywgZXJyb3IpXG4gICAgdGhyb3cgZXJyb3JcbiAgfVxufVxuXG4vLyBHZXQgdHJhbnNhY3Rpb25zXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VHJhbnNhY3Rpb25zKHVzZXJJZDogc3RyaW5nLCBsaW1pdENvdW50ID0gMTApIHtcbiAgdHJ5IHtcbiAgICBpZiAoIXVzZXJJZCB8fCB0eXBlb2YgdXNlcklkICE9PSAnc3RyaW5nJykge1xuICAgICAgY29uc29sZS5lcnJvcignSW52YWxpZCB1c2VySWQgcHJvdmlkZWQgdG8gZ2V0VHJhbnNhY3Rpb25zOicsIHVzZXJJZClcbiAgICAgIHJldHVybiBbXVxuICAgIH1cblxuICAgIC8vIFRlbXBvcmFyeSBmaXg6IFVzZSBvbmx5IHdoZXJlIGNsYXVzZSB3aXRob3V0IG9yZGVyQnkgdG8gYXZvaWQgaW5kZXggcmVxdWlyZW1lbnRcbiAgICAvLyBUT0RPOiBDcmVhdGUgY29tcG9zaXRlIGluZGV4IGluIEZpcmViYXNlIGNvbnNvbGUgZm9yIGJldHRlciBwZXJmb3JtYW5jZVxuICAgIGNvbnN0IHEgPSBxdWVyeShcbiAgICAgIGNvbGxlY3Rpb24oZGIsIENPTExFQ1RJT05TLnRyYW5zYWN0aW9ucyksXG4gICAgICB3aGVyZShGSUVMRF9OQU1FUy51c2VySWQsICc9PScsIHVzZXJJZCksXG4gICAgICBsaW1pdChsaW1pdENvdW50KVxuICAgIClcblxuICAgIGNvbnN0IHF1ZXJ5U25hcHNob3QgPSBhd2FpdCBnZXREb2NzKHEpXG4gICAgY29uc3QgdHJhbnNhY3Rpb25zID0gcXVlcnlTbmFwc2hvdC5kb2NzLm1hcChkb2MgPT4gKHtcbiAgICAgIGlkOiBkb2MuaWQsXG4gICAgICAuLi5kb2MuZGF0YSgpLFxuICAgICAgZGF0ZTogZG9jLmRhdGEoKVtGSUVMRF9OQU1FUy5kYXRlXT8udG9EYXRlKClcbiAgICB9KSlcblxuICAgIC8vIFNvcnQgaW4gbWVtb3J5IHNpbmNlIHdlIGNhbid0IHVzZSBvcmRlckJ5IHdpdGhvdXQgaW5kZXhcbiAgICB0cmFuc2FjdGlvbnMuc29ydCgoYSwgYikgPT4ge1xuICAgICAgY29uc3QgZGF0ZUEgPSBhLmRhdGUgfHwgbmV3IERhdGUoMClcbiAgICAgIGNvbnN0IGRhdGVCID0gYi5kYXRlIHx8IG5ldyBEYXRlKDApXG4gICAgICByZXR1cm4gZGF0ZUIuZ2V0VGltZSgpIC0gZGF0ZUEuZ2V0VGltZSgpIC8vIERlc2NlbmRpbmcgb3JkZXJcbiAgICB9KVxuXG4gICAgcmV0dXJuIHRyYW5zYWN0aW9uc1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgdHJhbnNhY3Rpb25zOicsIGVycm9yKVxuICAgIHJldHVybiBbXSAvLyBSZXR1cm4gZW1wdHkgYXJyYXkgaW5zdGVhZCBvZiB0aHJvd2luZyB0byBwcmV2ZW50IGNyYXNoZXNcbiAgfVxufVxuXG4vLyBHZXQgcmVmZXJyYWxzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0UmVmZXJyYWxzKHJlZmVycmFsQ29kZTogc3RyaW5nKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcSA9IHF1ZXJ5KFxuICAgICAgY29sbGVjdGlvbihkYiwgQ09MTEVDVElPTlMudXNlcnMpLFxuICAgICAgd2hlcmUoRklFTERfTkFNRVMucmVmZXJyZWRCeSwgJz09JywgcmVmZXJyYWxDb2RlKVxuICAgIClcbiAgICBcbiAgICBjb25zdCBxdWVyeVNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhxKVxuICAgIHJldHVybiBxdWVyeVNuYXBzaG90LmRvY3MubWFwKGRvYyA9PiAoe1xuICAgICAgaWQ6IGRvYy5pZCxcbiAgICAgIC4uLmRvYy5kYXRhKCksXG4gICAgICBqb2luZWREYXRlOiBkb2MuZGF0YSgpW0ZJRUxEX05BTUVTLmpvaW5lZERhdGVdPy50b0RhdGUoKVxuICAgIH0pKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgcmVmZXJyYWxzOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cblxuLy8gVXBkYXRlIHRyYW5zbGF0aW9uIGNvdW50XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlVmlkZW9Db3VudCh1c2VySWQ6IHN0cmluZykge1xuICB0cnkge1xuICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKVxuICAgIGNvbnN0IHVzZXJSZWYgPSBkb2MoZGIsIENPTExFQ1RJT05TLnVzZXJzLCB1c2VySWQpXG5cbiAgICAvLyBHZXQgY3VycmVudCBkYXRhIHRvIGNoZWNrIGlmIHdlIG5lZWQgdG8gcmVzZXQgZGFpbHkgY291bnRcbiAgICBjb25zdCB1c2VyRG9jID0gYXdhaXQgZ2V0RG9jKHVzZXJSZWYpXG4gICAgaWYgKHVzZXJEb2MuZXhpc3RzKCkpIHtcbiAgICAgIGNvbnN0IGRhdGEgPSB1c2VyRG9jLmRhdGEoKVxuICAgICAgY29uc3QgbGFzdFRyYW5zbGF0aW9uRGF0ZSA9IGRhdGFbRklFTERfTkFNRVMubGFzdFRyYW5zbGF0aW9uRGF0ZV0/LnRvRGF0ZSgpXG4gICAgICBjb25zdCBjdXJyZW50VG9kYXlUcmFuc2xhdGlvbnMgPSBkYXRhW0ZJRUxEX05BTUVTLnRvZGF5VHJhbnNsYXRpb25zXSB8fCAwXG5cbiAgICAgIC8vIENoZWNrIGlmIGl0J3MgYSBuZXcgZGF5XG4gICAgICBjb25zdCBpc05ld0RheSA9ICFsYXN0VHJhbnNsYXRpb25EYXRlIHx8XG4gICAgICAgIGxhc3RUcmFuc2xhdGlvbkRhdGUudG9EYXRlU3RyaW5nKCkgIT09IHRvZGF5LnRvRGF0ZVN0cmluZygpXG5cbiAgICAgIGlmIChpc05ld0RheSAmJiBjdXJyZW50VG9kYXlUcmFuc2xhdGlvbnMgPiAwKSB7XG4gICAgICAgIC8vIFJlc2V0IHRvZGF5J3MgY291bnQgYW5kIHRoZW4gaW5jcmVtZW50XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFJlc2V0dGluZyBhbmQgdXBkYXRpbmcgZGFpbHkgdHJhbnNsYXRpb24gY291bnQgZm9yIHVzZXIgJHt1c2VySWR9YClcbiAgICAgICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgICAgICBbRklFTERfTkFNRVMudG90YWxUcmFuc2xhdGlvbnNdOiBpbmNyZW1lbnQoMSksXG4gICAgICAgICAgW0ZJRUxEX05BTUVTLnRvZGF5VHJhbnNsYXRpb25zXTogMSwgLy8gUmVzZXQgdG8gMSAodGhpcyB0cmFuc2xhdGlvbilcbiAgICAgICAgICBbRklFTERfTkFNRVMubGFzdFRyYW5zbGF0aW9uRGF0ZV06IFRpbWVzdGFtcC5mcm9tRGF0ZSh0b2RheSlcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIE5vcm1hbCBpbmNyZW1lbnRcbiAgICAgICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgICAgICBbRklFTERfTkFNRVMudG90YWxUcmFuc2xhdGlvbnNdOiBpbmNyZW1lbnQoMSksXG4gICAgICAgICAgW0ZJRUxEX05BTUVTLnRvZGF5VHJhbnNsYXRpb25zXTogaW5jcmVtZW50KDEpLFxuICAgICAgICAgIFtGSUVMRF9OQU1FUy5sYXN0VHJhbnNsYXRpb25EYXRlXTogVGltZXN0YW1wLmZyb21EYXRlKHRvZGF5KVxuICAgICAgICB9KVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBVc2VyIGRvZXNuJ3QgZXhpc3QsIGNyZWF0ZSB3aXRoIGluaXRpYWwgdmFsdWVzXG4gICAgICBhd2FpdCB1cGRhdGVEb2ModXNlclJlZiwge1xuICAgICAgICBbRklFTERfTkFNRVMudG90YWxUcmFuc2xhdGlvbnNdOiBpbmNyZW1lbnQoMSksXG4gICAgICAgIFtGSUVMRF9OQU1FUy50b2RheVRyYW5zbGF0aW9uc106IGluY3JlbWVudCgxKSxcbiAgICAgICAgW0ZJRUxEX05BTUVTLmxhc3RUcmFuc2xhdGlvbkRhdGVdOiBUaW1lc3RhbXAuZnJvbURhdGUodG9kYXkpXG4gICAgICB9KVxuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB0cmFuc2xhdGlvbiBjb3VudDonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIFJlc2V0IGRhaWx5IHRyYW5zbGF0aW9uIGNvdW50IGZvciBhIHVzZXIgKGFkbWluIGZ1bmN0aW9uKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlc2V0RGFpbHlWaWRlb0NvdW50KHVzZXJJZDogc3RyaW5nKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgdXNlclJlZiA9IGRvYyhkYiwgQ09MTEVDVElPTlMudXNlcnMsIHVzZXJJZClcbiAgICBhd2FpdCB1cGRhdGVEb2ModXNlclJlZiwge1xuICAgICAgW0ZJRUxEX05BTUVTLnRvZGF5VHJhbnNsYXRpb25zXTogMFxuICAgIH0pXG4gICAgY29uc29sZS5sb2coYOKchSBSZXNldCBkYWlseSB0cmFuc2xhdGlvbiBjb3VudCBmb3IgdXNlciAke3VzZXJJZH1gKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlc2V0dGluZyBkYWlseSB0cmFuc2xhdGlvbiBjb3VudDonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIFVwZGF0ZSB1c2VyJ3MgYWN0aXZlIGRheXMgdXNpbmcgY2VudHJhbGl6ZWQgc2VydmljZVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZVVzZXJBY3RpdmVEYXlzKHVzZXJJZDogc3RyaW5nKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyB1cGRhdGVVc2VyQWN0aXZlRGF5czogY2VudHJhbGl6ZWRVcGRhdGUgfSA9IGF3YWl0IGltcG9ydCgnLi9hY3RpdmVEYXlzU2VydmljZScpXG4gICAgcmV0dXJuIGF3YWl0IGNlbnRyYWxpemVkVXBkYXRlKHVzZXJJZClcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB1c2VyIGFjdGl2ZSBkYXlzOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cblxuLy8gRml4IGFsbCB1c2VycycgYWN0aXZlIGRheXMgKGFkbWluIGZ1bmN0aW9uKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGZpeEFsbFVzZXJzQWN0aXZlRGF5cygpIHtcbiAgdHJ5IHtcbiAgICBjb25zb2xlLmxvZygn8J+UpyBTdGFydGluZyB0byBmaXggYWxsIHVzZXJzIGFjdGl2ZSBkYXlzLi4uJylcbiAgICBjb25zdCB1c2Vyc1NuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy51c2VycykpXG4gICAgbGV0IGZpeGVkQ291bnQgPSAwXG4gICAgbGV0IGVycm9yQ291bnQgPSAwXG5cbiAgICBmb3IgKGNvbnN0IHVzZXJEb2Mgb2YgdXNlcnNTbmFwc2hvdC5kb2NzKSB7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCB1cGRhdGVVc2VyQWN0aXZlRGF5cyh1c2VyRG9jLmlkKVxuICAgICAgICBmaXhlZENvdW50KytcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGZpeGluZyBhY3RpdmUgZGF5cyBmb3IgdXNlciAke3VzZXJEb2MuaWR9OmAsIGVycm9yKVxuICAgICAgICBlcnJvckNvdW50KytcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhg4pyFIEZpeGVkIGFjdGl2ZSBkYXlzIGZvciAke2ZpeGVkQ291bnR9IHVzZXJzLCAke2Vycm9yQ291bnR9IGVycm9yc2ApXG4gICAgcmV0dXJuIHsgZml4ZWRDb3VudCwgZXJyb3JDb3VudCB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZml4aW5nIGFsbCB1c2VycyBhY3RpdmUgZGF5czonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIFVwZGF0ZSB3YWxsZXQgYmFsYW5jZVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZVdhbGxldEJhbGFuY2UodXNlcklkOiBzdHJpbmcsIGFtb3VudDogbnVtYmVyKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgdXNlclJlZiA9IGRvYyhkYiwgQ09MTEVDVElPTlMudXNlcnMsIHVzZXJJZClcbiAgICBhd2FpdCB1cGRhdGVEb2ModXNlclJlZiwge1xuICAgICAgW0ZJRUxEX05BTUVTLndhbGxldF06IGluY3JlbWVudChhbW91bnQpXG4gICAgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB3YWxsZXQgYmFsYW5jZTonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIEJhbmsgZGV0YWlscyBpbnRlcmZhY2VcbmV4cG9ydCBpbnRlcmZhY2UgQmFua0RldGFpbHMge1xuICBhY2NvdW50SG9sZGVyTmFtZTogc3RyaW5nXG4gIGFjY291bnROdW1iZXI6IHN0cmluZ1xuICBpZnNjQ29kZTogc3RyaW5nXG4gIGJhbmtOYW1lOiBzdHJpbmdcbn1cblxuLy8gU2F2ZSBiYW5rIGRldGFpbHNcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzYXZlQmFua0RldGFpbHModXNlcklkOiBzdHJpbmcsIGJhbmtEZXRhaWxzOiBCYW5rRGV0YWlscykge1xuICB0cnkge1xuICAgIGlmICghdXNlcklkIHx8IHR5cGVvZiB1c2VySWQgIT09ICdzdHJpbmcnKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgdXNlcklkIHByb3ZpZGVkJylcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBiYW5rIGRldGFpbHNcbiAgICB2YWxpZGF0ZUJhbmtEZXRhaWxzKGJhbmtEZXRhaWxzKVxuXG4gICAgY29uc3QgdXNlclJlZiA9IGRvYyhkYiwgQ09MTEVDVElPTlMudXNlcnMsIHVzZXJJZClcbiAgICBhd2FpdCB1cGRhdGVEb2ModXNlclJlZiwge1xuICAgICAgW0ZJRUxEX05BTUVTLmJhbmtBY2NvdW50SG9sZGVyTmFtZV06IGJhbmtEZXRhaWxzLmFjY291bnRIb2xkZXJOYW1lLnRyaW0oKSxcbiAgICAgIFtGSUVMRF9OQU1FUy5iYW5rQWNjb3VudE51bWJlcl06IGJhbmtEZXRhaWxzLmFjY291bnROdW1iZXIudHJpbSgpLFxuICAgICAgW0ZJRUxEX05BTUVTLmJhbmtJZnNjQ29kZV06IGJhbmtEZXRhaWxzLmlmc2NDb2RlLnRyaW0oKS50b1VwcGVyQ2FzZSgpLFxuICAgICAgW0ZJRUxEX05BTUVTLmJhbmtOYW1lXTogYmFua0RldGFpbHMuYmFua05hbWUudHJpbSgpLFxuICAgICAgW0ZJRUxEX05BTUVTLmJhbmtEZXRhaWxzVXBkYXRlZF06IFRpbWVzdGFtcC5ub3coKVxuICAgIH0pXG5cbiAgICBjb25zb2xlLmxvZygnQmFuayBkZXRhaWxzIHNhdmVkIHN1Y2Nlc3NmdWxseSBmb3IgdXNlcjonLCB1c2VySWQpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGJhbmsgZGV0YWlsczonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIEdldCBiYW5rIGRldGFpbHNcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRCYW5rRGV0YWlscyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8QmFua0RldGFpbHMgfCBudWxsPiB7XG4gIHRyeSB7XG4gICAgaWYgKCF1c2VySWQgfHwgdHlwZW9mIHVzZXJJZCAhPT0gJ3N0cmluZycpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ludmFsaWQgdXNlcklkIHByb3ZpZGVkIHRvIGdldEJhbmtEZXRhaWxzOicsIHVzZXJJZClcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuXG4gICAgY29uc3QgdXNlckRvYyA9IGF3YWl0IGdldERvYyhkb2MoZGIsIENPTExFQ1RJT05TLnVzZXJzLCB1c2VySWQpKVxuICAgIGlmICh1c2VyRG9jLmV4aXN0cygpKSB7XG4gICAgICBjb25zdCBkYXRhID0gdXNlckRvYy5kYXRhKClcblxuICAgICAgLy8gQ2hlY2sgaWYgYmFuayBkZXRhaWxzIGV4aXN0XG4gICAgICBpZiAoZGF0YVtGSUVMRF9OQU1FUy5iYW5rQWNjb3VudE51bWJlcl0pIHtcbiAgICAgICAgY29uc3QgcmVzdWx0OiBCYW5rRGV0YWlscyA9IHtcbiAgICAgICAgICBhY2NvdW50SG9sZGVyTmFtZTogU3RyaW5nKGRhdGFbRklFTERfTkFNRVMuYmFua0FjY291bnRIb2xkZXJOYW1lXSB8fCAnJyksXG4gICAgICAgICAgYWNjb3VudE51bWJlcjogU3RyaW5nKGRhdGFbRklFTERfTkFNRVMuYmFua0FjY291bnROdW1iZXJdIHx8ICcnKSxcbiAgICAgICAgICBpZnNjQ29kZTogU3RyaW5nKGRhdGFbRklFTERfTkFNRVMuYmFua0lmc2NDb2RlXSB8fCAnJyksXG4gICAgICAgICAgYmFua05hbWU6IFN0cmluZyhkYXRhW0ZJRUxEX05BTUVTLmJhbmtOYW1lXSB8fCAnJylcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnNvbGUubG9nKCdnZXRCYW5rRGV0YWlscyByZXN1bHQgZm91bmQnKVxuICAgICAgICByZXR1cm4gcmVzdWx0XG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ05vIGJhbmsgZGV0YWlscyBmb3VuZCBmb3IgdXNlcicpXG4gICAgcmV0dXJuIG51bGxcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIGJhbmsgZGV0YWlsczonLCBlcnJvcilcbiAgICByZXR1cm4gbnVsbFxuICB9XG59XG5cbi8vIEdldCBwbGFuLWJhc2VkIGVhcm5pbmcgYW1vdW50IChwZXIgYmF0Y2ggb2YgNTAgdHJhbnNsYXRpb25zKVxuZXhwb3J0IGZ1bmN0aW9uIGdldFBsYW5FYXJuaW5nKHBsYW46IHN0cmluZyk6IG51bWJlciB7XG4gIGNvbnN0IHBsYW5FYXJuaW5nczogeyBba2V5OiBzdHJpbmddOiBudW1iZXIgfSA9IHtcbiAgICAnVHJpYWwnOiAyNSwgICAgIC8vIEZpeGVkOiBUcmlhbCBzaG91bGQgYmUg4oK5MjUgcGVyIDUwIHRyYW5zbGF0aW9uc1xuICAgICdKdW5pb3InOiAxNTAsICAgLy8gVXBkYXRlZCB0byBtYXRjaCBuZXcgcGxhbiBzdHJ1Y3R1cmVcbiAgICAnU2VuaW9yJzogMjUwLCAgIC8vIFVwZGF0ZWQgdG8gbWF0Y2ggbmV3IHBsYW4gc3RydWN0dXJlXG4gICAgJ0V4cGVydCc6IDQwMCwgICAvLyBVcGRhdGVkIHRvIG1hdGNoIG5ldyBwbGFuIHN0cnVjdHVyZVxuICAgICdTdGFydGVyJzogMjUsICAgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICdCYXNpYyc6IDc1LCAgICAgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICdQcmVtaXVtJzogMTUwLCAgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICdHb2xkJzogMjAwLCAgICAgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICdQbGF0aW51bSc6IDI1MCwgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICdEaWFtb25kJzogNDAwICAgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICB9XG5cbiAgcmV0dXJuIHBsYW5FYXJuaW5nc1twbGFuXSB8fCAyNSAvLyBEZWZhdWx0IHRvIHRyaWFsIGVhcm5pbmcgKHBlciBiYXRjaCBvZiA1MCB0cmFuc2xhdGlvbnMpXG59XG5cbi8vIENoZWNrIGNvcHktcGFzdGUgcGVybWlzc2lvbiB1c2luZyBjZW50cmFsaXplZCBzZXJ2aWNlXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY2hlY2tDb3B5UGFzdGVQZXJtaXNzaW9uKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTx7XG4gIGhhc1Blcm1pc3Npb246IGJvb2xlYW5cbiAgZGF5c1JlbWFpbmluZzogbnVtYmVyXG59PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBjaGVja0NvcHlQYXN0ZVBlcm1pc3Npb246IGNlbnRyYWxpemVkQ2hlY2sgfSA9IGF3YWl0IGltcG9ydCgnLi9jb3B5UGFzdGVTZXJ2aWNlJylcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjZW50cmFsaXplZENoZWNrKHVzZXJJZClcbiAgICByZXR1cm4ge1xuICAgICAgaGFzUGVybWlzc2lvbjogcmVzdWx0Lmhhc1Blcm1pc3Npb24sXG4gICAgICBkYXlzUmVtYWluaW5nOiByZXN1bHQuZGF5c1JlbWFpbmluZ1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBjb3B5LXBhc3RlIHBlcm1pc3Npb246JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgaGFzUGVybWlzc2lvbjogZmFsc2UsIGRheXNSZW1haW5pbmc6IDAgfVxuICB9XG59XG5cbi8vIEdldCBwbGFuLWJhc2VkIHRyYW5zbGF0aW9uIGR1cmF0aW9uIChpbiBzZWNvbmRzKVxuZXhwb3J0IGZ1bmN0aW9uIGdldFBsYW5WaWRlb0R1cmF0aW9uKHBsYW46IHN0cmluZyk6IG51bWJlciB7XG4gIGNvbnN0IHBsYW5EdXJhdGlvbnM6IHsgW2tleTogc3RyaW5nXTogbnVtYmVyIH0gPSB7XG4gICAgJ1RyaWFsJzogMzAsICAgICAgLy8gMzAgc2Vjb25kc1xuICAgICdTdGFydGVyJzogMzAwLCAgIC8vIDUgbWludXRlcyAoUnMgNDk5IHBsYW4pXG4gICAgJ0Jhc2ljJzogMzAwLCAgICAgLy8gNSBtaW51dGVzIChScyAxNDk5IHBsYW4pXG4gICAgJ1ByZW1pdW0nOiAzMDAsICAgLy8gNSBtaW51dGVzIChScyAyOTk5IHBsYW4pXG4gICAgJ0dvbGQnOiAxODAsICAgICAgLy8gMyBtaW51dGVzIChScyAzOTk5IHBsYW4pXG4gICAgJ1BsYXRpbnVtJzogMTIwLCAgLy8gMiBtaW51dGVzIChScyA1OTk5IHBsYW4pXG4gICAgJ0RpYW1vbmQnOiA2MCAgICAgLy8gMSBtaW51dGUgKFJzIDk5OTkgcGxhbilcbiAgfVxuXG4gIHJldHVybiBwbGFuRHVyYXRpb25zW3BsYW5dIHx8IDMwIC8vIERlZmF1bHQgdG8gdHJpYWwgZHVyYXRpb24gKDMwIHNlY29uZHMpXG59XG5cbi8vIEdldCBwbGFuIHZhbGlkaXR5IGR1cmF0aW9uIGluIGRheXNcbmV4cG9ydCBmdW5jdGlvbiBnZXRQbGFuVmFsaWRpdHlEYXlzKHBsYW46IHN0cmluZyk6IG51bWJlciB7XG4gIGNvbnN0IHBsYW5WYWxpZGl0eURheXM6IHsgW2tleTogc3RyaW5nXTogbnVtYmVyIH0gPSB7XG4gICAgJ1RyaWFsJzogMiwgICAgICAgLy8gMiBkYXlzXG4gICAgJ0p1bmlvcic6IDMwLCAgICAgLy8gMzAgZGF5cyAoUnMgMjk5OSBwbGFuKVxuICAgICdTZW5pb3InOiAzMCwgICAgIC8vIDMwIGRheXMgKFJzIDU5OTkgcGxhbilcbiAgICAnRXhwZXJ0JzogMzAsICAgICAvLyAzMCBkYXlzIChScyA5OTk5IHBsYW4pXG4gICAgJ1N0YXJ0ZXInOiAzMCwgICAgLy8gMzAgZGF5cyAoUnMgNDk5IHBsYW4pIC0gTGVnYWN5XG4gICAgJ0Jhc2ljJzogMzAsICAgICAgLy8gMzAgZGF5cyAoUnMgMTQ5OSBwbGFuKSAtIExlZ2FjeVxuICAgICdQcmVtaXVtJzogMzAsICAgIC8vIDMwIGRheXMgKFJzIDI5OTkgcGxhbikgLSBMZWdhY3lcbiAgICAnR29sZCc6IDMwLCAgICAgICAvLyAzMCBkYXlzIChScyAzOTk5IHBsYW4pIC0gTGVnYWN5XG4gICAgJ1BsYXRpbnVtJzogMzAsICAgLy8gMzAgZGF5cyAoUnMgNTk5OSBwbGFuKSAtIExlZ2FjeVxuICAgICdEaWFtb25kJzogMzAsICAgIC8vIDMwIGRheXMgKFJzIDk5OTkgcGxhbikgLSBMZWdhY3lcbiAgICAnNDk5JzogMzAsICAgICAgICAvLyBMZWdhY3kgcGxhbiBtYXBwaW5nXG4gICAgJzE0OTknOiAzMCwgICAgICAgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICcyOTk5JzogMzAsICAgICAgIC8vIExlZ2FjeSBwbGFuIG1hcHBpbmdcbiAgICAnMzk5OSc6IDMwLCAgICAgICAvLyBMZWdhY3kgcGxhbiBtYXBwaW5nXG4gICAgJzU5OTknOiAzMCwgICAgICAgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICc5OTk5JzogMzAgICAgICAgIC8vIExlZ2FjeSBwbGFuIG1hcHBpbmdcbiAgfVxuXG4gIHJldHVybiBwbGFuVmFsaWRpdHlEYXlzW3BsYW5dIHx8IDIgLy8gRGVmYXVsdCB0byB0cmlhbCBkdXJhdGlvbiAoMiBkYXlzKVxufVxuXG4vLyBDaGVjayBpZiB1c2VyJ3MgcGxhbiBpcyBleHBpcmVkIGJhc2VkIG9uIGFjdGl2ZSBkYXlzIGFuZCBwbGFuIHZhbGlkaXR5XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gaXNVc2VyUGxhbkV4cGlyZWQodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPHsgZXhwaXJlZDogYm9vbGVhbjsgcmVhc29uPzogc3RyaW5nOyBkYXlzTGVmdD86IG51bWJlcjsgYWN0aXZlRGF5cz86IG51bWJlciB9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgdXNlckRhdGEgPSBhd2FpdCBnZXRVc2VyRGF0YSh1c2VySWQpXG4gICAgaWYgKCF1c2VyRGF0YSkge1xuICAgICAgcmV0dXJuIHsgZXhwaXJlZDogdHJ1ZSwgcmVhc29uOiAnVXNlciBkYXRhIG5vdCBmb3VuZCcgfVxuICAgIH1cblxuICAgIC8vIElmIHVzZXIgaXMgb24gVHJpYWwgcGxhbiwgY2hlY2sgYmFzZWQgb24gam9pbmVkIGRhdGVcbiAgICBpZiAodXNlckRhdGEucGxhbiA9PT0gJ1RyaWFsJykge1xuICAgICAgY29uc3Qgam9pbmVkRGF0ZSA9IHVzZXJEYXRhLmpvaW5lZERhdGUgfHwgbmV3IERhdGUoKVxuICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpXG5cbiAgICAgIC8vIENhbGN1bGF0ZSBkYXlzIHNpbmNlIGpvaW5pbmcgKDAgb24gam9pbiBkYXksIDEgYWZ0ZXIgZmlyc3QgZGF5LCBldGMuKVxuICAgICAgY29uc3QgZGF5c1NpbmNlSm9pbmluZyA9IE1hdGguZmxvb3IoKHRvZGF5LmdldFRpbWUoKSAtIGpvaW5lZERhdGUuZ2V0VGltZSgpKSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSlcblxuICAgICAgLy8gQWN0aXZlIGRheXMgc3RhcnQgZnJvbSAxIG9uIGpvaW4gZGF5XG4gICAgICBjb25zdCBhY3RpdmVEYXlzID0gZGF5c1NpbmNlSm9pbmluZyArIDFcblxuICAgICAgLy8gVHJpYWwgbGFzdHMgMiBkYXlzLCBzbyBkYXlzIGxlZnQgPSAyIC0gZGF5cyBzaW5jZSBqb2luaW5nXG4gICAgICBjb25zdCB0cmlhbERheXNMZWZ0ID0gTWF0aC5tYXgoMCwgMiAtIGRheXNTaW5jZUpvaW5pbmcpXG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OFIFRyaWFsIHBsYW4gY2FsY3VsYXRpb246Jywge1xuICAgICAgICBqb2luZWREYXRlOiBqb2luZWREYXRlLnRvRGF0ZVN0cmluZygpLFxuICAgICAgICB0b2RheTogdG9kYXkudG9EYXRlU3RyaW5nKCksXG4gICAgICAgIGRheXNTaW5jZUpvaW5pbmcsXG4gICAgICAgIGFjdGl2ZURheXMsXG4gICAgICAgIHRyaWFsRGF5c0xlZnRcbiAgICAgIH0pXG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGV4cGlyZWQ6IHRyaWFsRGF5c0xlZnQgPD0gMCxcbiAgICAgICAgcmVhc29uOiB0cmlhbERheXNMZWZ0IDw9IDAgPyAnVHJpYWwgcGVyaW9kIGV4cGlyZWQnIDogdW5kZWZpbmVkLFxuICAgICAgICBkYXlzTGVmdDogdHJpYWxEYXlzTGVmdCxcbiAgICAgICAgYWN0aXZlRGF5czogYWN0aXZlRGF5c1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZvciBwYWlkIHBsYW5zLCBjaGVjayBpZiBwbGFuRXhwaXJ5IGlzIHNldFxuICAgIGlmICh1c2VyRGF0YS5wbGFuRXhwaXJ5KSB7XG4gICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKClcbiAgICAgIGNvbnN0IGV4cGlyZWQgPSB0b2RheSA+IHVzZXJEYXRhLnBsYW5FeHBpcnlcbiAgICAgIGNvbnN0IGRheXNMZWZ0ID0gZXhwaXJlZCA/IDAgOiBNYXRoLmNlaWwoKHVzZXJEYXRhLnBsYW5FeHBpcnkuZ2V0VGltZSgpIC0gdG9kYXkuZ2V0VGltZSgpKSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSlcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgZXhwaXJlZCxcbiAgICAgICAgcmVhc29uOiBleHBpcmVkID8gJ1BsYW4gc3Vic2NyaXB0aW9uIGV4cGlyZWQnIDogdW5kZWZpbmVkLFxuICAgICAgICBkYXlzTGVmdCxcbiAgICAgICAgYWN0aXZlRGF5czogdXNlckRhdGEuYWN0aXZlRGF5cyB8fCAwXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gSWYgcGxhbkV4cGlyeSBpcyBub3Qgc2V0LCBjYWxjdWxhdGUgYmFzZWQgb24gYWN0aXZlIGRheXMgYW5kIHBsYW4gdmFsaWRpdHlcbiAgICBjb25zdCBwbGFuVmFsaWRpdHlEYXlzID0gZ2V0UGxhblZhbGlkaXR5RGF5cyh1c2VyRGF0YS5wbGFuKVxuICAgIGNvbnN0IGN1cnJlbnRBY3RpdmVEYXlzID0gdXNlckRhdGEuYWN0aXZlRGF5cyB8fCAwXG4gICAgY29uc3QgZGF5c0xlZnQgPSBNYXRoLm1heCgwLCBwbGFuVmFsaWRpdHlEYXlzIC0gY3VycmVudEFjdGl2ZURheXMpXG4gICAgY29uc3QgZXhwaXJlZCA9IGRheXNMZWZ0IDw9IDBcblxuICAgIHJldHVybiB7XG4gICAgICBleHBpcmVkLFxuICAgICAgcmVhc29uOiBleHBpcmVkID8gYFBsYW4gdmFsaWRpdHkgcGVyaW9kICgke3BsYW5WYWxpZGl0eURheXN9IGRheXMpIGV4Y2VlZGVkIGJhc2VkIG9uIGFjdGl2ZSBkYXlzYCA6IHVuZGVmaW5lZCxcbiAgICAgIGRheXNMZWZ0LFxuICAgICAgYWN0aXZlRGF5czogY3VycmVudEFjdGl2ZURheXNcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgcGxhbiBleHBpcnk6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgZXhwaXJlZDogdHJ1ZSwgcmVhc29uOiAnRXJyb3IgY2hlY2tpbmcgcGxhbiBzdGF0dXMnIH1cbiAgfVxufVxuXG4vLyBVcGRhdGUgdXNlcidzIHBsYW4gZXhwaXJ5IHdoZW4gYWRtaW4gY2hhbmdlcyBwbGFuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlVXNlclBsYW5FeHBpcnkodXNlcklkOiBzdHJpbmcsIG5ld1BsYW46IHN0cmluZywgY3VzdG9tRXhwaXJ5RGF0ZT86IERhdGUpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB1c2VyUmVmID0gZG9jKGRiLCBDT0xMRUNUSU9OUy51c2VycywgdXNlcklkKVxuXG4gICAgaWYgKG5ld1BsYW4gPT09ICdUcmlhbCcpIHtcbiAgICAgIC8vIFRyaWFsIHBsYW4gZG9lc24ndCBoYXZlIGV4cGlyeSwgaXQncyBiYXNlZCBvbiBqb2luZWQgZGF0ZVxuICAgICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgICAgW0ZJRUxEX05BTUVTLnBsYW5FeHBpcnldOiBudWxsXG4gICAgICB9KVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBTZXQgZXhwaXJ5IGRhdGUgZm9yIHBhaWQgcGxhbnNcbiAgICAgIGxldCBleHBpcnlEYXRlOiBEYXRlXG5cbiAgICAgIGlmIChjdXN0b21FeHBpcnlEYXRlKSB7XG4gICAgICAgIGV4cGlyeURhdGUgPSBjdXN0b21FeHBpcnlEYXRlXG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBDYWxjdWxhdGUgZXhwaXJ5IGJhc2VkIG9uIHBsYW4gdmFsaWRpdHlcbiAgICAgICAgY29uc3QgdmFsaWRpdHlEYXlzID0gZ2V0UGxhblZhbGlkaXR5RGF5cyhuZXdQbGFuKVxuICAgICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKClcbiAgICAgICAgZXhwaXJ5RGF0ZSA9IG5ldyBEYXRlKHRvZGF5LmdldFRpbWUoKSArICh2YWxpZGl0eURheXMgKiAyNCAqIDYwICogNjAgKiAxMDAwKSlcbiAgICAgIH1cblxuICAgICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgICAgW0ZJRUxEX05BTUVTLnBsYW5FeHBpcnldOiBUaW1lc3RhbXAuZnJvbURhdGUoZXhwaXJ5RGF0ZSlcbiAgICAgIH0pXG5cbiAgICAgIGNvbnNvbGUubG9nKGBVcGRhdGVkIHBsYW4gZXhwaXJ5IGZvciB1c2VyICR7dXNlcklkfSB0byAke2V4cGlyeURhdGUudG9EYXRlU3RyaW5nKCl9YClcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgcGxhbiBleHBpcnk6JywgZXJyb3IpXG4gICAgdGhyb3cgZXJyb3JcbiAgfVxufVxuXG4vLyBHZXQgcmVmZXJyYWwgYm9udXMgYmFzZWQgb24gcGxhblxuZXhwb3J0IGZ1bmN0aW9uIGdldFJlZmVycmFsQm9udXMocGxhbjogc3RyaW5nKTogbnVtYmVyIHtcbiAgY29uc3QgcmVmZXJyYWxCb251c2VzOiB7IFtrZXk6IHN0cmluZ106IG51bWJlciB9ID0ge1xuICAgICdUcmlhbCc6IDAsICAgICAvLyBUcmlhbCBwbGFuIC0+IE5vIGJvbnVzXG4gICAgJ0p1bmlvcic6IDMwMCwgIC8vIEp1bmlvciBwbGFuIC0+IFJzIDMwMCBib251cyArIDUwIHRyYW5zbGF0aW9uc1xuICAgICdTZW5pb3InOiA3MDAsICAvLyBTZW5pb3IgcGxhbiAtPiBScyA3MDAgYm9udXMgKyA1MCB0cmFuc2xhdGlvbnNcbiAgICAnRXhwZXJ0JzogMTIwMCwgLy8gRXhwZXJ0IHBsYW4gLT4gUnMgMTIwMCBib251cyArIDUwIHRyYW5zbGF0aW9uc1xuICAgICc0OTknOiA1MCwgICAgICAvLyBMZWdhY3kgUnMgNDk5IHBsYW4gLT4gUnMgNTAgYm9udXNcbiAgICAnMTQ5OSc6IDE1MCwgICAgLy8gTGVnYWN5IFJzIDE0OTkgcGxhbiAtPiBScyAxNTAgYm9udXNcbiAgICAnMjk5OSc6IDMwMCwgICAgLy8gTGVnYWN5IFJzIDI5OTkgcGxhbiAtPiBScyAzMDAgYm9udXNcbiAgICAnMzk5OSc6IDQwMCwgICAgLy8gTGVnYWN5IFJzIDM5OTkgcGxhbiAtPiBScyA0MDAgYm9udXNcbiAgICAnNTk5OSc6IDcwMCwgICAgLy8gTGVnYWN5IFJzIDU5OTkgcGxhbiAtPiBScyA3MDAgYm9udXNcbiAgICAnOTk5OSc6IDEyMDAsICAgLy8gTGVnYWN5IFJzIDk5OTkgcGxhbiAtPiBScyAxMjAwIGJvbnVzXG4gICAgJ1N0YXJ0ZXInOiA1MCwgIC8vIExlZ2FjeSBwbGFuIG1hcHBpbmdcbiAgICAnQmFzaWMnOiAxNTAsICAgLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICdQcmVtaXVtJzogMzAwLCAvLyBMZWdhY3kgcGxhbiBtYXBwaW5nXG4gICAgJ0dvbGQnOiA0MDAsICAgIC8vIExlZ2FjeSBwbGFuIG1hcHBpbmdcbiAgICAnUGxhdGludW0nOiA3MDAsLy8gTGVnYWN5IHBsYW4gbWFwcGluZ1xuICAgICdEaWFtb25kJzogMTIwMCAvLyBMZWdhY3kgcGxhbiBtYXBwaW5nXG4gIH1cblxuICByZXR1cm4gcmVmZXJyYWxCb251c2VzW3BsYW5dIHx8IDBcbn1cblxuLy8gUHJvY2VzcyByZWZlcnJhbCBib251cyB3aGVuIGFkbWluIHVwZ3JhZGVzIHVzZXIgZnJvbSBUcmlhbCB0byBwYWlkIHBsYW5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBwcm9jZXNzUmVmZXJyYWxCb251cyh1c2VySWQ6IHN0cmluZywgb2xkUGxhbjogc3RyaW5nLCBuZXdQbGFuOiBzdHJpbmcpIHtcbiAgdHJ5IHtcbiAgICBjb25zb2xlLmxvZyhg8J+OryBSRUZFUlJBTCBCT05VUyBERUJVRzogU3RhcnRpbmcgcHJvY2VzcyBmb3IgdXNlciAke3VzZXJJZH1gKVxuICAgIGNvbnNvbGUubG9nKGDwn46vIFBsYW4gY2hhbmdlOiAke29sZFBsYW59IOKGkiAke25ld1BsYW59YClcblxuICAgIC8vIE9ubHkgcHJvY2VzcyBib251cyB3aGVuIHVwZ3JhZGluZyBGUk9NIFRyaWFsIFRPIGEgcGFpZCBwbGFuXG4gICAgaWYgKG9sZFBsYW4gIT09ICdUcmlhbCcgfHwgbmV3UGxhbiA9PT0gJ1RyaWFsJykge1xuICAgICAgY29uc29sZS5sb2coJ+KdjCBSZWZlcnJhbCBib251cyBvbmx5IGFwcGxpZXMgd2hlbiB1cGdyYWRpbmcgZnJvbSBUcmlhbCB0byBwYWlkIHBsYW4nKVxuICAgICAgY29uc29sZS5sb2coYOKdjCBDdXJyZW50IGNvbmRpdGlvbnM6IG9sZFBsYW49JHtvbGRQbGFufSwgbmV3UGxhbj0ke25ld1BsYW59YClcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGDinIUgUHJvY2Vzc2luZyByZWZlcnJhbCBib251cyBmb3IgdXNlciAke3VzZXJJZH0gdXBncmFkaW5nIGZyb20gJHtvbGRQbGFufSB0byAke25ld1BsYW59YClcblxuICAgIC8vIEdldCB0aGUgdXNlcidzIGRhdGEgdG8gZmluZCB0aGVpciByZWZlcnJhbCBpbmZvXG4gICAgY29uc3QgdXNlckRvYyA9IGF3YWl0IGdldERvYyhkb2MoZGIsIENPTExFQ1RJT05TLnVzZXJzLCB1c2VySWQpKVxuICAgIGlmICghdXNlckRvYy5leGlzdHMoKSkge1xuICAgICAgY29uc29sZS5sb2coJ+KdjCBVc2VyIG5vdCBmb3VuZCBpbiBkYXRhYmFzZScpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBjb25zdCB1c2VyRGF0YSA9IHVzZXJEb2MuZGF0YSgpXG4gICAgY29uc3QgcmVmZXJyZWRCeSA9IHVzZXJEYXRhW0ZJRUxEX05BTUVTLnJlZmVycmVkQnldXG4gICAgY29uc3QgYWxyZWFkeUNyZWRpdGVkID0gdXNlckRhdGFbRklFTERfTkFNRVMucmVmZXJyYWxCb251c0NyZWRpdGVkXVxuXG4gICAgY29uc29sZS5sb2coYPCflI0gVXNlciByZWZlcnJhbCBkYXRhOmApXG4gICAgY29uc29sZS5sb2coYCAgIC0gUmVmZXJyZWQgYnk6ICR7cmVmZXJyZWRCeSB8fCAnTm9uZSd9YClcbiAgICBjb25zb2xlLmxvZyhgICAgLSBBbHJlYWR5IGNyZWRpdGVkOiAke2FscmVhZHlDcmVkaXRlZH1gKVxuICAgIGNvbnNvbGUubG9nKGAgICAtIFVzZXIgbmFtZTogJHt1c2VyRGF0YVtGSUVMRF9OQU1FUy5uYW1lXX1gKVxuXG4gICAgaWYgKCFyZWZlcnJlZEJ5KSB7XG4gICAgICBjb25zb2xlLmxvZygn4p2MIFVzZXIgd2FzIG5vdCByZWZlcnJlZCBieSBhbnlvbmUsIHNraXBwaW5nIGJvbnVzIHByb2Nlc3NpbmcnKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgaWYgKGFscmVhZHlDcmVkaXRlZCkge1xuICAgICAgY29uc29sZS5sb2coJ+KdjCBSZWZlcnJhbCBib251cyBhbHJlYWR5IGNyZWRpdGVkIGZvciB0aGlzIHVzZXIsIHNraXBwaW5nJylcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGDwn5SNIEZpbmRpbmcgcmVmZXJyZXIgd2l0aCBjb2RlOiAke3JlZmVycmVkQnl9YClcblxuICAgIC8vIEZpbmQgdGhlIHJlZmVycmVyIGJ5IHJlZmVycmFsIGNvZGVcbiAgICBjb25zdCBxID0gcXVlcnkoXG4gICAgICBjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy51c2VycyksXG4gICAgICB3aGVyZShGSUVMRF9OQU1FUy5yZWZlcnJhbENvZGUsICc9PScsIHJlZmVycmVkQnkpLFxuICAgICAgbGltaXQoMSlcbiAgICApXG5cbiAgICBjb25zdCBxdWVyeVNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhxKVxuXG4gICAgaWYgKHF1ZXJ5U25hcHNob3QuZW1wdHkpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDinYwgUmVmZXJyYWwgY29kZSBub3QgZm91bmQ6ICR7cmVmZXJyZWRCeX1gKVxuICAgICAgY29uc29sZS5sb2coJ+KdjCBObyByZWZlcnJlciBmb3VuZCB3aXRoIHRoaXMgY29kZSBpbiBkYXRhYmFzZScpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBjb25zdCByZWZlcnJlckRvYyA9IHF1ZXJ5U25hcHNob3QuZG9jc1swXVxuICAgIGNvbnN0IHJlZmVycmVySWQgPSByZWZlcnJlckRvYy5pZFxuICAgIGNvbnN0IHJlZmVycmVyRGF0YSA9IHJlZmVycmVyRG9jLmRhdGEoKVxuICAgIGNvbnN0IGJvbnVzQW1vdW50ID0gZ2V0UmVmZXJyYWxCb251cyhuZXdQbGFuKVxuXG4gICAgY29uc29sZS5sb2coYOKchSBGb3VuZCByZWZlcnJlcjpgKVxuICAgIGNvbnNvbGUubG9nKGAgICAtIFJlZmVycmVyIElEOiAke3JlZmVycmVySWR9YClcbiAgICBjb25zb2xlLmxvZyhgICAgLSBSZWZlcnJlciBuYW1lOiAke3JlZmVycmVyRGF0YVtGSUVMRF9OQU1FUy5uYW1lXX1gKVxuICAgIGNvbnNvbGUubG9nKGAgICAtIEJvbnVzIGFtb3VudDog4oK5JHtib251c0Ftb3VudH1gKVxuICAgIGNvbnNvbGUubG9nKGAgICAtIFBsYW46ICR7bmV3UGxhbn1gKVxuXG4gICAgaWYgKGJvbnVzQW1vdW50ID4gMCkge1xuICAgICAgY29uc29sZS5sb2coYPCfkrAgUHJvY2Vzc2luZyBib251cyBwYXltZW50Li4uYClcblxuICAgICAgLy8gQWRkIGJvbnVzIHRvIHJlZmVycmVyJ3Mgd2FsbGV0XG4gICAgICBhd2FpdCB1cGRhdGVXYWxsZXRCYWxhbmNlKHJlZmVycmVySWQsIGJvbnVzQW1vdW50KVxuICAgICAgY29uc29sZS5sb2coYOKchSBBZGRlZCDigrkke2JvbnVzQW1vdW50fSB0byByZWZlcnJlcidzIHdhbGxldGApXG5cbiAgICAgIC8vIEFkZCA1MCB0cmFuc2xhdGlvbnMgdG8gcmVmZXJyZXIncyB0b3RhbCB0cmFuc2xhdGlvbiBjb3VudFxuICAgICAgY29uc3QgcmVmZXJyZXJSZWYgPSBkb2MoZGIsIENPTExFQ1RJT05TLnVzZXJzLCByZWZlcnJlcklkKVxuICAgICAgYXdhaXQgdXBkYXRlRG9jKHJlZmVycmVyUmVmLCB7XG4gICAgICAgIFtGSUVMRF9OQU1FUy50b3RhbFRyYW5zbGF0aW9uc106IGluY3JlbWVudCg1MClcbiAgICAgIH0pXG4gICAgICBjb25zb2xlLmxvZyhg4pyFIEFkZGVkIDUwIGJvbnVzIHRyYW5zbGF0aW9ucyB0byByZWZlcnJlcmApXG5cbiAgICAgIC8vIE1hcmsgcmVmZXJyYWwgYm9udXMgYXMgY3JlZGl0ZWQgZm9yIHRoaXMgdXNlclxuICAgICAgY29uc3QgdXNlclJlZiA9IGRvYyhkYiwgQ09MTEVDVElPTlMudXNlcnMsIHVzZXJJZClcbiAgICAgIGF3YWl0IHVwZGF0ZURvYyh1c2VyUmVmLCB7XG4gICAgICAgIFtGSUVMRF9OQU1FUy5yZWZlcnJhbEJvbnVzQ3JlZGl0ZWRdOiB0cnVlXG4gICAgICB9KVxuICAgICAgY29uc29sZS5sb2coYOKchSBNYXJrZWQgcmVmZXJyYWwgYm9udXMgYXMgY3JlZGl0ZWQgZm9yIHVzZXIgJHt1c2VySWR9YClcblxuICAgICAgLy8gQWRkIHRyYW5zYWN0aW9uIHJlY29yZCBmb3IgcmVmZXJyYWwgYm9udXNcbiAgICAgIGF3YWl0IGFkZFRyYW5zYWN0aW9uKHJlZmVycmVySWQsIHtcbiAgICAgICAgdHlwZTogJ3JlZmVycmFsX2JvbnVzJyxcbiAgICAgICAgYW1vdW50OiBib251c0Ftb3VudCxcbiAgICAgICAgZGVzY3JpcHRpb246IGBSZWZlcnJhbCBib251cyBmb3IgJHtuZXdQbGFufSBwbGFuIHVwZ3JhZGUgKyA1MCBib251cyB0cmFuc2xhdGlvbnMgKFVzZXI6ICR7dXNlckRhdGFbRklFTERfTkFNRVMubmFtZV19KWBcbiAgICAgIH0pXG4gICAgICBjb25zb2xlLmxvZyhg4pyFIEFkZGVkIHRyYW5zYWN0aW9uIHJlY29yZCBmb3IgcmVmZXJyYWwgYm9udXNgKVxuXG4gICAgICBjb25zb2xlLmxvZyhg8J+OiSBSRUZFUlJBTCBCT05VUyBDT01QTEVURUQ6IOKCuSR7Ym9udXNBbW91bnR9ICsgNTAgdHJhbnNsYXRpb25zIGZvciByZWZlcnJlciAke3JlZmVycmVySWR9YClcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc29sZS5sb2coYOKdjCBObyBib251cyBhbW91bnQgY2FsY3VsYXRlZCBmb3IgcGxhbjogJHtuZXdQbGFufWApXG4gICAgICBjb25zb2xlLmxvZyhg4p2MIEF2YWlsYWJsZSBwbGFuczpgLCBPYmplY3Qua2V5cyh7XG4gICAgICAgICdUcmlhbCc6IDAsICdKdW5pb3InOiAzMDAsICdTZW5pb3InOiA3MDAsICdFeHBlcnQnOiAxMjAwXG4gICAgICB9KSlcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIHByb2Nlc3NpbmcgcmVmZXJyYWwgYm9udXM6JywgZXJyb3IpXG4gICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGRldGFpbHM6JywgZXJyb3IpXG4gICAgLy8gRG9uJ3QgdGhyb3cgZXJyb3IgdG8gYXZvaWQgYnJlYWtpbmcgcGxhbiB1cGRhdGVcbiAgfVxufVxuXG4vLyBNYW51YWwgcmVmZXJyYWwgYm9udXMgcHJvY2Vzc2luZyAoZm9yIHRlc3RpbmcvZGVidWdnaW5nKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIG1hbnVhbFByb2Nlc3NSZWZlcnJhbEJvbnVzKHVzZXJJZDogc3RyaW5nKSB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coYPCflKcgTUFOVUFMIFJFRkVSUkFMIEJPTlVTOiBQcm9jZXNzaW5nIGZvciB1c2VyICR7dXNlcklkfWApXG5cbiAgICBjb25zdCB1c2VyRGF0YSA9IGF3YWl0IGdldFVzZXJEYXRhKHVzZXJJZClcbiAgICBpZiAoIXVzZXJEYXRhKSB7XG4gICAgICBjb25zb2xlLmxvZygn4p2MIFVzZXIgbm90IGZvdW5kJylcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnVXNlciBub3QgZm91bmQnIH1cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhg8J+UjSBVc2VyIGRhdGE6YClcbiAgICBjb25zb2xlLmxvZyhgICAgLSBOYW1lOiAke3VzZXJEYXRhLm5hbWV9YClcbiAgICBjb25zb2xlLmxvZyhgICAgLSBQbGFuOiAke3VzZXJEYXRhLnBsYW59YClcbiAgICBjb25zb2xlLmxvZyhgICAgLSBSZWZlcnJlZCBieTogJHt1c2VyRGF0YS5yZWZlcnJlZEJ5IHx8ICdOb25lJ31gKVxuICAgIGNvbnNvbGUubG9nKGAgICAtIEJvbnVzIGNyZWRpdGVkOiAke3VzZXJEYXRhLnJlZmVycmFsQm9udXNDcmVkaXRlZH1gKVxuXG4gICAgaWYgKHVzZXJEYXRhLnBsYW4gPT09ICdUcmlhbCcpIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnVXNlciBpcyBzdGlsbCBvbiBUcmlhbCBwbGFuLiBVcGdyYWRlIHRvIHBhaWQgcGxhbiBmaXJzdC4nIH1cbiAgICB9XG5cbiAgICBpZiAoIXVzZXJEYXRhLnJlZmVycmVkQnkpIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnVXNlciB3YXMgbm90IHJlZmVycmVkIGJ5IGFueW9uZScgfVxuICAgIH1cblxuICAgIGlmICh1c2VyRGF0YS5yZWZlcnJhbEJvbnVzQ3JlZGl0ZWQpIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnUmVmZXJyYWwgYm9udXMgYWxyZWFkeSBjcmVkaXRlZCcgfVxuICAgIH1cblxuICAgIC8vIFByb2Nlc3MgdGhlIGJvbnVzIG1hbnVhbGx5XG4gICAgYXdhaXQgcHJvY2Vzc1JlZmVycmFsQm9udXModXNlcklkLCAnVHJpYWwnLCB1c2VyRGF0YS5wbGFuKVxuXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogYFJlZmVycmFsIGJvbnVzIHByb2Nlc3NlZCBmb3IgJHt1c2VyRGF0YS5wbGFufSBwbGFuYCB9XG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgaW4gbWFudWFsIHJlZmVycmFsIGJvbnVzIHByb2Nlc3Npbmc6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6IGBFcnJvcjogJHtlcnJvcj8ubWVzc2FnZSB8fCAnVW5rbm93biBlcnJvcid9YCB9XG4gIH1cbn1cblxuLy8gR2V0IHVzZXIgdHJhbnNsYXRpb24gc2V0dGluZ3MgKGR1cmF0aW9uIGFuZCBlYXJuaW5nIHBlciBiYXRjaClcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRVc2VyVmlkZW9TZXR0aW5ncyh1c2VySWQ6IHN0cmluZykge1xuICB0cnkge1xuICAgIGNvbnN0IHVzZXJEYXRhID0gYXdhaXQgZ2V0VXNlckRhdGEodXNlcklkKVxuICAgIGlmICghdXNlckRhdGEpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHRyYW5zbGF0aW9uRHVyYXRpb246IDMwLCAvLyBEZWZhdWx0IDMwIHNlY29uZHMgZm9yIHRyaWFsXG4gICAgICAgIGVhcm5pbmdQZXJCYXRjaDogMjUsIC8vIERlZmF1bHQgdHJpYWwgZWFybmluZyBwZXIgYmF0Y2ggb2YgNTAgdHJhbnNsYXRpb25zXG4gICAgICAgIHBsYW46ICdUcmlhbCcsXG4gICAgICAgIGhhc1F1aWNrQWR2YW50YWdlOiBmYWxzZVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHVzZXIgaGFzIGFjdGl2ZSBxdWljayB0cmFuc2xhdGlvbiBhZHZhbnRhZ2VcbiAgICBjb25zdCBoYXNBY3RpdmVRdWlja0FkdmFudGFnZSA9IGNoZWNrUXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZUFjdGl2ZSh1c2VyRGF0YSlcblxuICAgIGxldCB0cmFuc2xhdGlvbkR1cmF0aW9uID0gdXNlckRhdGEudHJhbnNsYXRpb25EdXJhdGlvblxuXG4gICAgLy8gSWYgdXNlciBoYXMgYWN0aXZlIHF1aWNrIHRyYW5zbGF0aW9uIGFkdmFudGFnZSwgdXNlIGN1c3RvbSBzZWNvbmRzIG9yIGRlZmF1bHQgdG8gMzBcbiAgICBpZiAoaGFzQWN0aXZlUXVpY2tBZHZhbnRhZ2UpIHtcbiAgICAgIHRyYW5zbGF0aW9uRHVyYXRpb24gPSB1c2VyRGF0YS5xdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlU2Vjb25kcyB8fCAzMCAvLyBVc2UgY3VzdG9tIGR1cmF0aW9uIG9yIGRlZmF1bHQgdG8gMzAgc2Vjb25kc1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBVc2UgcGxhbi1iYXNlZCB0cmFuc2xhdGlvbiBkdXJhdGlvbiwgYnV0IGFsbG93IGFkbWluIG92ZXJyaWRlcyBmb3Igbm9uLXRyaWFsIHVzZXJzXG4gICAgICBpZiAoIXRyYW5zbGF0aW9uRHVyYXRpb24gfHwgdXNlckRhdGEucGxhbiA9PT0gJ1RyaWFsJykge1xuICAgICAgICB0cmFuc2xhdGlvbkR1cmF0aW9uID0gZ2V0UGxhblZpZGVvRHVyYXRpb24odXNlckRhdGEucGxhbilcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgdHJhbnNsYXRpb25EdXJhdGlvbjogdHJhbnNsYXRpb25EdXJhdGlvbixcbiAgICAgIGVhcm5pbmdQZXJCYXRjaDogZ2V0UGxhbkVhcm5pbmcodXNlckRhdGEucGxhbiksIC8vIEVhcm5pbmcgcGVyIGJhdGNoIG9mIDUwIHRyYW5zbGF0aW9uc1xuICAgICAgcGxhbjogdXNlckRhdGEucGxhbixcbiAgICAgIGhhc1F1aWNrQWR2YW50YWdlOiBoYXNBY3RpdmVRdWlja0FkdmFudGFnZSxcbiAgICAgIHF1aWNrQWR2YW50YWdlRXhwaXJ5OiB1c2VyRGF0YS5xdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlRXhwaXJ5XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgdXNlciB0cmFuc2xhdGlvbiBzZXR0aW5nczonLCBlcnJvcilcbiAgICByZXR1cm4ge1xuICAgICAgdHJhbnNsYXRpb25EdXJhdGlvbjogMzAsIC8vIERlZmF1bHQgMzAgc2Vjb25kcyBmb3IgdHJpYWxcbiAgICAgIGVhcm5pbmdQZXJCYXRjaDogMjUsIC8vIERlZmF1bHQgdHJpYWwgZWFybmluZyBwZXIgYmF0Y2ggb2YgNTAgdHJhbnNsYXRpb25zXG4gICAgICBwbGFuOiAnVHJpYWwnLFxuICAgICAgaGFzUXVpY2tBZHZhbnRhZ2U6IGZhbHNlXG4gICAgfVxuICB9XG59XG5cbi8vIENoZWNrIGlmIHVzZXIgaGFzIGFjdGl2ZSBxdWljayB0cmFuc2xhdGlvbiBhZHZhbnRhZ2VcbmV4cG9ydCBmdW5jdGlvbiBjaGVja1F1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VBY3RpdmUodXNlckRhdGE6IGFueSk6IGJvb2xlYW4ge1xuICBpZiAoIXVzZXJEYXRhLnF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2UgfHwgIXVzZXJEYXRhLnF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VFeHBpcnkpIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcbiAgY29uc3QgZXhwaXJ5ID0gdXNlckRhdGEucXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZUV4cGlyeVxuXG4gIHJldHVybiBub3cgPCBleHBpcnlcbn1cblxuLy8gR3JhbnQgcXVpY2sgdHJhbnNsYXRpb24gYWR2YW50YWdlIHRvIHVzZXIgKGFkbWluIGZ1bmN0aW9uKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdyYW50UXVpY2tWaWRlb0FkdmFudGFnZSh1c2VySWQ6IHN0cmluZywgZGF5czogbnVtYmVyLCBncmFudGVkQnk6IHN0cmluZywgc2Vjb25kczogbnVtYmVyID0gMzApIHtcbiAgdHJ5IHtcbiAgICBpZiAoZGF5cyA8PSAwIHx8IGRheXMgPiAzNjUpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignRGF5cyBtdXN0IGJlIGJldHdlZW4gMSBhbmQgMzY1JylcbiAgICB9XG5cbiAgICBpZiAoc2Vjb25kcyA8IDEgfHwgc2Vjb25kcyA+IDQyMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdTZWNvbmRzIG11c3QgYmUgYmV0d2VlbiAxIGFuZCA0MjAgKDcgbWludXRlcyknKVxuICAgIH1cblxuICAgIC8vIFVzZSBjZW50cmFsaXplZCBjb3B5LXBhc3RlIHNlcnZpY2VcbiAgICBjb25zdCB7IGdyYW50Q29weVBhc3RlUGVybWlzc2lvbiB9ID0gYXdhaXQgaW1wb3J0KCcuL2NvcHlQYXN0ZVNlcnZpY2UnKVxuICAgIGF3YWl0IGdyYW50Q29weVBhc3RlUGVybWlzc2lvbih1c2VySWQsIGRheXMpXG5cbiAgICBjb25zb2xlLmxvZyhgR3JhbnRlZCBjb3B5LXBhc3RlIHBlcm1pc3Npb24gdG8gdXNlciAke3VzZXJJZH0gZm9yICR7ZGF5c30gZGF5cyBieSAke2dyYW50ZWRCeX1gKVxuXG4gICAgLy8gQWRkIHRyYW5zYWN0aW9uIHJlY29yZFxuICAgIGF3YWl0IGFkZFRyYW5zYWN0aW9uKHVzZXJJZCwge1xuICAgICAgdHlwZTogJ3F1aWNrX2FkdmFudGFnZV9ncmFudGVkJyxcbiAgICAgIGFtb3VudDogMCxcbiAgICAgIGRlc2NyaXB0aW9uOiBgQ29weS1wYXN0ZSBwZXJtaXNzaW9uIGdyYW50ZWQgZm9yICR7ZGF5c30gZGF5cyBieSAke2dyYW50ZWRCeX1gXG4gICAgfSlcblxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdyYW50aW5nIHF1aWNrIHZpZGVvIGFkdmFudGFnZTonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIFJlbW92ZSBxdWljayB0cmFuc2xhdGlvbiBhZHZhbnRhZ2UgZnJvbSB1c2VyIChhZG1pbiBmdW5jdGlvbilcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiByZW1vdmVRdWlja1ZpZGVvQWR2YW50YWdlKHVzZXJJZDogc3RyaW5nLCByZW1vdmVkQnk6IHN0cmluZykge1xuICB0cnkge1xuICAgIC8vIFVzZSBjZW50cmFsaXplZCBjb3B5LXBhc3RlIHNlcnZpY2VcbiAgICBjb25zdCB7IHJlbW92ZUNvcHlQYXN0ZVBlcm1pc3Npb24gfSA9IGF3YWl0IGltcG9ydCgnLi9jb3B5UGFzdGVTZXJ2aWNlJylcbiAgICBhd2FpdCByZW1vdmVDb3B5UGFzdGVQZXJtaXNzaW9uKHVzZXJJZClcblxuICAgIGNvbnNvbGUubG9nKGBSZW1vdmVkIGNvcHktcGFzdGUgcGVybWlzc2lvbiBmcm9tIHVzZXIgJHt1c2VySWR9IGJ5ICR7cmVtb3ZlZEJ5fWApXG5cbiAgICAvLyBBZGQgdHJhbnNhY3Rpb24gcmVjb3JkXG4gICAgYXdhaXQgYWRkVHJhbnNhY3Rpb24odXNlcklkLCB7XG4gICAgICB0eXBlOiAncXVpY2tfYWR2YW50YWdlX3JlbW92ZWQnLFxuICAgICAgYW1vdW50OiAwLFxuICAgICAgZGVzY3JpcHRpb246IGBDb3B5LXBhc3RlIHBlcm1pc3Npb24gcmVtb3ZlZCBieSAke3JlbW92ZWRCeX1gXG4gICAgfSlcblxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlbW92aW5nIHF1aWNrIHZpZGVvIGFkdmFudGFnZTonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIFVwZGF0ZSB1c2VyIHRyYW5zbGF0aW9uIGR1cmF0aW9uIChhZG1pbiBmdW5jdGlvbilcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1cGRhdGVVc2VyVmlkZW9EdXJhdGlvbih1c2VySWQ6IHN0cmluZywgZHVyYXRpb25JblNlY29uZHM6IG51bWJlcikge1xuICB0cnkge1xuICAgIC8vIFZhbGlkYXRlIGR1cmF0aW9uIChxdWljayBkdXJhdGlvbnM6IDEsIDEwLCAzMCBzZWNvbmRzIE9SIHN0YW5kYXJkIGR1cmF0aW9uczogMS03IG1pbnV0ZXMpXG4gICAgY29uc3QgaXNRdWlja0R1cmF0aW9uID0gWzEsIDEwLCAzMF0uaW5jbHVkZXMoZHVyYXRpb25JblNlY29uZHMpXG4gICAgY29uc3QgaXNTdGFuZGFyZER1cmF0aW9uID0gZHVyYXRpb25JblNlY29uZHMgPj0gNjAgJiYgZHVyYXRpb25JblNlY29uZHMgPD0gNDIwXG5cbiAgICBpZiAoIWlzUXVpY2tEdXJhdGlvbiAmJiAhaXNTdGFuZGFyZER1cmF0aW9uKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1RyYW5zbGF0aW9uIGR1cmF0aW9uIG11c3QgYmUgMSwgMTAsIG9yIDMwIHNlY29uZHMgZm9yIHF1aWNrIGR1cmF0aW9uLCBvciBiZXR3ZWVuIDEtNyBtaW51dGVzICg2MC00MjAgc2Vjb25kcykgZm9yIHN0YW5kYXJkIGR1cmF0aW9uJylcbiAgICB9XG5cbiAgICBjb25zdCB1c2VyUmVmID0gZG9jKGRiLCBDT0xMRUNUSU9OUy51c2VycywgdXNlcklkKVxuICAgIGF3YWl0IHVwZGF0ZURvYyh1c2VyUmVmLCB7XG4gICAgICBbRklFTERfTkFNRVMudHJhbnNsYXRpb25EdXJhdGlvbl06IGR1cmF0aW9uSW5TZWNvbmRzXG4gICAgfSlcblxuICAgIGNvbnNvbGUubG9nKGBVcGRhdGVkIHRyYW5zbGF0aW9uIGR1cmF0aW9uIGZvciB1c2VyICR7dXNlcklkfSB0byAke2R1cmF0aW9uSW5TZWNvbmRzfSBzZWNvbmRzYClcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB1c2VyIHRyYW5zbGF0aW9uIGR1cmF0aW9uOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cblxuLy8gVmFsaWRhdGUgYmFuayBkZXRhaWxzXG5mdW5jdGlvbiB2YWxpZGF0ZUJhbmtEZXRhaWxzKGJhbmtEZXRhaWxzOiBCYW5rRGV0YWlscykge1xuICBjb25zdCB7IGFjY291bnRIb2xkZXJOYW1lLCBhY2NvdW50TnVtYmVyLCBpZnNjQ29kZSwgYmFua05hbWUgfSA9IGJhbmtEZXRhaWxzXG5cbiAgaWYgKCFhY2NvdW50SG9sZGVyTmFtZSB8fCBhY2NvdW50SG9sZGVyTmFtZS50cmltKCkubGVuZ3RoIDwgMikge1xuICAgIHRocm93IG5ldyBFcnJvcignQWNjb3VudCBob2xkZXIgbmFtZSBtdXN0IGJlIGF0IGxlYXN0IDIgY2hhcmFjdGVycyBsb25nJylcbiAgfVxuXG4gIGlmICghYWNjb3VudE51bWJlciB8fCAhL15cXGR7OSwxOH0kLy50ZXN0KGFjY291bnROdW1iZXIudHJpbSgpKSkge1xuICAgIHRocm93IG5ldyBFcnJvcignQWNjb3VudCBudW1iZXIgbXVzdCBiZSA5LTE4IGRpZ2l0cycpXG4gIH1cblxuICBpZiAoIWlmc2NDb2RlIHx8ICEvXltBLVpdezR9MFtBLVowLTldezZ9JC8udGVzdChpZnNjQ29kZS50cmltKCkudG9VcHBlckNhc2UoKSkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgSUZTQyBjb2RlIGZvcm1hdCAoZS5nLiwgU0JJTjAwMDEyMzQpJylcbiAgfVxuXG4gIGlmICghYmFua05hbWUgfHwgYmFua05hbWUudHJpbSgpLmxlbmd0aCA8IDIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0JhbmsgbmFtZSBtdXN0IGJlIGF0IGxlYXN0IDIgY2hhcmFjdGVycyBsb25nJylcbiAgfVxufVxuXG4vLyBOb3RpZmljYXRpb24gaW50ZXJmYWNlXG5leHBvcnQgaW50ZXJmYWNlIE5vdGlmaWNhdGlvbiB7XG4gIGlkPzogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIHR5cGU6ICdpbmZvJyB8ICdzdWNjZXNzJyB8ICd3YXJuaW5nJyB8ICdlcnJvcidcbiAgdGFyZ2V0VXNlcnM6ICdhbGwnIHwgJ3NwZWNpZmljJ1xuICB1c2VySWRzPzogc3RyaW5nW11cbiAgY3JlYXRlZEF0OiBEYXRlXG4gIGNyZWF0ZWRCeTogc3RyaW5nXG4gIC8vIEFsbCBub3RpZmljYXRpb25zIGFyZSBub3cgYmxvY2tpbmcgYnkgZGVmYXVsdCAtIG5vIG5lZWQgZm9yIGlzQmxvY2tpbmcgZmllbGRcbn1cblxuLy8gQWRkIG5vdGlmaWNhdGlvbiAoYWRtaW4gZnVuY3Rpb24pIC0gQWxsIG5vdGlmaWNhdGlvbnMgYXJlIG5vdyBibG9ja2luZ1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGFkZE5vdGlmaWNhdGlvbihub3RpZmljYXRpb246IE9taXQ8Tm90aWZpY2F0aW9uLCAnaWQnIHwgJ2NyZWF0ZWRBdCc+KSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgbm90aWZpY2F0aW9uRGF0YSA9IHtcbiAgICAgIHRpdGxlOiBub3RpZmljYXRpb24udGl0bGUsXG4gICAgICBtZXNzYWdlOiBub3RpZmljYXRpb24ubWVzc2FnZSxcbiAgICAgIHR5cGU6IG5vdGlmaWNhdGlvbi50eXBlLFxuICAgICAgdGFyZ2V0VXNlcnM6IG5vdGlmaWNhdGlvbi50YXJnZXRVc2VycyxcbiAgICAgIHVzZXJJZHM6IG5vdGlmaWNhdGlvbi51c2VySWRzIHx8IFtdLFxuICAgICAgY3JlYXRlZEF0OiBUaW1lc3RhbXAubm93KCksXG4gICAgICBjcmVhdGVkQnk6IG5vdGlmaWNhdGlvbi5jcmVhdGVkQnlcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnQWRkaW5nIG5vdGlmaWNhdGlvbiB0byBGaXJlc3RvcmU6Jywgbm90aWZpY2F0aW9uRGF0YSlcblxuICAgIGNvbnN0IGRvY1JlZiA9IGF3YWl0IGFkZERvYyhjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy5ub3RpZmljYXRpb25zKSwgbm90aWZpY2F0aW9uRGF0YSlcbiAgICBjb25zb2xlLmxvZygnTm90aWZpY2F0aW9uIGFkZGVkIHN1Y2Nlc3NmdWxseSB3aXRoIElEOicsIGRvY1JlZi5pZClcblxuICAgIC8vIFZlcmlmeSB0aGUgbm90aWZpY2F0aW9uIHdhcyBhZGRlZFxuICAgIGNvbnN0IGFkZGVkRG9jID0gYXdhaXQgZ2V0RG9jKGRvY1JlZilcbiAgICBpZiAoYWRkZWREb2MuZXhpc3RzKCkpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdOb3RpZmljYXRpb24gdmVyaWZpZWQgaW4gZGF0YWJhc2U6JywgYWRkZWREb2MuZGF0YSgpKVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLndhcm4oJ05vdGlmaWNhdGlvbiBub3QgZm91bmQgYWZ0ZXIgYWRkaW5nJylcbiAgICB9XG5cbiAgICByZXR1cm4gZG9jUmVmLmlkXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIG5vdGlmaWNhdGlvbjonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIEdldCBub3RpZmljYXRpb25zIGZvciBhIHVzZXJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRVc2VyTm90aWZpY2F0aW9ucyh1c2VySWQ6IHN0cmluZywgbGltaXRDb3VudCA9IDIwKSB7XG4gIHRyeSB7XG4gICAgaWYgKCF1c2VySWQgfHwgdHlwZW9mIHVzZXJJZCAhPT0gJ3N0cmluZycpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ludmFsaWQgdXNlcklkIHByb3ZpZGVkIHRvIGdldFVzZXJOb3RpZmljYXRpb25zOicsIHVzZXJJZClcbiAgICAgIHJldHVybiBbXVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGBMb2FkaW5nIG5vdGlmaWNhdGlvbnMgZm9yIHVzZXI6ICR7dXNlcklkfWApXG5cbiAgICAvLyBUcnkgdG8gZ2V0IG5vdGlmaWNhdGlvbnMgd2l0aCBmYWxsYmFjayBmb3IgaW5kZXhpbmcgaXNzdWVzXG4gICAgbGV0IGFsbFVzZXJzU25hcHNob3QsIHNwZWNpZmljVXNlclNuYXBzaG90XG5cbiAgICB0cnkge1xuICAgICAgLy8gR2V0IG5vdGlmaWNhdGlvbnMgdGFyZ2V0ZWQgdG8gYWxsIHVzZXJzXG4gICAgICBjb25zdCBhbGxVc2Vyc1F1ZXJ5ID0gcXVlcnkoXG4gICAgICAgIGNvbGxlY3Rpb24oZGIsIENPTExFQ1RJT05TLm5vdGlmaWNhdGlvbnMpLFxuICAgICAgICB3aGVyZSgndGFyZ2V0VXNlcnMnLCAnPT0nLCAnYWxsJyksXG4gICAgICAgIG9yZGVyQnkoJ2NyZWF0ZWRBdCcsICdkZXNjJyksXG4gICAgICAgIGxpbWl0KGxpbWl0Q291bnQpXG4gICAgICApXG5cbiAgICAgIGFsbFVzZXJzU25hcHNob3QgPSBhd2FpdCBnZXREb2NzKGFsbFVzZXJzUXVlcnkpXG4gICAgICBjb25zb2xlLmxvZyhgRm91bmQgJHthbGxVc2Vyc1NuYXBzaG90LmRvY3MubGVuZ3RofSBub3RpZmljYXRpb25zIGZvciBhbGwgdXNlcnNgKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ0Vycm9yIHF1ZXJ5aW5nIGFsbCB1c2VycyBub3RpZmljYXRpb25zLCB0cnlpbmcgd2l0aG91dCBvcmRlckJ5OicsIGVycm9yKVxuICAgICAgLy8gRmFsbGJhY2sgd2l0aG91dCBvcmRlckJ5IGlmIGluZGV4IGlzIG5vdCByZWFkeVxuICAgICAgY29uc3QgYWxsVXNlcnNRdWVyeSA9IHF1ZXJ5KFxuICAgICAgICBjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy5ub3RpZmljYXRpb25zKSxcbiAgICAgICAgd2hlcmUoJ3RhcmdldFVzZXJzJywgJz09JywgJ2FsbCcpLFxuICAgICAgICBsaW1pdChsaW1pdENvdW50KVxuICAgICAgKVxuICAgICAgYWxsVXNlcnNTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MoYWxsVXNlcnNRdWVyeSlcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gR2V0IG5vdGlmaWNhdGlvbnMgdGFyZ2V0ZWQgdG8gc3BlY2lmaWMgdXNlclxuICAgICAgY29uc3Qgc3BlY2lmaWNVc2VyUXVlcnkgPSBxdWVyeShcbiAgICAgICAgY29sbGVjdGlvbihkYiwgQ09MTEVDVElPTlMubm90aWZpY2F0aW9ucyksXG4gICAgICAgIHdoZXJlKCd0YXJnZXRVc2VycycsICc9PScsICdzcGVjaWZpYycpLFxuICAgICAgICB3aGVyZSgndXNlcklkcycsICdhcnJheS1jb250YWlucycsIHVzZXJJZCksXG4gICAgICAgIG9yZGVyQnkoJ2NyZWF0ZWRBdCcsICdkZXNjJyksXG4gICAgICAgIGxpbWl0KGxpbWl0Q291bnQpXG4gICAgICApXG5cbiAgICAgIHNwZWNpZmljVXNlclNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhzcGVjaWZpY1VzZXJRdWVyeSlcbiAgICAgIGNvbnNvbGUubG9nKGBGb3VuZCAke3NwZWNpZmljVXNlclNuYXBzaG90LmRvY3MubGVuZ3RofSBub3RpZmljYXRpb25zIGZvciBzcGVjaWZpYyB1c2VyYClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdFcnJvciBxdWVyeWluZyBzcGVjaWZpYyB1c2VyIG5vdGlmaWNhdGlvbnMsIHRyeWluZyB3aXRob3V0IG9yZGVyQnk6JywgZXJyb3IpXG4gICAgICAvLyBGYWxsYmFjayB3aXRob3V0IG9yZGVyQnkgaWYgaW5kZXggaXMgbm90IHJlYWR5XG4gICAgICBjb25zdCBzcGVjaWZpY1VzZXJRdWVyeSA9IHF1ZXJ5KFxuICAgICAgICBjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy5ub3RpZmljYXRpb25zKSxcbiAgICAgICAgd2hlcmUoJ3RhcmdldFVzZXJzJywgJz09JywgJ3NwZWNpZmljJyksXG4gICAgICAgIHdoZXJlKCd1c2VySWRzJywgJ2FycmF5LWNvbnRhaW5zJywgdXNlcklkKSxcbiAgICAgICAgbGltaXQobGltaXRDb3VudClcbiAgICAgIClcbiAgICAgIHNwZWNpZmljVXNlclNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhzcGVjaWZpY1VzZXJRdWVyeSlcbiAgICB9XG5cbiAgICBjb25zdCBub3RpZmljYXRpb25zOiBOb3RpZmljYXRpb25bXSA9IFtdXG5cbiAgICAvLyBQcm9jZXNzIGFsbCB1c2VycyBub3RpZmljYXRpb25zXG4gICAgYWxsVXNlcnNTbmFwc2hvdC5kb2NzLmZvckVhY2goZG9jID0+IHtcbiAgICAgIG5vdGlmaWNhdGlvbnMucHVzaCh7XG4gICAgICAgIGlkOiBkb2MuaWQsXG4gICAgICAgIC4uLmRvYy5kYXRhKCksXG4gICAgICAgIGNyZWF0ZWRBdDogZG9jLmRhdGEoKS5jcmVhdGVkQXQ/LnRvRGF0ZSgpIHx8IG5ldyBEYXRlKClcbiAgICAgIH0gYXMgTm90aWZpY2F0aW9uKVxuICAgIH0pXG5cbiAgICAvLyBQcm9jZXNzIHNwZWNpZmljIHVzZXIgbm90aWZpY2F0aW9uc1xuICAgIHNwZWNpZmljVXNlclNuYXBzaG90LmRvY3MuZm9yRWFjaChkb2MgPT4ge1xuICAgICAgbm90aWZpY2F0aW9ucy5wdXNoKHtcbiAgICAgICAgaWQ6IGRvYy5pZCxcbiAgICAgICAgLi4uZG9jLmRhdGEoKSxcbiAgICAgICAgY3JlYXRlZEF0OiBkb2MuZGF0YSgpLmNyZWF0ZWRBdD8udG9EYXRlKCkgfHwgbmV3IERhdGUoKVxuICAgICAgfSBhcyBOb3RpZmljYXRpb24pXG4gICAgfSlcblxuICAgIC8vIFNvcnQgYnkgY3JlYXRpb24gZGF0ZSAobmV3ZXN0IGZpcnN0KVxuICAgIG5vdGlmaWNhdGlvbnMuc29ydCgoYSwgYikgPT4gYi5jcmVhdGVkQXQuZ2V0VGltZSgpIC0gYS5jcmVhdGVkQXQuZ2V0VGltZSgpKVxuXG4gICAgY29uc3QgZmluYWxOb3RpZmljYXRpb25zID0gbm90aWZpY2F0aW9ucy5zbGljZSgwLCBsaW1pdENvdW50KVxuICAgIGNvbnNvbGUubG9nKGBSZXR1cm5pbmcgJHtmaW5hbE5vdGlmaWNhdGlvbnMubGVuZ3RofSB0b3RhbCBub3RpZmljYXRpb25zIGZvciB1c2VyYClcblxuICAgIHJldHVybiBmaW5hbE5vdGlmaWNhdGlvbnNcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVzZXIgbm90aWZpY2F0aW9uczonLCBlcnJvcilcbiAgICByZXR1cm4gW11cbiAgfVxufVxuXG4vLyBHZXQgYWxsIG5vdGlmaWNhdGlvbnMgKGFkbWluIGZ1bmN0aW9uKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEFsbE5vdGlmaWNhdGlvbnMobGltaXRDb3VudCA9IDUwKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcSA9IHF1ZXJ5KFxuICAgICAgY29sbGVjdGlvbihkYiwgQ09MTEVDVElPTlMubm90aWZpY2F0aW9ucyksXG4gICAgICBvcmRlckJ5KCdjcmVhdGVkQXQnLCAnZGVzYycpLFxuICAgICAgbGltaXQobGltaXRDb3VudClcbiAgICApXG5cbiAgICBjb25zdCBxdWVyeVNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhxKVxuICAgIGNvbnN0IG5vdGlmaWNhdGlvbnMgPSBxdWVyeVNuYXBzaG90LmRvY3MubWFwKGRvYyA9PiAoe1xuICAgICAgaWQ6IGRvYy5pZCxcbiAgICAgIC4uLmRvYy5kYXRhKCksXG4gICAgICBjcmVhdGVkQXQ6IGRvYy5kYXRhKCkuY3JlYXRlZEF0Py50b0RhdGUoKSB8fCBuZXcgRGF0ZSgpXG4gICAgfSkpIGFzIE5vdGlmaWNhdGlvbltdXG5cbiAgICByZXR1cm4gbm90aWZpY2F0aW9uc1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgYWxsIG5vdGlmaWNhdGlvbnM6JywgZXJyb3IpXG4gICAgcmV0dXJuIFtdXG4gIH1cbn1cblxuLy8gRGVsZXRlIG5vdGlmaWNhdGlvbiAoYWRtaW4gZnVuY3Rpb24pXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVsZXRlTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbklkOiBzdHJpbmcpIHtcbiAgdHJ5IHtcbiAgICBpZiAoIW5vdGlmaWNhdGlvbklkIHx8IHR5cGVvZiBub3RpZmljYXRpb25JZCAhPT0gJ3N0cmluZycpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBub3RpZmljYXRpb24gSUQgcHJvdmlkZWQnKVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdEZWxldGluZyBub3RpZmljYXRpb246Jywgbm90aWZpY2F0aW9uSWQpXG4gICAgYXdhaXQgZGVsZXRlRG9jKGRvYyhkYiwgQ09MTEVDVElPTlMubm90aWZpY2F0aW9ucywgbm90aWZpY2F0aW9uSWQpKVxuICAgIGNvbnNvbGUubG9nKCdOb3RpZmljYXRpb24gZGVsZXRlZCBzdWNjZXNzZnVsbHknKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIG5vdGlmaWNhdGlvbjonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIE1hcmsgbm90aWZpY2F0aW9uIGFzIHJlYWRcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBtYXJrTm90aWZpY2F0aW9uQXNSZWFkKG5vdGlmaWNhdGlvbklkOiBzdHJpbmcsIHVzZXJJZDogc3RyaW5nKSB7XG4gIHRyeSB7XG4gICAgLy8gRm9yIG5vdywgd2UnbGwgc3RvcmUgcmVhZCBzdGF0dXMgaW4gbG9jYWxTdG9yYWdlIHNpbmNlIGl0J3MgdXNlci1zcGVjaWZpY1xuICAgIGNvbnN0IHJlYWROb3RpZmljYXRpb25zID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbShgcmVhZF9ub3RpZmljYXRpb25zXyR7dXNlcklkfWApIHx8ICdbXScpXG4gICAgaWYgKCFyZWFkTm90aWZpY2F0aW9ucy5pbmNsdWRlcyhub3RpZmljYXRpb25JZCkpIHtcbiAgICAgIHJlYWROb3RpZmljYXRpb25zLnB1c2gobm90aWZpY2F0aW9uSWQpXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShgcmVhZF9ub3RpZmljYXRpb25zXyR7dXNlcklkfWAsIEpTT04uc3RyaW5naWZ5KHJlYWROb3RpZmljYXRpb25zKSlcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgbWFya2luZyBub3RpZmljYXRpb24gYXMgcmVhZDonLCBlcnJvcilcbiAgfVxufVxuXG4vLyBDaGVjayBpZiBub3RpZmljYXRpb24gaXMgcmVhZFxuZXhwb3J0IGZ1bmN0aW9uIGlzTm90aWZpY2F0aW9uUmVhZChub3RpZmljYXRpb25JZDogc3RyaW5nLCB1c2VySWQ6IHN0cmluZyk6IGJvb2xlYW4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlYWROb3RpZmljYXRpb25zID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbShgcmVhZF9ub3RpZmljYXRpb25zXyR7dXNlcklkfWApIHx8ICdbXScpXG4gICAgcmV0dXJuIHJlYWROb3RpZmljYXRpb25zLmluY2x1ZGVzKG5vdGlmaWNhdGlvbklkKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIG5vdGlmaWNhdGlvbiByZWFkIHN0YXR1czonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG4vLyBHZXQgdW5yZWFkIG5vdGlmaWNhdGlvbiBjb3VudFxuZXhwb3J0IGZ1bmN0aW9uIGdldFVucmVhZE5vdGlmaWNhdGlvbkNvdW50KG5vdGlmaWNhdGlvbnM6IE5vdGlmaWNhdGlvbltdLCB1c2VySWQ6IHN0cmluZyk6IG51bWJlciB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVhZE5vdGlmaWNhdGlvbnMgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKGByZWFkX25vdGlmaWNhdGlvbnNfJHt1c2VySWR9YCkgfHwgJ1tdJylcbiAgICByZXR1cm4gbm90aWZpY2F0aW9ucy5maWx0ZXIobm90aWZpY2F0aW9uID0+ICFyZWFkTm90aWZpY2F0aW9ucy5pbmNsdWRlcyhub3RpZmljYXRpb24uaWQpKS5sZW5ndGhcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVucmVhZCBub3RpZmljYXRpb24gY291bnQ6JywgZXJyb3IpXG4gICAgcmV0dXJuIDBcbiAgfVxufVxuXG4vLyBHZXQgdW5yZWFkIG5vdGlmaWNhdGlvbnMgLSBBbGwgbm90aWZpY2F0aW9ucyBhcmUgbm93IGJsb2NraW5nXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VW5yZWFkTm90aWZpY2F0aW9ucyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8Tm90aWZpY2F0aW9uW10+IHtcbiAgdHJ5IHtcbiAgICBpZiAoIXVzZXJJZCB8fCB0eXBlb2YgdXNlcklkICE9PSAnc3RyaW5nJykge1xuICAgICAgY29uc29sZS5lcnJvcignSW52YWxpZCB1c2VySWQgcHJvdmlkZWQgdG8gZ2V0VW5yZWFkTm90aWZpY2F0aW9uczonLCB1c2VySWQpXG4gICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhgTG9hZGluZyB1bnJlYWQgbm90aWZpY2F0aW9ucyBmb3IgdXNlcjogJHt1c2VySWR9YClcblxuICAgIC8vIEdldCBhbGwgbm90aWZpY2F0aW9ucyBmb3IgdGhlIHVzZXJcbiAgICBjb25zdCBhbGxOb3RpZmljYXRpb25zID0gYXdhaXQgZ2V0VXNlck5vdGlmaWNhdGlvbnModXNlcklkLCA1MClcblxuICAgIC8vIEZpbHRlciBmb3IgdW5yZWFkIG5vdGlmaWNhdGlvbnNcbiAgICBjb25zdCByZWFkTm90aWZpY2F0aW9ucyA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oYHJlYWRfbm90aWZpY2F0aW9uc18ke3VzZXJJZH1gKSB8fCAnW10nKVxuICAgIGNvbnN0IHVucmVhZE5vdGlmaWNhdGlvbnMgPSBhbGxOb3RpZmljYXRpb25zLmZpbHRlcihub3RpZmljYXRpb24gPT5cbiAgICAgIG5vdGlmaWNhdGlvbi5pZCAmJlxuICAgICAgIXJlYWROb3RpZmljYXRpb25zLmluY2x1ZGVzKG5vdGlmaWNhdGlvbi5pZClcbiAgICApXG5cbiAgICBjb25zb2xlLmxvZyhgRm91bmQgJHt1bnJlYWROb3RpZmljYXRpb25zLmxlbmd0aH0gdW5yZWFkIG5vdGlmaWNhdGlvbnNgKVxuICAgIHJldHVybiB1bnJlYWROb3RpZmljYXRpb25zXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyB1bnJlYWQgbm90aWZpY2F0aW9uczonLCBlcnJvcilcbiAgICByZXR1cm4gW11cbiAgfVxufVxuXG4vLyBDaGVjayBpZiB1c2VyIGhhcyB1bnJlYWQgbm90aWZpY2F0aW9uc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGhhc1VucmVhZE5vdGlmaWNhdGlvbnModXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB1bnJlYWROb3RpZmljYXRpb25zID0gYXdhaXQgZ2V0VW5yZWFkTm90aWZpY2F0aW9ucyh1c2VySWQpXG4gICAgcmV0dXJuIHVucmVhZE5vdGlmaWNhdGlvbnMubGVuZ3RoID4gMFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIGZvciB1bnJlYWQgbm90aWZpY2F0aW9uczonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG4vLyBDaGVjayBpZiB1c2VyIGhhcyBwZW5kaW5nIHdpdGhkcmF3YWxzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gaGFzUGVuZGluZ1dpdGhkcmF3YWxzKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcSA9IHF1ZXJ5KFxuICAgICAgY29sbGVjdGlvbihkYiwgQ09MTEVDVElPTlMud2l0aGRyYXdhbHMpLFxuICAgICAgd2hlcmUoJ3VzZXJJZCcsICc9PScsIHVzZXJJZCksXG4gICAgICB3aGVyZSgnc3RhdHVzJywgJz09JywgJ3BlbmRpbmcnKSxcbiAgICAgIGxpbWl0KDEpXG4gICAgKVxuXG4gICAgY29uc3Qgc25hcHNob3QgPSBhd2FpdCBnZXREb2NzKHEpXG4gICAgcmV0dXJuICFzbmFwc2hvdC5lbXB0eVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIHBlbmRpbmcgd2l0aGRyYXdhbHM6JywgZXJyb3IpXG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbn1cblxuLy8gQ2hlY2sgaWYgd2l0aGRyYXdhbCBpcyBhbGxvd2VkICh0aW1pbmcsIGxlYXZlIHJlc3RyaWN0aW9ucywgYW5kIHBsYW4gcmVzdHJpY3Rpb25zKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNoZWNrV2l0aGRyYXdhbEFsbG93ZWQodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPHsgYWxsb3dlZDogYm9vbGVhbjsgcmVhc29uPzogc3RyaW5nIH0+IHtcbiAgdHJ5IHtcbiAgICAvLyBDaGVjayB1c2VyIHBsYW4gZmlyc3RcbiAgICBjb25zdCB1c2VyRGF0YSA9IGF3YWl0IGdldFVzZXJEYXRhKHVzZXJJZClcbiAgICBpZiAoIXVzZXJEYXRhKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBhbGxvd2VkOiBmYWxzZSxcbiAgICAgICAgcmVhc29uOiAnVW5hYmxlIHRvIHZlcmlmeSB1c2VyIGluZm9ybWF0aW9uLiBQbGVhc2UgdHJ5IGFnYWluLidcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIG9uIHRyaWFsIHBsYW5cbiAgICBpZiAodXNlckRhdGEucGxhbiA9PT0gJ1RyaWFsJykge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgYWxsb3dlZDogZmFsc2UsXG4gICAgICAgIHJlYXNvbjogJ1RyaWFsIHBsYW4gdXNlcnMgY2Fubm90IG1ha2Ugd2l0aGRyYXdhbHMuIFBsZWFzZSB1cGdyYWRlIHRvIGEgcGFpZCBwbGFuIHRvIGVuYWJsZSB3aXRoZHJhd2Fscy4nXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgcGVuZGluZyB3aXRoZHJhd2Fsc1xuICAgIGNvbnN0IGhhc1BlbmRpbmcgPSBhd2FpdCBoYXNQZW5kaW5nV2l0aGRyYXdhbHModXNlcklkKVxuICAgIGlmIChoYXNQZW5kaW5nKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBhbGxvd2VkOiBmYWxzZSxcbiAgICAgICAgcmVhc29uOiAnWW91IGhhdmUgYSBwZW5kaW5nIHdpdGhkcmF3YWwgcmVxdWVzdC4gUGxlYXNlIHdhaXQgZm9yIGl0IHRvIGJlIHByb2Nlc3NlZCBiZWZvcmUgc3VibWl0dGluZyBhIG5ldyByZXF1ZXN0LidcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXG4gICAgY29uc3QgY3VycmVudEhvdXIgPSBub3cuZ2V0SG91cnMoKVxuXG4gICAgLy8gQ2hlY2sgdGltZSByZXN0cmljdGlvbnMgKDEwIEFNIHRvIDYgUE0pXG4gICAgaWYgKGN1cnJlbnRIb3VyIDwgMTAgfHwgY3VycmVudEhvdXIgPj0gMTgpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGFsbG93ZWQ6IGZhbHNlLFxuICAgICAgICByZWFzb246ICdXaXRoZHJhd2FscyBhcmUgb25seSBhbGxvd2VkIGJldHdlZW4gMTA6MDAgQU0gdG8gNjowMCBQTSdcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBDaGVjayBhZG1pbiBsZWF2ZSBkYXlcbiAgICBjb25zdCB7IGlzQWRtaW5MZWF2ZURheSB9ID0gYXdhaXQgaW1wb3J0KCcuL2xlYXZlU2VydmljZScpXG4gICAgY29uc3QgaXNBZG1pbkxlYXZlID0gYXdhaXQgaXNBZG1pbkxlYXZlRGF5KG5vdylcbiAgICBpZiAoaXNBZG1pbkxlYXZlKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBhbGxvd2VkOiBmYWxzZSxcbiAgICAgICAgcmVhc29uOiAnV2l0aGRyYXdhbHMgYXJlIG5vdCBhbGxvd2VkIG9uIGFkbWluIGxlYXZlL2hvbGlkYXkgZGF5cydcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBDaGVjayB1c2VyIGxlYXZlIGRheVxuICAgIGNvbnN0IHsgaXNVc2VyT25MZWF2ZSB9ID0gYXdhaXQgaW1wb3J0KCcuL2xlYXZlU2VydmljZScpXG4gICAgY29uc3QgaXNVc2VyTGVhdmUgPSBhd2FpdCBpc1VzZXJPbkxlYXZlKHVzZXJJZCwgbm93KVxuICAgIGlmIChpc1VzZXJMZWF2ZSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgYWxsb3dlZDogZmFsc2UsXG4gICAgICAgIHJlYXNvbjogJ1dpdGhkcmF3YWxzIGFyZSBub3QgYWxsb3dlZCBvbiB5b3VyIGxlYXZlIGRheXMnXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgYWxsb3dlZDogdHJ1ZSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgd2l0aGRyYXdhbCBhbGxvd2VkOicsIGVycm9yKVxuICAgIHJldHVybiB7IGFsbG93ZWQ6IGZhbHNlLCByZWFzb246ICdVbmFibGUgdG8gdmVyaWZ5IHdpdGhkcmF3YWwgZWxpZ2liaWxpdHkuIFBsZWFzZSB0cnkgYWdhaW4uJyB9XG4gIH1cbn1cblxuLy8gQ3JlYXRlIHdpdGhkcmF3YWwgcmVxdWVzdFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZVdpdGhkcmF3YWxSZXF1ZXN0KHVzZXJJZDogc3RyaW5nLCBhbW91bnQ6IG51bWJlciwgYmFua0RldGFpbHM6IGFueSkge1xuICB0cnkge1xuICAgIC8vIENoZWNrIG1pbmltdW0gd2l0aGRyYXdhbCBhbW91bnRcbiAgICBpZiAoYW1vdW50IDwgNTApIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTWluaW11bSB3aXRoZHJhd2FsIGFtb3VudCBpcyDigrk1MCcpXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgd2l0aGRyYXdhbCBpcyBhbGxvd2VkXG4gICAgY29uc3Qgd2l0aGRyYXdhbENoZWNrID0gYXdhaXQgY2hlY2tXaXRoZHJhd2FsQWxsb3dlZCh1c2VySWQpXG4gICAgaWYgKCF3aXRoZHJhd2FsQ2hlY2suYWxsb3dlZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKHdpdGhkcmF3YWxDaGVjay5yZWFzb24pXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgc3VmZmljaWVudCBiYWxhbmNlXG4gICAgY29uc3Qgd2FsbGV0RGF0YSA9IGF3YWl0IGdldFdhbGxldERhdGEodXNlcklkKVxuICAgIGlmICh3YWxsZXREYXRhLndhbGxldCA8IGFtb3VudCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnN1ZmZpY2llbnQgd2FsbGV0IGJhbGFuY2UnKVxuICAgIH1cblxuICAgIC8vIERlYml0IHRoZSBhbW91bnQgZnJvbSB1c2VyJ3Mgd2FsbGV0IGltbWVkaWF0ZWx5XG4gICAgYXdhaXQgdXBkYXRlV2FsbGV0QmFsYW5jZSh1c2VySWQsIC1hbW91bnQpXG5cbiAgICAvLyBBZGQgdHJhbnNhY3Rpb24gcmVjb3JkIGZvciB3aXRoZHJhd2FsIGRlYml0XG4gICAgYXdhaXQgYWRkVHJhbnNhY3Rpb24odXNlcklkLCB7XG4gICAgICB0eXBlOiAnd2l0aGRyYXdhbF9yZXF1ZXN0JyxcbiAgICAgIGFtb3VudDogLWFtb3VudCxcbiAgICAgIGRlc2NyaXB0aW9uOiBgV2l0aGRyYXdhbCByZXF1ZXN0IHN1Ym1pdHRlZCAtIOKCuSR7YW1vdW50fSBkZWJpdGVkIGZyb20gd2FsbGV0YFxuICAgIH0pXG5cbiAgICBjb25zdCB3aXRoZHJhd2FsRGF0YSA9IHtcbiAgICAgIHVzZXJJZCxcbiAgICAgIGFtb3VudCxcbiAgICAgIGJhbmtEZXRhaWxzLFxuICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICBkYXRlOiBUaW1lc3RhbXAubm93KCksXG4gICAgICBjcmVhdGVkQXQ6IFRpbWVzdGFtcC5ub3coKVxuICAgIH1cblxuICAgIGNvbnN0IGRvY1JlZiA9IGF3YWl0IGFkZERvYyhjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy53aXRoZHJhd2FscyksIHdpdGhkcmF3YWxEYXRhKVxuXG4gICAgcmV0dXJuIGRvY1JlZi5pZFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHdpdGhkcmF3YWwgcmVxdWVzdDonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vIEdldCB1c2VyIHdpdGhkcmF3YWxzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlcldpdGhkcmF3YWxzKHVzZXJJZDogc3RyaW5nLCBsaW1pdENvdW50ID0gMjApIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBxID0gcXVlcnkoXG4gICAgICBjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy53aXRoZHJhd2FscyksXG4gICAgICB3aGVyZSgndXNlcklkJywgJz09JywgdXNlcklkKSxcbiAgICAgIG9yZGVyQnkoJ2RhdGUnLCAnZGVzYycpLFxuICAgICAgbGltaXQobGltaXRDb3VudClcbiAgICApXG5cbiAgICBjb25zdCBzbmFwc2hvdCA9IGF3YWl0IGdldERvY3MocSlcbiAgICByZXR1cm4gc25hcHNob3QuZG9jcy5tYXAoZG9jID0+ICh7XG4gICAgICBpZDogZG9jLmlkLFxuICAgICAgLi4uZG9jLmRhdGEoKSxcbiAgICAgIGRhdGU6IGRvYy5kYXRhKCkuZGF0ZT8udG9EYXRlKClcbiAgICB9KSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVzZXIgd2l0aGRyYXdhbHM6JywgZXJyb3IpXG4gICAgcmV0dXJuIFtdXG4gIH1cbn1cblxuLy8gR2VuZXJhdGUgdW5pcXVlIHJlZmVycmFsIGNvZGUgd2l0aCBUTiBwcmVmaXggYW5kIHNlcXVlbnRpYWwgbnVtYmVyaW5nXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVVbmlxdWVSZWZlcnJhbENvZGUoKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgdHJ5IHtcbiAgICAvLyBUcnkgdG8gZ2V0IGNvdW50IGZyb20gc2VydmVyIGZvciBzZXF1ZW50aWFsIG51bWJlcmluZ1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2Vyc0NvbGxlY3Rpb24gPSBjb2xsZWN0aW9uKGRiLCBDT0xMRUNUSU9OUy51c2VycylcbiAgICAgIGNvbnN0IHNuYXBzaG90ID0gYXdhaXQgZ2V0Q291bnRGcm9tU2VydmVyKHVzZXJzQ29sbGVjdGlvbilcbiAgICAgIGNvbnN0IGNvdW50ID0gc25hcHNob3QuZGF0YSgpLmNvdW50XG4gICAgICBjb25zdCBzZXF1ZW50aWFsTnVtYmVyID0gKGNvdW50ICsgMSkudG9TdHJpbmcoKS5wYWRTdGFydCg0LCAnMCcpXG4gICAgICByZXR1cm4gYFROJHtzZXF1ZW50aWFsTnVtYmVyfWBcbiAgICB9IGNhdGNoIChjb3VudEVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBnZXQgY291bnQgZnJvbSBzZXJ2ZXIsIHVzaW5nIGZhbGxiYWNrIG1ldGhvZDonLCBjb3VudEVycm9yKVxuICAgICAgLy8gRmFsbGJhY2sgdG8gdGltZXN0YW1wLWJhc2VkIGdlbmVyYXRpb25cbiAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCkudG9TdHJpbmcoKS5zbGljZSgtNClcbiAgICAgIGNvbnN0IHJhbmRvbVBhcnQgPSBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgNCkudG9VcHBlckNhc2UoKVxuICAgICAgcmV0dXJuIGBUTiR7dGltZXN0YW1wfSR7cmFuZG9tUGFydH1gXG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgdW5pcXVlIHJlZmVycmFsIGNvZGU6JywgZXJyb3IpXG4gICAgLy8gRmluYWwgZmFsbGJhY2tcbiAgICBjb25zdCB0aW1lc3RhbXAgPSBEYXRlLm5vdygpLnRvU3RyaW5nKCkuc2xpY2UoLTQpXG4gICAgcmV0dXJuIGBUTiR7dGltZXN0YW1wfWBcbiAgfVxufVxuXG4vLyBHZW5lcmF0ZSBzZXF1ZW50aWFsIHJlZmVycmFsIGNvZGUgKGFsaWFzIGZvciBnZW5lcmF0ZVVuaXF1ZVJlZmVycmFsQ29kZSlcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZVNlcXVlbnRpYWxSZWZlcnJhbENvZGUoKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgcmV0dXJuIGdlbmVyYXRlVW5pcXVlUmVmZXJyYWxDb2RlKClcbn1cblxuXG4iXSwibmFtZXMiOlsiZG9jIiwiZ2V0RG9jIiwidXBkYXRlRG9jIiwiZGVsZXRlRG9jIiwiY29sbGVjdGlvbiIsInF1ZXJ5Iiwid2hlcmUiLCJvcmRlckJ5IiwibGltaXQiLCJnZXREb2NzIiwiYWRkRG9jIiwiVGltZXN0YW1wIiwiaW5jcmVtZW50IiwiZ2V0Q291bnRGcm9tU2VydmVyIiwiZGIiLCJGSUVMRF9OQU1FUyIsIm5hbWUiLCJlbWFpbCIsIm1vYmlsZSIsInJlZmVycmFsQ29kZSIsInJlZmVycmVkQnkiLCJyZWZlcnJhbEJvbnVzQ3JlZGl0ZWQiLCJwbGFuIiwicGxhbkV4cGlyeSIsImFjdGl2ZURheXMiLCJsYXN0QWN0aXZlRGF5VXBkYXRlIiwiam9pbmVkRGF0ZSIsIndhbGxldCIsImJhbmtBY2NvdW50SG9sZGVyTmFtZSIsImJhbmtBY2NvdW50TnVtYmVyIiwiYmFua0lmc2NDb2RlIiwiYmFua05hbWUiLCJiYW5rRGV0YWlsc1VwZGF0ZWQiLCJ0b3RhbFRyYW5zbGF0aW9ucyIsInRvZGF5VHJhbnNsYXRpb25zIiwibGFzdFRyYW5zbGF0aW9uRGF0ZSIsInRyYW5zbGF0aW9uRHVyYXRpb24iLCJxdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlIiwicXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZUV4cGlyeSIsInF1aWNrVHJhbnNsYXRpb25BZHZhbnRhZ2VEYXlzIiwicXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZVNlY29uZHMiLCJxdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlR3JhbnRlZEJ5IiwicXVpY2tUcmFuc2xhdGlvbkFkdmFudGFnZUdyYW50ZWRBdCIsInR5cGUiLCJhbW91bnQiLCJkYXRlIiwic3RhdHVzIiwiZGVzY3JpcHRpb24iLCJ1c2VySWQiLCJDT0xMRUNUSU9OUyIsInVzZXJzIiwidHJhbnNhY3Rpb25zIiwid2l0aGRyYXdhbHMiLCJwbGFucyIsInNldHRpbmdzIiwibm90aWZpY2F0aW9ucyIsImFkbWluTGVhdmVzIiwidXNlckxlYXZlcyIsImdldFVzZXJEYXRhIiwiY29uc29sZSIsImVycm9yIiwidXNlckRvYyIsImV4aXN0cyIsImRhdGEiLCJyZXN1bHQiLCJTdHJpbmciLCJCb29sZWFuIiwidG9EYXRlIiwiTnVtYmVyIiwiRGF0ZSIsImxvZyIsImdldFdhbGxldERhdGEiLCJnZXRWaWRlb0NvdW50RGF0YSIsInVzZXJSZWYiLCJ0b2RheSIsImlzTmV3RGF5IiwidG9EYXRlU3RyaW5nIiwidXBkYXRlVXNlckFjdGl2ZURheXMiLCJyZW1haW5pbmdUcmFuc2xhdGlvbnMiLCJNYXRoIiwibWF4IiwidXBkYXRlVXNlckRhdGEiLCJhZGRUcmFuc2FjdGlvbiIsInRyYW5zYWN0aW9uRGF0YSIsInRyYW5zYWN0aW9uIiwibm93IiwiZ2V0VHJhbnNhY3Rpb25zIiwibGltaXRDb3VudCIsInEiLCJxdWVyeVNuYXBzaG90IiwiZG9jcyIsIm1hcCIsImlkIiwic29ydCIsImEiLCJiIiwiZGF0ZUEiLCJkYXRlQiIsImdldFRpbWUiLCJnZXRSZWZlcnJhbHMiLCJ1cGRhdGVWaWRlb0NvdW50IiwiY3VycmVudFRvZGF5VHJhbnNsYXRpb25zIiwiZnJvbURhdGUiLCJyZXNldERhaWx5VmlkZW9Db3VudCIsImNlbnRyYWxpemVkVXBkYXRlIiwiZml4QWxsVXNlcnNBY3RpdmVEYXlzIiwidXNlcnNTbmFwc2hvdCIsImZpeGVkQ291bnQiLCJlcnJvckNvdW50IiwidXBkYXRlV2FsbGV0QmFsYW5jZSIsInNhdmVCYW5rRGV0YWlscyIsImJhbmtEZXRhaWxzIiwiRXJyb3IiLCJ2YWxpZGF0ZUJhbmtEZXRhaWxzIiwiYWNjb3VudEhvbGRlck5hbWUiLCJ0cmltIiwiYWNjb3VudE51bWJlciIsImlmc2NDb2RlIiwidG9VcHBlckNhc2UiLCJnZXRCYW5rRGV0YWlscyIsImdldFBsYW5FYXJuaW5nIiwicGxhbkVhcm5pbmdzIiwiY2hlY2tDb3B5UGFzdGVQZXJtaXNzaW9uIiwiY2VudHJhbGl6ZWRDaGVjayIsImhhc1Blcm1pc3Npb24iLCJkYXlzUmVtYWluaW5nIiwiZ2V0UGxhblZpZGVvRHVyYXRpb24iLCJwbGFuRHVyYXRpb25zIiwiZ2V0UGxhblZhbGlkaXR5RGF5cyIsInBsYW5WYWxpZGl0eURheXMiLCJpc1VzZXJQbGFuRXhwaXJlZCIsInVzZXJEYXRhIiwiZXhwaXJlZCIsInJlYXNvbiIsImRheXNTaW5jZUpvaW5pbmciLCJmbG9vciIsInRyaWFsRGF5c0xlZnQiLCJ1bmRlZmluZWQiLCJkYXlzTGVmdCIsImNlaWwiLCJjdXJyZW50QWN0aXZlRGF5cyIsInVwZGF0ZVVzZXJQbGFuRXhwaXJ5IiwibmV3UGxhbiIsImN1c3RvbUV4cGlyeURhdGUiLCJleHBpcnlEYXRlIiwidmFsaWRpdHlEYXlzIiwiZ2V0UmVmZXJyYWxCb251cyIsInJlZmVycmFsQm9udXNlcyIsInByb2Nlc3NSZWZlcnJhbEJvbnVzIiwib2xkUGxhbiIsImFscmVhZHlDcmVkaXRlZCIsImVtcHR5IiwicmVmZXJyZXJEb2MiLCJyZWZlcnJlcklkIiwicmVmZXJyZXJEYXRhIiwiYm9udXNBbW91bnQiLCJyZWZlcnJlclJlZiIsIk9iamVjdCIsImtleXMiLCJtYW51YWxQcm9jZXNzUmVmZXJyYWxCb251cyIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiZ2V0VXNlclZpZGVvU2V0dGluZ3MiLCJlYXJuaW5nUGVyQmF0Y2giLCJoYXNRdWlja0FkdmFudGFnZSIsImhhc0FjdGl2ZVF1aWNrQWR2YW50YWdlIiwiY2hlY2tRdWlja1RyYW5zbGF0aW9uQWR2YW50YWdlQWN0aXZlIiwicXVpY2tBZHZhbnRhZ2VFeHBpcnkiLCJleHBpcnkiLCJncmFudFF1aWNrVmlkZW9BZHZhbnRhZ2UiLCJkYXlzIiwiZ3JhbnRlZEJ5Iiwic2Vjb25kcyIsImdyYW50Q29weVBhc3RlUGVybWlzc2lvbiIsInJlbW92ZVF1aWNrVmlkZW9BZHZhbnRhZ2UiLCJyZW1vdmVkQnkiLCJyZW1vdmVDb3B5UGFzdGVQZXJtaXNzaW9uIiwidXBkYXRlVXNlclZpZGVvRHVyYXRpb24iLCJkdXJhdGlvbkluU2Vjb25kcyIsImlzUXVpY2tEdXJhdGlvbiIsImluY2x1ZGVzIiwiaXNTdGFuZGFyZER1cmF0aW9uIiwibGVuZ3RoIiwidGVzdCIsImFkZE5vdGlmaWNhdGlvbiIsIm5vdGlmaWNhdGlvbiIsIm5vdGlmaWNhdGlvbkRhdGEiLCJ0aXRsZSIsInRhcmdldFVzZXJzIiwidXNlcklkcyIsImNyZWF0ZWRBdCIsImNyZWF0ZWRCeSIsImRvY1JlZiIsImFkZGVkRG9jIiwid2FybiIsImdldFVzZXJOb3RpZmljYXRpb25zIiwiYWxsVXNlcnNTbmFwc2hvdCIsInNwZWNpZmljVXNlclNuYXBzaG90IiwiYWxsVXNlcnNRdWVyeSIsInNwZWNpZmljVXNlclF1ZXJ5IiwiZm9yRWFjaCIsInB1c2giLCJmaW5hbE5vdGlmaWNhdGlvbnMiLCJzbGljZSIsImdldEFsbE5vdGlmaWNhdGlvbnMiLCJkZWxldGVOb3RpZmljYXRpb24iLCJub3RpZmljYXRpb25JZCIsIm1hcmtOb3RpZmljYXRpb25Bc1JlYWQiLCJyZWFkTm90aWZpY2F0aW9ucyIsIkpTT04iLCJwYXJzZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiaXNOb3RpZmljYXRpb25SZWFkIiwiZ2V0VW5yZWFkTm90aWZpY2F0aW9uQ291bnQiLCJmaWx0ZXIiLCJnZXRVbnJlYWROb3RpZmljYXRpb25zIiwiYWxsTm90aWZpY2F0aW9ucyIsInVucmVhZE5vdGlmaWNhdGlvbnMiLCJoYXNVbnJlYWROb3RpZmljYXRpb25zIiwiaGFzUGVuZGluZ1dpdGhkcmF3YWxzIiwic25hcHNob3QiLCJjaGVja1dpdGhkcmF3YWxBbGxvd2VkIiwiYWxsb3dlZCIsImhhc1BlbmRpbmciLCJjdXJyZW50SG91ciIsImdldEhvdXJzIiwiaXNBZG1pbkxlYXZlRGF5IiwiaXNBZG1pbkxlYXZlIiwiaXNVc2VyT25MZWF2ZSIsImlzVXNlckxlYXZlIiwiY3JlYXRlV2l0aGRyYXdhbFJlcXVlc3QiLCJ3aXRoZHJhd2FsQ2hlY2siLCJ3YWxsZXREYXRhIiwid2l0aGRyYXdhbERhdGEiLCJnZXRVc2VyV2l0aGRyYXdhbHMiLCJnZW5lcmF0ZVVuaXF1ZVJlZmVycmFsQ29kZSIsInVzZXJzQ29sbGVjdGlvbiIsImNvdW50Iiwic2VxdWVudGlhbE51bWJlciIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJjb3VudEVycm9yIiwidGltZXN0YW1wIiwicmFuZG9tUGFydCIsInJhbmRvbSIsInN1YnN0cmluZyIsImdlbmVyYXRlU2VxdWVudGlhbFJlZmVycmFsQ29kZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/dataService.ts\n");

/***/ })

};
;