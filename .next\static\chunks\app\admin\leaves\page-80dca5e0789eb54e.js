(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9451],{1577:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(5155),n=a(2115),s=a(6874),i=a.n(s),l=a(6681),o=a(3737),c=a(4752),d=a.n(c);function x(){let{user:e,loading:t,isAdmin:s}=(0,l.wC)(),[c,x]=(0,n.useState)([]),[u,m]=(0,n.useState)(!0),[g,y]=(0,n.useState)(!1),[p,f]=(0,n.useState)(!1),[h,D]=(0,n.useState)({date:"",reason:"",type:"holiday"});(0,n.useEffect)(()=>{s&&b()},[s]);let b=async()=>{try{m(!0);let{getAdminLeaves:e}=await a.e(9567).then(a.bind(a,9567)),t=await e();x(t)}catch(e){console.error("Error loading leaves:",e),x([]),d().fire({icon:"error",title:"Error",text:"Failed to load leaves. Please try again."})}finally{m(!1)}},v=async()=>{try{if(!h.date||!h.reason.trim())return void d().fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let t=new Date(h.date),r=new Date;if(r.setHours(0,0,0,0),t<r)return void d().fire({icon:"error",title:"Invalid Date",text:"Cannot create leave for past dates."});if(c.find(e=>e.date.toDateString()===t.toDateString()))return void d().fire({icon:"error",title:"Duplicate Leave",text:"Leave already exists for this date."});f(!0);let{createAdminLeave:n}=await a.e(9567).then(a.bind(a,9567));await n({date:t,reason:h.reason.trim(),type:h.type,createdBy:(null==e?void 0:e.email)||"admin"}),await b(),d().fire({icon:"success",title:"Leave Created!",text:"Admin leave created for ".concat(t.toLocaleDateString(),"."),timer:3e3,showConfirmButton:!1}),D({date:"",reason:"",type:"holiday"}),y(!1)}catch(e){console.error("Error creating leave:",e),d().fire({icon:"error",title:"Creation Failed",text:"Failed to create leave. Please try again."})}finally{f(!1)}},N=async e=>{try{if((await d().fire({title:"Delete Leave",text:"Are you sure you want to delete this leave?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Delete",cancelButtonText:"Cancel"})).isConfirmed){let{deleteAdminLeave:t}=await a.e(9567).then(a.bind(a,9567));await t(e),await b(),d().fire({icon:"success",title:"Leave Deleted",text:"Leave has been deleted successfully.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error deleting leave:",e),d().fire({icon:"error",title:"Delete Failed",text:"Failed to delete leave. Please try again."})}},j=e=>{switch(e){case"holiday":return"bg-green-100 text-green-800";case"maintenance":return"bg-blue-100 text-blue-800";case"emergency":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"holiday":return"fas fa-calendar-day text-green-500";case"maintenance":return"fas fa-tools text-blue-500";case"emergency":return"fas fa-exclamation-triangle text-red-500";default:return"fas fa-calendar text-gray-500"}};return t||u?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading leaves..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Leave Management"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-gray-700",children:["Total: ",c.length]}),(0,r.jsxs)("button",{onClick:()=>{if(0===c.length)return void d().fire({icon:"warning",title:"No Data",text:"No leaves to export."});let e=c.map(e=>({Date:e.date instanceof Date?e.date.toLocaleDateString():new Date(e.date).toLocaleDateString(),Reason:e.reason,Type:e.type.charAt(0).toUpperCase()+e.type.slice(1),"Created By":e.createdBy,"Created At":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():new Date(e.createdAt).toLocaleDateString()}));(0,o.Bf)(e,"admin-leaves"),d().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(c.length," leaves to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,r.jsxs)("button",{onClick:()=>y(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Leave"]}),(0,r.jsxs)("button",{onClick:b,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===c.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("i",{className:"fas fa-calendar-times text-gray-300 text-6xl mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No leaves scheduled"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your first admin leave to block work and withdrawals"}),(0,r.jsxs)("button",{onClick:()=>y(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Add First Leave"]})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reason"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created By"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.date.toLocaleDateString()}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.date.toLocaleDateString("en-US",{weekday:"long"})})]}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm text-gray-900 max-w-xs",children:e.reason})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(j(e.type)),children:[(0,r.jsx)("i",{className:"".concat(w(e.type)," mr-1")}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.createdBy}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.createdAt.toLocaleDateString()})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("button",{onClick:()=>N(e.id),className:"text-red-600 hover:text-red-900",children:[(0,r.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete"]})})]},e.id))})]})})})}),g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Add Admin Leave"}),(0,r.jsx)("button",{onClick:()=>y(!1),className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,r.jsx)("input",{type:"date",value:h.date,onChange:e=>D(t=>({...t,date:e.target.value})),min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,r.jsxs)("select",{value:h.type,onChange:e=>D(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"holiday",children:"Holiday"}),(0,r.jsx)("option",{value:"maintenance",children:"Maintenance"}),(0,r.jsx)("option",{value:"emergency",children:"Emergency"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,r.jsx)("textarea",{value:h.reason,onChange:e=>D(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,r.jsx)("button",{onClick:()=>y(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,r.jsx)("button",{onClick:v,disabled:p,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Create Leave"]})})]})]})})]})}},3737:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),n=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],s=new Blob(["\uFEFF"+[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";let r=n.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):r&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(s);i.setAttribute("href",e),i.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function n(e){return e.map(t=>{let a=0,r=null,n="No",s=t.quickTranslationAdvantageExpiry||t.quickVideoAdvantageExpiry;if(s)try{s instanceof Date?r=s:s.toDate&&"function"==typeof s.toDate?r=s.toDate():r=new Date(s);let i=new Date,l=r.getTime()-i.getTime();n=(a=Math.max(0,Math.ceil(l/864e5)))>0?"Yes":"No",5>e.indexOf(t)&&console.log("\uD83D\uDCCA Export debug for user ".concat(t.email,":"),{expiryField:s,expiryFieldType:typeof s,copyPasteExpiryDate:r,copyPasteRemainingDays:a,copyPastePermission:n,hasQuickTranslationAdvantageExpiry:!!t.quickTranslationAdvantageExpiry,hasQuickVideoAdvantageExpiry:!!t.quickVideoAdvantageExpiry})}catch(e){console.error("❌ Error calculating copy-paste days for user ".concat(t.email,":"),e)}else 5>e.indexOf(t)&&console.log("\uD83D\uDCCA Export debug for user ".concat(t.email,": No copy-paste expiry field found"));return{"User ID":t.id||"",Name:t.name||"",Email:t.email||"",Mobile:String(t.mobile||""),"Referral Code":t.referralCode||"","Referred By":t.referredBy||"Direct",Plan:t.plan||"","Plan Expiry":t.planExpiry instanceof Date?t.planExpiry.toLocaleDateString():t.planExpiry?new Date(t.planExpiry).toLocaleDateString():"","Active Days":t.activeDays||0,"Total Translations":t.totalTranslations||t.totalVideos||0,"Today Translations":t.todayTranslations||t.todayVideos||0,"Last Translation Date":t.lastTranslationDate instanceof Date?t.lastTranslationDate.toLocaleDateString():t.lastTranslationDate?new Date(t.lastTranslationDate).toLocaleDateString():t.lastVideoDate instanceof Date?t.lastVideoDate.toLocaleDateString():t.lastVideoDate?new Date(t.lastVideoDate).toLocaleDateString():"","Copy-Paste Permission":n,"Copy-Paste Remaining Days":a,"Copy-Paste Expiry":r?r.toLocaleDateString():"","Copy-Paste Granted By":t.quickTranslationAdvantageGrantedBy||t.quickVideoAdvantageGrantedBy||"","Copy-Paste Granted At":t.quickTranslationAdvantageGrantedAt?t.quickTranslationAdvantageGrantedAt instanceof Date?t.quickTranslationAdvantageGrantedAt.toLocaleDateString():new Date(t.quickTranslationAdvantageGrantedAt).toLocaleDateString():"","Wallet Balance":t.wallet||0,"Referral Bonus Credited":t.referralBonusCredited?"Yes":"No",Status:t.status||"","Joined Date":t.joinedDate instanceof Date?t.joinedDate.toLocaleDateString():t.joinedDate?new Date(t.joinedDate).toLocaleDateString():"","Joined Time":t.joinedDate instanceof Date?t.joinedDate.toLocaleTimeString():t.joinedDate?new Date(t.joinedDate).toLocaleTimeString():""}})}function s(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>{var t,a,r,n;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(r=e.bankDetails)?void 0:r.accountNumber)||""),"IFSC Code":(null==(n=e.bankDetails)?void 0:n.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function l(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>r,Fz:()=>n,Pe:()=>l,dB:()=>i,sL:()=>s})},4249:(e,t,a)=>{Promise.resolve().then(a.bind(a,1577))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,6681,8441,1684,7358],()=>t(4249)),_N_E=e.O()}]);