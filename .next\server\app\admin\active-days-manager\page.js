(()=>{var e={};e.id=8297,e.ids=[8297],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1775:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Instra\\\\src\\\\app\\\\admin\\\\active-days-manager\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\active-days-manager\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14488:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["active-days-manager",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1775)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\active-days-manager\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Instra\\src\\app\\admin\\active-days-manager\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/active-days-manager/page",pathname:"/admin/active-days-manager",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},42069:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(60687),i=t(43210),a=t(85814),n=t.n(a),l=t(87979),c=t(51705),d=t(27878),o=t(77567);function x(){let{user:e,loading:s,isAdmin:t}=(0,l.wC)(),[a,x]=(0,i.useState)(!1),[u,m]=(0,i.useState)(null),p=async()=>{if(e&&(await o.A.fire({icon:"warning",title:"Manual Active Days Increment",text:"This will increment active days for all eligible users. Are you sure?",showCancelButton:!0,confirmButtonText:"Yes, Proceed",cancelButtonText:"Cancel"})).isConfirmed)try{x(!0),console.log(`🔧 Manual active days increment triggered by admin: ${e.uid}`),console.log("\uD83D\uDCC5 Processing active days increment...");let s=await (0,c.mH)();console.log("\uD83D\uDCCB Processing copy-paste reduction...");let t=await (0,d.Mk)(),r={success:!0,totalUsers:s.processed,updatedUsers:s.updated,skippedUsers:0,errors:s.errors,activeDaysResult:s,copyPasteResult:t};m(r),o.A.fire({icon:"success",title:"Process Completed",html:`
          <div class="text-left">
            <p><strong>Active Days Results:</strong></p>
            <ul>
              <li>Processed: ${s.processed} users</li>
              <li>Updated: ${s.updated} users</li>
              <li>Errors: ${s.errors} users</li>
            </ul>
            <br>
            <p><strong>Copy-Paste Results:</strong></p>
            <ul>
              <li>Processed: ${t.processed} users</li>
              <li>Reduced: ${t.reduced} users</li>
              <li>Expired: ${t.expired} users</li>
              <li>Errors: ${t.errors} users</li>
            </ul>
          </div>
        `,confirmButtonText:"OK"})}catch(e){console.error("Error triggering manual increment:",e),o.A.fire({icon:"error",title:"Process Failed",text:"An error occurred while processing active days increment."})}finally{x(!1)}};if(s)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:"Loading active days manager..."})]})});let h=(()=>{let e=new Date(new Date);return e.setDate(e.getDate()+1),e.setHours(0,0,0,0),e})();return(0,r.jsxs)("div",{className:"min-h-screen p-4",children:[(0,r.jsx)("div",{className:"glass-card p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-white mb-2",children:[(0,r.jsx)("i",{className:"fas fa-calendar-plus mr-3"}),"Active Days Manager"]}),(0,r.jsx)("p",{className:"text-white/80",children:"Manage daily active days increment system"})]}),(0,r.jsxs)(n(),{href:"/admin",className:"btn-secondary",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]})]})}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-2"}),"System Status"]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-clock mr-2"}),"Next Scheduled Run"]}),(0,r.jsx)("p",{className:"text-white/80 text-lg",children:h.toLocaleString()}),(0,r.jsx)("p",{className:"text-white/60 text-sm mt-1",children:"Runs automatically at midnight (12:00 AM) daily"})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-cogs mr-2"}),"System Rules"]}),(0,r.jsxs)("ul",{className:"text-white/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Increments active days for all active users"}),(0,r.jsx)("li",{children:"• Skips admin users automatically"}),(0,r.jsx)("li",{children:"• Skips users on leave"}),(0,r.jsx)("li",{children:"• Runs once per day at midnight"})]})]})]})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-hand-pointer mr-2"}),"Manual Controls"]}),(0,r.jsx)("div",{className:"bg-yellow-500/20 rounded-lg p-4 border border-yellow-500/30 mb-4",children:(0,r.jsxs)("div",{className:"flex items-start text-yellow-300",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle mr-2 mt-1"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("p",{className:"font-semibold mb-1",children:"Warning:"}),(0,r.jsx)("p",{children:"Manual trigger will increment active days for all eligible users immediately. This should only be used for testing or emergency situations."})]})]})}),(0,r.jsx)("button",{onClick:p,disabled:a,className:"btn-primary",children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-play mr-2"}),"Manual Trigger Active Days Increment"]})})]}),u&&(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Last Execution Result"]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:u.totalUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Total Users"})]}),(0,r.jsxs)("div",{className:"bg-green-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-400",children:u.updatedUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Successfully Updated"})]}),(0,r.jsxs)("div",{className:"bg-yellow-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:u.skippedUsers}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Skipped Users"})]}),(0,r.jsxs)("div",{className:"bg-red-500/20 rounded-lg p-4 text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-400",children:u.errors}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Errors"})]})]}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${u.success?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"}`,children:u.success?"Success":"Failed"})})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mt-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-question-circle mr-2"}),"How It Works"]}),(0,r.jsxs)("div",{className:"space-y-4 text-white/80",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-white",children:"Automatic Scheduling"}),(0,r.jsx)("p",{className:"text-sm",children:"System runs automatically every day at midnight (12:00 AM)"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-white",children:"User Filtering"}),(0,r.jsx)("p",{className:"text-sm",children:"Identifies eligible users (active, non-admin, not on leave)"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-white",children:"Increment Process"}),(0,r.jsx)("p",{className:"text-sm",children:"Increases active days by 1 for each eligible user"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1",children:"4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-white",children:"Logging"}),(0,r.jsx)("p",{className:"text-sm",children:"Records all actions and results for audit purposes"})]})]})]})]})]})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53719:(e,s,t)=>{Promise.resolve().then(t.bind(t,42069))},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59295:(e,s,t)=>{Promise.resolve().then(t.bind(t,1775))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[2579,6803],()=>t(14488));module.exports=r})();