'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/useAuth'
import { grantQuickVideoAdvantage, removeQuickVideoAdvantage } from '@/lib/dataService'
import { checkCopyPastePermission } from '@/lib/copyPasteService'

export default function TestCopyPasteAdminPage() {
  const { user, loading } = useRequireAuth()
  const [userId, setUserId] = useState('')
  const [days, setDays] = useState(1)
  const [result, setResult] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [permissionStatus, setPermissionStatus] = useState<any>(null)

  const handleGrantPermission = async () => {
    if (!userId.trim()) {
      alert('Please enter a user ID')
      return
    }

    setIsProcessing(true)
    setResult(null)

    try {
      console.log(`🔧 Granting copy-paste permission to user ${userId} for ${days} days`)
      await grantQuickVideoAdvantage(userId.trim(), days, user?.email || 'admin', 30)
      
      // Check status after granting
      const status = await checkCopyPastePermission(userId.trim())
      setPermissionStatus(status)
      
      setResult({
        success: true,
        message: `Copy-paste permission granted for ${days} days`,
        action: 'granted'
      })
    } catch (error: any) {
      setResult({
        success: false,
        message: `Error: ${error?.message || 'Unknown error'}`,
        action: 'grant_failed'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRemovePermission = async () => {
    if (!userId.trim()) {
      alert('Please enter a user ID')
      return
    }

    setIsProcessing(true)
    setResult(null)

    try {
      console.log(`🔧 Removing copy-paste permission from user ${userId}`)
      await removeQuickVideoAdvantage(userId.trim(), user?.email || 'admin')
      
      // Check status after removing
      const status = await checkCopyPastePermission(userId.trim())
      setPermissionStatus(status)
      
      setResult({
        success: true,
        message: 'Copy-paste permission removed',
        action: 'removed'
      })
    } catch (error: any) {
      setResult({
        success: false,
        message: `Error: ${error?.message || 'Unknown error'}`,
        action: 'remove_failed'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCheckStatus = async () => {
    if (!userId.trim()) {
      alert('Please enter a user ID')
      return
    }

    setIsProcessing(true)

    try {
      const status = await checkCopyPastePermission(userId.trim())
      setPermissionStatus(status)
      setResult({
        success: true,
        message: 'Status checked successfully',
        action: 'status_checked'
      })
    } catch (error: any) {
      setResult({
        success: false,
        message: `Error: ${error?.message || 'Unknown error'}`,
        action: 'status_failed'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-purple-700">
      <div className="max-w-2xl mx-auto">
        <div className="glass-card p-6">
          <h1 className="text-2xl font-bold text-white mb-6">Test Copy-Paste Admin Functions</h1>
          
          <div className="space-y-4">
            <div>
              <label className="block text-white mb-2">User ID:</label>
              <input
                type="text"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                className="w-full p-3 rounded-lg bg-white/10 text-white border border-white/20"
                placeholder="Enter user ID"
              />
            </div>

            <div>
              <label className="block text-white mb-2">Days to Grant:</label>
              <input
                type="number"
                value={days}
                onChange={(e) => setDays(parseInt(e.target.value) || 1)}
                min="1"
                max="365"
                className="w-full p-3 rounded-lg bg-white/10 text-white border border-white/20"
              />
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleGrantPermission}
                disabled={isProcessing}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50"
              >
                {isProcessing ? 'Processing...' : 'Grant Permission'}
              </button>

              <button
                onClick={handleRemovePermission}
                disabled={isProcessing}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50"
              >
                {isProcessing ? 'Processing...' : 'Remove Permission'}
              </button>

              <button
                onClick={handleCheckStatus}
                disabled={isProcessing}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold disabled:opacity-50"
              >
                {isProcessing ? 'Checking...' : 'Check Status'}
              </button>
            </div>

            {permissionStatus && (
              <div className="p-4 bg-white/10 rounded-lg">
                <h3 className="text-white font-bold mb-2">Current Permission Status:</h3>
                <div className="text-white space-y-1">
                  <p><strong>Has Permission:</strong> {permissionStatus.hasPermission ? 'Yes' : 'No'}</p>
                  <p><strong>Days Remaining:</strong> {permissionStatus.daysRemaining}</p>
                  {permissionStatus.expiryDate && (
                    <p><strong>Expires:</strong> {new Date(permissionStatus.expiryDate).toLocaleDateString()}</p>
                  )}
                </div>
              </div>
            )}

            {result && (
              <div className={`p-4 rounded-lg ${result.success ? 'bg-green-500/20 border border-green-500/30' : 'bg-red-500/20 border border-red-500/30'}`}>
                <h3 className={`font-bold ${result.success ? 'text-green-400' : 'text-red-400'}`}>
                  {result.success ? 'Success!' : 'Failed'}
                </h3>
                <p className="text-white mt-2">{result.message}</p>
                <p className="text-white/70 text-sm mt-1">Action: {result.action}</p>
              </div>
            )}
          </div>

          <div className="mt-8 p-4 bg-white/5 rounded-lg">
            <h3 className="text-white font-bold mb-2">Instructions:</h3>
            <ul className="text-white/80 text-sm space-y-1">
              <li>• Enter a valid user ID</li>
              <li>• Use "Grant Permission" to enable copy-paste</li>
              <li>• Use "Remove Permission" to disable copy-paste</li>
              <li>• Use "Check Status" to see current permission state</li>
              <li>• Check browser console for detailed logs</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
